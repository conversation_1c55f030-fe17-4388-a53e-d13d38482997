<template>
  <view class="register-container">
    <!-- 标题栏 -->
    <view class="header">
      <view class="back-btn" @click="handleBlack">
        <text class="back-icon">←</text>
        <text class="back-text">免费注册</text>
      </view>
    </view>

    <!-- 注册步骤 -->
    <view class="register-steps">
      <!-- 步骤1: 获取手机号 -->
      <view class="step-item">
        <view class="step-number">1</view>
        <view class="step-content">
          <up-button class="step-btn" @click="handleGetPhone"
                     :customStyle="{backgroundColor: '#3281c6', color: '#ffffff', fontSize: '16px', height: '45px', borderRadius: '8px'}">
            获取手机号
          </up-button>
          <view class="phone-display" v-if="formData.account">{{ formData.account }}</view>
        </view>
      </view>

      <!-- 步骤2: 阅读协议 -->
      <view class="step-item">
        <view class="step-number">2</view>
        <view class="step-content">
          <view class="agreement-section">
            <up-checkbox label="阅读并同意" name="agree" usedAlone v-model:checked="aloneChecked"
                         shape="circle" size="15" :customStyle="{fontWeight: 'normal'}">
            </up-checkbox>
            <text @click="handleUserAgrement" style="color: #3281c6;">《用户协议》、</text>
            <text @click="handlePrivacy" style="color: #3281c6;">《隐私政策》</text>
          </view>
        </view>
      </view>

      <!-- 步骤3: 申请账号 -->
      <view class="step-item">
        <view class="step-number">3</view>
        <view class="step-content">
          <up-button class="step-btn" @click="handleApplyAccount"
                     :customStyle="{backgroundColor: '#3281c6', color: '#ffffff', fontSize: '16px', height: '45px', borderRadius: '8px'}"
                     :disabled="!aloneChecked || !formData.account">
            申请账号
          </up-button>
          <view class="account-info" v-if="accountApplied">
            <view class="info-text">账号申请成功！</view>
            <view class="info-text">账号：{{ formData.account }}</view>
            <view class="info-text">初始密码：jishu123</view>
          </view>
        </view>
      </view>

      <!-- 登录系统按钮 -->
      <view class="login-system-btn">
        <up-button @click="handleLoginSystem"
                   :customStyle="{backgroundColor: '#3281c6', color: '#ffffff', fontSize: '16px', height: '45px', borderRadius: '8px', width: '100%'}"
                   :disabled="!accountApplied">
          登录系统
        </up-button>
      </view>
    </view>

    <Verify @success="handleSubmit" :mode="'pop'" :captchaType="'blockPuzzle'" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
  </view>
</template>

<script setup>
import modal from '@/plugins/modal'
import {
  getCurrentInstance,
  onMounted,
  ref
} from "vue";
import config from '@/config.js'
import Verify from "./components/verifition/Verify.vue"
import {onLoad} from "@dcloudio/uni-app";


const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const verify = ref(null)

const aloneChecked = ref(false)
const globalConfig = ref(config);

// 账号是否已申请成功
const accountApplied = ref(false)

// 小程序版本号
const appVersion = ref('')

// 表单数据
const formData = ref({
  account: undefined,
  password: 'jishu123', // 默认初始密码
})

// 返回事件
const handleBlack = () => {
  uni.navigateBack()
}

// 获取手机号
const handleGetPhone = () => {
  uni.getPhoneNumber({
    success: (res) => {
      console.log('获取手机号成功', res)
      if (res.phoneNumber) {
        formData.value.account = res.phoneNumber
        modal.msgSuccess('手机号获取成功')
      }
    },
    fail: (err) => {
      console.log('获取手机号失败', err)
      // 模拟获取手机号成功（用于测试）
      formData.value.account = '***********'
      modal.msgSuccess('手机号获取成功')
    }
  })
}

// 申请账号
const handleApplyAccount = async () => {
  if (!formData.value.account) {
    modal.msgError('请先获取手机号')
    return
  }
  if (!aloneChecked.value) {
    modal.msgError('请先阅读并同意用户协议和隐私政策')
    return
  }

  // 模拟申请账号成功
  accountApplied.value = true
  modal.msgSuccess('账号申请成功！')
}

// 登录系统
const handleLoginSystem = () => {
  if (!accountApplied.value) {
    modal.msgError('请先申请账号')
    return
  }

  // 保存登录信息并跳转到登录页
  const loginFormData = {
    account: formData.value.account,
    password: '',
    isRemember: false,
    socialCode: undefined
  }
  uni.setStorageSync("loginFormData", loginFormData)

  modal.msgSuccess('即将跳转到登录页面')
  setTimeout(() => {
    uni.navigateBack()
  }, 1000)
}
// 确定事件（保留用于验证码组件）
const handleSubmit = async () => {
  // 这里可以添加实际的注册逻辑
  console.log('验证码验证成功')
}

// 隐私协议
function handlePrivacy() {
  /* let site = globalConfig.value.appInfo.agreements[0];
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
  }); */
  uni.navigateTo({
    url: `/page_word/userPrivacy`
  });
};
// 用户协议
function handleUserAgrement() {
  /* let site = globalConfig.value.appInfo.agreements[1]
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
  }); */
  uni.navigateTo({
    url: `/page_word/userService`
  });
};

onLoad((option)=>{
  console.log('注册onLoad', option)
})

onMounted(() => {
  console.log('注册onMounted')
  eventChannel.on('parentParam', function(data) {
    console.log('注册onMounted', data)
    formData.value.account = data.account
    appVersion.value = data.appVersion
  })
})

</script>

<style lang="scss">
page {
  background-color: #f5f5f5;
}

.register-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0 20px;

  .header {
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;
    background-color: #ffffff;
    margin: 0 -20px 20px -20px;
    padding-left: 20px;

    .back-btn {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 16px;

      .back-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .back-text {
        font-weight: 500;
      }
    }
  }

  .register-steps {
    .step-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30px;
      background-color: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      .step-number {
        width: 30px;
        height: 30px;
        background-color: #3281c6;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
      }

      .step-content {
        flex: 1;

        .step-btn {
          width: 100%;
          margin-bottom: 10px;
        }

        .phone-display {
          color: #666;
          font-size: 16px;
          margin-top: 10px;
        }

        .agreement-section {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 5px;
        }

        .account-info {
          margin-top: 15px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 6px;
          border-left: 4px solid #3281c6;

          .info-text {
            color: #333;
            font-size: 14px;
            margin-bottom: 5px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .login-system-btn {
      margin-top: 30px;
      padding: 0 20px;
    }
  }
}

// 按钮样式覆盖
:deep(.u-button) {
  border: none !important;
}

:deep(.u-button--disabled) {
  opacity: 0.5 !important;
}
</style>