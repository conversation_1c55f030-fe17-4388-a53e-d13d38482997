<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <image style="width: 300rpx;height: 300rpx;" :src="globalConfig.appInfo.logo" mode="scaleToFill"></image>
    </view>
    <view class="logo-content align-center justify-center flex" style="padding-top: 25rpx;">
      <text class="title" style="color: #291670;">发货更省心！</text>
    </view>
    <view style="padding: 10rpx 80rpx">
      <up-line color="#7B71A4" :hairline="false"></up-line>
    </view>
    <view class="login-form-content">
      <view class="input-item align-center">
        <uni-easyinput type="idcard" v-model="formData.account" placeholder="请输入手机号"
                       :inputBorder="false" placeholderStyle="font-size: 15px"
                       @change="validatorAccount"
        ></uni-easyinput>
        <up-line color="#e7e7e7" :hairline="false"></up-line>
        <view class="tip-view" v-if="showAccountTip">{{ tip.account }}</view>
      </view>
      <view class="input-item align-center">
        <uni-easyinput type="password" v-model="formData.password" placeholder="请输入登录密码"
                       :inputBorder="false" placeholderStyle="font-size: 15px"
                       @change="validatorPasswordNew('password', $event)"
        ></uni-easyinput>
        <up-line color="#e7e7e7" :hairline="false"></up-line>
        <view class="tip-view" v-if="showPwdTip">{{ tip.password }}</view>
      </view>
      <view class="input-item align-center">
        <uni-easyinput type="password" v-model="formData.enterPassword" placeholder="再次输入登录密码"
                       :inputBorder="false" placeholderStyle="font-size: 15px"
                       @change="validatorEnterPasswordNew"
        ></uni-easyinput>
        <up-line color="#e7e7e7" :hairline="false"></up-line>
        <view class="tip-view" v-if="showEnterPwdTip">{{ tip.enterPassword }}</view>
      </view>

      <view class="action-btn" style="padding-top: 30rpx;">
        <up-button type="primary" :loading="loginLoading" loadingText="注册中" @click="handleRegister" shape="circle"
                   :customStyle="loginBtn" :disabled="!aloneChecked">注册</up-button>
      </view>
    </view>

    <view class="xieyi">
      <up-checkbox label="我已阅读并同意" name="agree" usedAlone v-model:checked="aloneChecked"
                   shape="circle" size="15">
      </up-checkbox>
      <text @click="handleUserAgrement" style="color: #02a7f0;">《用户协议》、</text>
      <text @click="handlePrivacy" style="color: #02a7f0;">《隐私政策》</text>
    </view>
    <view class="text-center" style="margin-top: 4px;font-size: 15px;">
<!--      <text style="color: #606266;">版本号：{{ appVersion }}</text>-->
    </view>

    <view class="custom-footer">
      <view style="text-align: center;padding: 0 0 10rpx 0;">
        多多物流超市加盟热线:&nbsp;
        <span style="color: #02a7f0;text-decoration: underline;"
              @click="makeCall({type:'phone',value:'17786356789'})">
          17786356789
        </span>
      </view>
      <up-line color="#000000" :hairline="false"></up-line>
      <view style="text-align: center;padding: 10rpx 0 20rpx 0;">
        行业变革势不可挡，携手大驰抢占先机
      </view>
    </view>

    <Verify @success="handleSubmit" :mode="'pop'" :captchaType="'blockPuzzle'" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>

  </view>
  <!--  <view class="login-bottom"><view class="login-bottom-version">版本号：{{ appVersion }}</view></view>-->
</template>

<script setup>
import modal from '@/plugins/modal'
import {
  getCurrentInstance,
  onMounted,
  reactive,
  ref
} from "vue";
import config from '@/config.js'
import Verify from "./components/verifition/Verify.vue"
import { getSysConf } from "@/api/wljh/home"
import {onLoad} from "@dcloudio/uni-app";
import {registerNetwork} from "@/page_mine/api/sub_mine";
import {handleLogout} from "@/api/wljh/mine";


const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const verify = ref(null)

const aloneChecked = ref(false)
const globalConfig = ref(config);

const loginLoading = ref(false) // 登录加载状态
const loginBtn = reactive({ // 登录按钮样式
  marginTop: '10px',
  height: '35px'
})

// 小程序版本号
const appVersion = ref('')

// 系统是否开启小程序登录验证码
const verifyConf = ref('0')

// 点击手机号拨打电话
const makeCall = (phoneContent) => {
  if (phoneContent.type === 'phone') {
    console.log('拨打电话', phoneContent.value)
    uni.makePhoneCall({
      phoneNumber: phoneContent.value
    })
  }
}

// 密码强度
const pwdStrength = ref(0)
const pwdColor = ref('red')

const formRef = ref(null)
const formData = ref({ // 表单数据
  networkId: undefined,
  nickname: undefined,
  account: undefined,
  password: undefined,
  enterPassword: undefined
})
const showOldPwdTip = ref(false)
const showAccountTip = ref(false)
const showPwdTip = ref(false)
const showEnterPwdTip = ref(false)
const tip = ref({
  account: undefined,
  oldPassword: undefined,
  password: undefined,
  enterPassword: undefined,
})
/*const rules = ref({ // 自定义校验
  oldPassword: {
    validator: validatorPassword,
    trigger: ['blur', 'change']
  },
  password: {
    validator: validatorPassword,
    trigger: ['blur', 'change']
  },
  enterPassword: {
    validator: validatorEnterPassword,
    trigger: ['blur', 'change']
  }
})*/

// 返回事件
const handleBlack = () => {
  uni.navigateBack()
}

// 密码强度校验
const checkPasswordStrength = (password) => {
  // 这里简单示例密码强度的计算，实际情况需要更复杂的逻辑
  const minLength = 6;
  if (!password) {
    pwdStrength.value = 0;
  } else if (password.length < minLength) {
    pwdColor.value='red'
    pwdStrength.value = 20
  } else if (password.length === minLength) {
    pwdColor.value='red'
    pwdStrength.value = 40
  } else if (password.length <= minLength + 3) {
    pwdColor.value='orange'
    pwdStrength.value = 60
  } else if (password.length <= minLength + 5) {
    pwdColor.value='green'
    pwdStrength.value = 80
  } else {
    pwdColor.value='green'
    pwdStrength.value = 100
  }
}
// 点击注册
const handleRegister = async () => {
  if (!validateForm()) {
    return
  }
  verify.value.show()
}
// 确定事件
const handleSubmit = async () => {
  let data = {...formData.value}
  await registerNetwork(data).then(res => {
    if (res.code === 0) {
      modal.msgSuccess('注册成功！')
      verify.value.hide()
      setTimeout(() => {
        const loginFormData = { //登录参数
          account: formData.value.account,
          password: undefined,
          isRemember: undefined,
          socialCode: undefined
        }
        uni.setStorageSync("loginFormData", loginFormData)
        // 跳转登录页
        handleLogout()
      }, 1000)
    }
  }).catch(() => {
    verify.value.hide()
  })
}
// 校验表单
const validateForm = () => {
  let validAccount = validatorAccount(formData.value.account)
  let validPassword = validatorPasswordNew('password', formData.value.password)
  let validEnterPassword = validatorEnterPasswordNew(formData.value.enterPassword)
  if (!(validAccount && validPassword && validEnterPassword)) {
    return false
  }
  return true
}

// 校验手机号
function validatorAccount(value) {
  if (!value) {
    tip.value.account = '请输入手机号'
    showAccountTip.value = true
    return false
  } else if (!/^1[3-9]\d{9}$/.test(value)) {
    // 手机号格式校验
    tip.value.account = '请输入正确的手机号'
    showAccountTip.value = true
    return false
  } else {
    tip.value.account = ''
    showAccountTip.value = false
    return true
  }
}

// 检验密码New
function validatorPasswordNew(type, value) {
  const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/
  if (type === 'password') {
    // checkPasswordStrength(value)
    if (!value) {
      tip.value.password = '请输入登录密码'
      showPwdTip.value = true
      return false
    } else if (!regex.test(value)){
      tip.value.password = '密码中必须包含字母数字至少6~20个字符'
      showPwdTip.value = true
      return false
    } else {
      tip.value.password = ''
      showPwdTip.value = false
      return true
    }
  }
}
// 检验二次密码New
function validatorEnterPasswordNew(value) {
  if (!value) {
    tip.value.enterPassword = '请再次输入登录密码'
    showEnterPwdTip.value = true
    return false
  } else if (value !== formData.value.password) {
    tip.value.enterPassword = '两次密码不一致'
    showEnterPwdTip.value = true
    return false
  } else {
    tip.value.enterPassword = ''
    showEnterPwdTip.value = false
    return true
  }
}
const networkInfo = ref({}) // 网点信息

// 隐私协议
function handlePrivacy() {
  /* let site = globalConfig.value.appInfo.agreements[0];
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
  }); */
  uni.navigateTo({
    url: `/page_word/userPrivacy`
  });
};
// 用户协议
function handleUserAgrement() {
  /* let site = globalConfig.value.appInfo.agreements[1]
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
  }); */
  uni.navigateTo({
    url: `/page_word/userService`
  });
};

onLoad((option)=>{
  console.log('注册onLoad', option)
})

onMounted(() => {
  console.log('注册onMounted')
  eventChannel.on('parentParam', function(data) {
    console.log('注册onMounted', data)
    formData.value.account = data.account
    appVersion.value = data.appVersion
  })
})

</script>

<style lang="scss">
.tip-view{
  color: #e50d0d;
  text-align: right;
  font-size: 25rpx;
}
page {
  background-color: #ffffff;
}

.normal-login-container {
  width: 100%;

  .logo-content {
    width: 100%;
    font-size: 18px;
    text-align: center;
    //padding-top: 20rpx;

    image {
      border-radius: 4px;
    }

    .title {
      margin-left: 10px;
    }
  }

  .login-form-content {
    text-align: center;
    margin: 10px auto;
    //margin-top: 0rpx;
    width: 80%;

    .input-item {
      margin: 20px 0 0 0;
      //background-color: #f5f6f7;
      height: 45px;
      border-radius: 20px;

      .icon {
        font-size: 38rpx;
        margin-left: 10px;
        color: #999;
      }

      .input {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        padding-left: 15px;
      }

    }

    .login-btn {
      margin-top: 10px;
      height: 45px;
    }

    .login-code {
      height: 38px;
      float: right;

      .login-code-img {
        height: 38px;
        position: absolute;
        margin-left: 10px;
        width: 200rpx;
      }
    }

    .action-btn {

      :deep(.u-button--circle) {
        border-radius: 5px;
      }

      :deep(.u-button--primary) {
        background-color: #d81e06;
        border-color: #d81e06;
      }
    }

    :deep(.u-input--radius, .u-input--square) {
      border-radius: 20px;
      height: 45px;
      border: 0;
    }
  }
}
.login-bottom {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: right;

  .login-bottom-version {
    padding: 0rpx 20rpx;
    color: #606266;
  }
}
.uni-easyinput {
  :deep(.uni-easyinput__content-input) {
    padding-left: 0!important;
  }
}

.xieyi {
  color: #333;
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-footer {
  height: 150rpx;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 80rpx;
  //display: flex;
  gap: 60rpx;
  font-size: 17px;
  //justify-content: center;
  //align-items: center;

  :deep(.u-button) {
    height: 70rpx;
  }

  :deep(.u-button--error){
    background-color: #d81e06;
  }

  :deep(.u-button--plain.u-button--info){
    color:#000;
  }
}
</style>