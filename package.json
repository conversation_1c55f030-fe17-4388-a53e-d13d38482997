{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app:dev": "uni build -p app --mode dev", "build:app:prod": "uni build -p app --mode prod", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "clean:linux": "rm -rf dist || rm -rf node_modules", "clean:windows": "rd /s /q dist || rd /s /q node_modules"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4000820240401001", "@dcloudio/uni-app-plus": "3.0.0-4000820240401001", "@dcloudio/uni-components": "3.0.0-4000820240401001", "@dcloudio/uni-h5": "3.0.0-4000820240401001", "@dcloudio/uni-mp-alipay": "3.0.0-4000820240401001", "@dcloudio/uni-mp-baidu": "3.0.0-4000820240401001", "@dcloudio/uni-mp-jd": "3.0.0-4000820240401001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4000820240401001", "@dcloudio/uni-mp-lark": "3.0.0-4000820240401001", "@dcloudio/uni-mp-qq": "3.0.0-4000820240401001", "@dcloudio/uni-mp-toutiao": "3.0.0-4000820240401001", "@dcloudio/uni-mp-weixin": "3.0.0-4000820240401001", "@dcloudio/uni-mp-xhs": "3.0.0-4000820240401001", "@dcloudio/uni-quickapp-webview": "3.0.0-4000820240401001", "@esbuild/darwin-arm64": "^0.17.19", "@esbuild/darwin-x64": "^0.17.19", "@jridgewell/sourcemap-codec": "^1.4.15", "@qiun/wx-ucharts": "2.5.0-20230101", "@ttou/uview-typings": "^2.0.5", "@uni-ui/code-plugs": "^1.9.6", "@uni-ui/code-ui": "^1.5.3", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "mqtt": "4.1.0", "net": "^1.0.2", "pinia": "2.0.17", "text-decoding": "^1.0.0", "tslib": "^2.6.2", "uview-plus": "^3.1.45", "vue": "3.4.23", "vue-i18n": "^9.10.2", "ws": "^8.18.0"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4000820240401001", "@dcloudio/uni-cli-shared": "3.0.0-4000820240401001", "@dcloudio/uni-stacktracey": "3.0.0-4000820240401001", "@dcloudio/vite-plugin-uni": "3.0.0-4000820240401001", "@vue/runtime-core": "^3.3.11", "@vue/tsconfig": "^0.1.3", "less": "^4.2.0", "sass": "~1.66.1", "sass-loader": "^10.4.1", "typescript": "^4.9.5", "vite": "4.3.5", "vue-tsc": "^1.8.8"}}