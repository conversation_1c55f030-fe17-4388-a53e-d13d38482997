import request from '@/utils/request'

// 获得字典数据信息
export const getDictDataInfoAll = (params : any) => {
	return request({
		url: '/system/dict-data/type',
		method: 'GET',
		params: params
	})
}

// 获得订单信息分页
export const getOrderInfoPageClient = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"list": [
				{
					"id": 260,
					"orderCode": "D25032500001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendAddressOne": null,
					"sendAddressTwo": null,
					"sendAddressThree": null,
					"sendAddressFour": null,
					"sendAddressFive": null,
					"collectAddressOne": null,
					"collectAddressTwo": null,
					"collectAddressThree": null,
					"collectAddressFour": null,
					"collectAddressFive": null,
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"orderStatus": "1",
					"logisticsStatus": "0",
					"orderTransportStatus": "网点入库",
					"orderStartTime": *************,
					"orderEndTime": null,
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"logisticsStatusData": null,
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"collectionDelivery": 0.0,
					"collectionName": null,
					"collectionBankName": null,
					"collectionCardNum": null,
					"amountFreight": 309.00,
					"totalAmount": 320.00,
					"deptId": null,
					"remark": "测试app到站4444",
					"deliveryAmount": 0.00,
					"insuredAmount": 0.00,
					"printCount": 0,
					"lastPrintTime": null,
					"receiptType": "1",
					"receiptAmount": 0.00,
					"releaseAmount": 0.00,
					"insuredPayAmount": 0.00,
					"brokerageAmount": 10.00,
					"brokerageBackType": "1",
					"otherAmount": 0.00,
					"discountAmount": 0.00,
					"collectionFreightAmount": 0.00,
					"collectionPayAmount": 0.00,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"collectionProcRate": 3.00,
					"taxAmount": 0.00,
					"whetherInvoice": "1",
					"invoiceStatus": "0",
					"discDeptId": "000036",
					"discDeptName": "安阳",
					"destDeptName": "",
					"goodsShowLabel": "食品饮料，1.0kg，1.0m³",
					"waybillCode": null,
					"subWaybillList": null
				},
				{
					"id": 259,
					"orderCode": "D25032400001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendAddressOne": null,
					"sendAddressTwo": null,
					"sendAddressThree": null,
					"sendAddressFour": null,
					"sendAddressFive": null,
					"collectAddressOne": null,
					"collectAddressTwo": null,
					"collectAddressThree": null,
					"collectAddressFour": null,
					"collectAddressFive": null,
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"orderStatus": "1",
					"logisticsStatus": "0",
					"orderTransportStatus": "网点入库",
					"orderStartTime": *************,
					"orderEndTime": null,
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"logisticsStatusData": null,
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"collectionDelivery": 0.0,
					"collectionName": null,
					"collectionBankName": null,
					"collectionCardNum": null,
					"amountFreight": 309.00,
					"totalAmount": 320.00,
					"deptId": 121,
					"remark": "测试app到站4444",
					"deliveryAmount": 0.00,
					"insuredAmount": 0.00,
					"printCount": 0,
					"lastPrintTime": null,
					"receiptType": "1",
					"receiptAmount": 0.00,
					"releaseAmount": 0.00,
					"insuredPayAmount": 0.00,
					"brokerageAmount": 10.00,
					"brokerageBackType": "1",
					"otherAmount": 0.00,
					"discountAmount": 0.00,
					"collectionFreightAmount": 0.00,
					"collectionPayAmount": 1.00,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"collectionProcRate": 3.00,
					"taxAmount": 0.00,
					"whetherInvoice": "1",
					"invoiceStatus": "0",
					"discDeptId": "000036",
					"discDeptName": "安阳",
					"destDeptName": "",
					"goodsShowLabel": "食品饮料，1.0kg，1.0m³",
					"waybillCode": null,
					"subWaybillList": null
				},
				{
					"id": 258,
					"orderCode": "D25032000002",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendAddressOne": null,
					"sendAddressTwo": null,
					"sendAddressThree": null,
					"sendAddressFour": null,
					"sendAddressFive": null,
					"collectAddressOne": null,
					"collectAddressTwo": null,
					"collectAddressThree": null,
					"collectAddressFour": null,
					"collectAddressFive": null,
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"orderStatus": "1",
					"logisticsStatus": "0",
					"orderTransportStatus": "网点入库",
					"orderStartTime": *************,
					"orderEndTime": null,
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"logisticsStatusData": null,
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"collectionDelivery": 0.0,
					"collectionName": null,
					"collectionBankName": null,
					"collectionCardNum": null,
					"amountFreight": 309.00,
					"totalAmount": 320.00,
					"deptId": null,
					"remark": "测试app到站4444",
					"deliveryAmount": 0.00,
					"insuredAmount": 0.00,
					"printCount": 0,
					"lastPrintTime": null,
					"receiptType": "1",
					"receiptAmount": 0.00,
					"releaseAmount": 0.00,
					"insuredPayAmount": 0.00,
					"brokerageAmount": 10.00,
					"brokerageBackType": "1",
					"otherAmount": 0.00,
					"discountAmount": 0.00,
					"collectionFreightAmount": 0.00,
					"collectionPayAmount": 0.00,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"collectionProcRate": 3.00,
					"taxAmount": 0.00,
					"whetherInvoice": "1",
					"invoiceStatus": "0",
					"discDeptId": "000036",
					"discDeptName": "安阳",
					"destDeptName": "",
					"goodsShowLabel": "食品饮料，1.0kg，1.0m³",
					"waybillCode": null,
					"subWaybillList": null
				},
				{
					"id": 257,
					"orderCode": "D25032000001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendAddressOne": null,
					"sendAddressTwo": null,
					"sendAddressThree": null,
					"sendAddressFour": null,
					"sendAddressFive": null,
					"collectAddressOne": null,
					"collectAddressTwo": null,
					"collectAddressThree": null,
					"collectAddressFour": null,
					"collectAddressFive": null,
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_COLLECT_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"orderStatus": "1",
					"logisticsStatus": "0",
					"orderTransportStatus": "网点入库",
					"orderStartTime": *************,
					"orderEndTime": null,
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"logisticsStatusData": null,
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"collectionDelivery": 0.0,
					"collectionName": null,
					"collectionBankName": null,
					"collectionCardNum": null,
					"amountFreight": 309.00,
					"totalAmount": 320.00,
					"deptId": 121,
					"remark": "测试app到站4444",
					"deliveryAmount": 0.00,
					"insuredAmount": 0.00,
					"printCount": 0,
					"lastPrintTime": null,
					"receiptType": "1",
					"receiptAmount": 0.00,
					"releaseAmount": 0.00,
					"insuredPayAmount": 0.00,
					"brokerageAmount": 10.00,
					"brokerageBackType": "2",
					"otherAmount": 0.00,
					"discountAmount": 0.00,
					"collectionFreightAmount": 0.00,
					"collectionPayAmount": 1.00,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"collectionProcRate": 3.00,
					"taxAmount": 0.00,
					"whetherInvoice": "1",
					"invoiceStatus": "0",
					"discDeptId": "000036",
					"discDeptName": "安阳",
					"destDeptName": "",
					"goodsShowLabel": "食品饮料，1.0kg，1.0m³",
					"waybillCode": null,
					"subWaybillList": null
				},
				{
					"id": 256,
					"orderCode": "D25031900006",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendAddressOne": null,
					"sendAddressTwo": null,
					"sendAddressThree": null,
					"sendAddressFour": null,
					"sendAddressFive": null,
					"collectAddressOne": null,
					"collectAddressTwo": null,
					"collectAddressThree": null,
					"collectAddressFour": null,
					"collectAddressFive": null,
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_COLLECT_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"orderStatus": "1",
					"logisticsStatus": "0",
					"orderTransportStatus": "网点入库",
					"orderStartTime": *************,
					"orderEndTime": null,
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"logisticsStatusData": null,
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"collectionDelivery": 0.0,
					"collectionName": null,
					"collectionBankName": null,
					"collectionCardNum": null,
					"amountFreight": 309.00,
					"totalAmount": 320.00,
					"deptId": 121,
					"remark": "测试app到站4444",
					"deliveryAmount": 0.00,
					"insuredAmount": 0.00,
					"printCount": 0,
					"lastPrintTime": null,
					"receiptType": "1",
					"receiptAmount": 0.00,
					"releaseAmount": 0.00,
					"insuredPayAmount": 0.00,
					"brokerageAmount": 10.00,
					"brokerageBackType": "2",
					"otherAmount": 0.00,
					"discountAmount": 0.00,
					"collectionFreightAmount": 0.00,
					"collectionPayAmount": 1.00,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"collectionProcRate": 3.00,
					"taxAmount": 0.00,
					"whetherInvoice": "1",
					"invoiceStatus": "0",
					"discDeptId": "000036",
					"discDeptName": "安阳",
					"destDeptName": "",
					"goodsShowLabel": "食品饮料，1.0kg，1.0m³",
					"waybillCode": null,
					"subWaybillList": null
				}
			],
			"total": 5
		},
		"msg": ""
	})})
}

// 删除订单信息
export const deleteOrderInfo = (id : number) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 编辑订单信息
export const updateOrderInfo = (data : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}


// 获得运单信息
export const getWaybillInfoApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"list": [
				{
					"id": 258,
					"waybillCode": "*********",
					"orderCode": "D25032500001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"logisticsStatus": "0",
					"waybillCreateTime": 1742885385000,
					"waybillStartTime": null,
					"waybillEndTime": null,
					"signTime": null,
					"discDeptId": "000036",
					"orderStatus": "网点入库",
					"orderStatusDesc": "网点入库",
					"codStatus": "无代收",
					"profitStatus": "未结算",
					"lostStatus": "未挂失",
					"orderTransferStatus": "未转",
					"amountFreight": "309.00",
					"totalAmount": "320.0",
					"getStatus": "0",
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"packMethodLabel": "纸",
					"collectionDelivery": 0.0,
					"remark": "测试app到站4444",
					"printCount": 0,
					"lastPrintTime": null,
					"deductStatus": "1",
					"networkCalcStatus": "0",
					"companyCalcStatus": "0",
					"networkCalcTime": null,
					"companyCalcTime": null,
					"transferFreezeStatus": "1",
					"billDeptName": "蚌埠1",
					"discDeptName": "安阳",
					"destDeptName": "",
					"thirdQrcode": null,
					"orderInfo": null,
					"subWaybillList": [
						{
							"createTime": 1742891232000,
							"updateTime": 1742891232000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": ********,
							"id": 1445,
							"waybillCode": "*********",
							"subWaybillCode": "*********001",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 1
						},
						{
							"createTime": 1742891232000,
							"updateTime": 1742891232000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": ********,
							"id": 1446,
							"waybillCode": "*********",
							"subWaybillCode": "*********002",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 2
						}
					]
				},
				{
					"id": 257,
					"waybillCode": "*********",
					"orderCode": "D25032400001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"logisticsStatus": "0",
					"waybillCreateTime": 1742777296000,
					"waybillStartTime": null,
					"waybillEndTime": null,
					"signTime": null,
					"discDeptId": "000036",
					"orderStatus": "网点入库",
					"orderStatusDesc": "网点入库",
					"codStatus": "无代收",
					"profitStatus": "未结算",
					"lostStatus": "未挂失",
					"orderTransferStatus": "未转",
					"amountFreight": "309.00",
					"totalAmount": "320.0",
					"getStatus": "0",
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"packMethodLabel": "纸",
					"collectionDelivery": 0.0,
					"remark": "测试app到站4444",
					"printCount": 0,
					"lastPrintTime": null,
					"deductStatus": "1",
					"networkCalcStatus": "0",
					"companyCalcStatus": "0",
					"networkCalcTime": null,
					"companyCalcTime": null,
					"transferFreezeStatus": "1",
					"billDeptName": "蚌埠1",
					"discDeptName": "安阳",
					"destDeptName": "",
					"thirdQrcode": null,
					"orderInfo": null,
					"subWaybillList": [
						{
							"createTime": 1742891144000,
							"updateTime": 1742891144000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": ********,
							"id": 1443,
							"waybillCode": "*********",
							"subWaybillCode": "*********001",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 1
						},
						{
							"createTime": 1742891144000,
							"updateTime": 1742891144000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": ********,
							"id": 1444,
							"waybillCode": "*********",
							"subWaybillCode": "*********002",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 2
						}
					]
				},
				{
					"id": 256,
					"waybillCode": "*********",
					"orderCode": "D25032000002",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"logisticsStatus": "0",
					"waybillCreateTime": 1742456809000,
					"waybillStartTime": null,
					"waybillEndTime": null,
					"signTime": null,
					"discDeptId": "000036",
					"orderStatus": "网点入库",
					"orderStatusDesc": "网点入库",
					"codStatus": "无代收",
					"profitStatus": "未结算",
					"lostStatus": "未挂失",
					"orderTransferStatus": "未转",
					"amountFreight": "309.00",
					"totalAmount": "320.0",
					"getStatus": "0",
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"packMethodLabel": "纸",
					"collectionDelivery": 0.0,
					"remark": "测试app到站4444",
					"printCount": 0,
					"lastPrintTime": null,
					"deductStatus": "1",
					"networkCalcStatus": "0",
					"companyCalcStatus": "0",
					"networkCalcTime": null,
					"companyCalcTime": null,
					"transferFreezeStatus": "1",
					"billDeptName": "蚌埠1",
					"discDeptName": "安阳",
					"destDeptName": "",
					"thirdQrcode": null,
					"orderInfo": null,
					"subWaybillList": [
						{
							"createTime": 1742540501000,
							"updateTime": 1742780575000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": 20250321,
							"id": 1390,
							"waybillCode": "*********",
							"subWaybillCode": "*********001",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 1
						},
						{
							"createTime": 1742540501000,
							"updateTime": 1742780575000,
							"creator": "2app",
							"updater": "2app",
							"deleted": false,
							"createDate": 20250321,
							"id": 1391,
							"waybillCode": "*********",
							"subWaybillCode": "*********002",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 2
						}
					]
				},
				{
					"id": 255,
					"waybillCode": "*********",
					"orderCode": "D25032000001",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区花园口",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_COLLECT_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"logisticsStatus": "0",
					"waybillCreateTime": 1742456764000,
					"waybillStartTime": null,
					"waybillEndTime": null,
					"signTime": null,
					"discDeptId": "000036",
					"orderStatus": "网点入库",
					"orderStatusDesc": "网点入库",
					"codStatus": "无代收",
					"profitStatus": "未结算",
					"lostStatus": "未挂失",
					"orderTransferStatus": "未转",
					"amountFreight": "309.00",
					"totalAmount": "320.0",
					"getStatus": "0",
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"packMethodLabel": "纸",
					"collectionDelivery": 0.0,
					"remark": "测试app到站4444",
					"printCount": 0,
					"lastPrintTime": null,
					"deductStatus": "0",
					"networkCalcStatus": "0",
					"companyCalcStatus": "0",
					"networkCalcTime": null,
					"companyCalcTime": null,
					"transferFreezeStatus": "1",
					"billDeptName": "蚌埠1",
					"discDeptName": "安阳",
					"destDeptName": "",
					"thirdQrcode": null,
					"orderInfo": null,
					"subWaybillList": [
						{
							"createTime": 1742456764000,
							"updateTime": 1742780575000,
							"creator": null,
							"updater": null,
							"deleted": false,
							"createDate": 20250320,
							"id": 1386,
							"waybillCode": "*********",
							"subWaybillCode": "*********001",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 1
						},
						{
							"createTime": 1742456764000,
							"updateTime": 1742780575000,
							"creator": null,
							"updater": null,
							"deleted": false,
							"createDate": 20250320,
							"id": 1387,
							"waybillCode": "*********",
							"subWaybillCode": "*********002",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 2
						}
					]
				},
				{
					"id": 254,
					"waybillCode": "*********",
					"orderCode": "D25031900006",
					"collectName": "金酷",
					"collectPhone": "18818260203",
					"collectAddress": "安阳",
					"sendName": "张爱因",
					"sendPhone": "***********",
					"sendIdCard": null,
					"sendAddress": "河南省郑州市二七区",
					"goodsType": "GOODS_TYPE_OTHER",
					"goodsTypeLabel": "食品饮料",
					"totalWeight": 1.0,
					"totalVolume": 1.0,
					"totalNum": 2,
					"goodsLong": null,
					"goodsWidth": null,
					"goodsHeight": null,
					"payMethod": "PAY_METHOD_COLLECT_PAY_SETTLE",
					"shouldPayAmount": 320.0,
					"actualPayAmount": 320.0,
					"logisticsCompaniesId": 1,
					"logisticsNetworkId": 1,
					"logisticsStatus": "0",
					"waybillCreateTime": 1742355149000,
					"waybillStartTime": null,
					"waybillEndTime": null,
					"signTime": null,
					"discDeptId": "000036",
					"orderStatus": "网点入库",
					"orderStatusDesc": "网点入库",
					"codStatus": "无代收",
					"profitStatus": "未结算",
					"lostStatus": "未挂失",
					"orderTransferStatus": "未转",
					"amountFreight": "309.00",
					"totalAmount": "320.0",
					"getStatus": "0",
					"networkName": null,
					"logisticsCompaniesName": "远通物流-测试",
					"isReceipt": "0",
					"deliveryMethod": "1",
					"releaseMethod": "2",
					"packMethod": "2",
					"packMethodLabel": "纸",
					"collectionDelivery": 0.0,
					"remark": "测试app到站4444",
					"printCount": 0,
					"lastPrintTime": null,
					"deductStatus": "1",
					"networkCalcStatus": "6",
					"companyCalcStatus": "6",
					"networkCalcTime": 1742355202000,
					"companyCalcTime": 1742355177000,
					"transferFreezeStatus": "0",
					"billDeptName": "蚌埠1",
					"discDeptName": "安阳",
					"destDeptName": "",
					"thirdQrcode": null,
					"orderInfo": null,
					"subWaybillList": [
						{
							"createTime": 1742871465000,
							"updateTime": 1742871465000,
							"creator": null,
							"updater": null,
							"deleted": false,
							"createDate": ********,
							"id": 1432,
							"waybillCode": "*********",
							"subWaybillCode": "*********001",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 1
						},
						{
							"createTime": 1742871465000,
							"updateTime": 1742871465000,
							"creator": null,
							"updater": null,
							"deleted": false,
							"createDate": ********,
							"id": 1433,
							"waybillCode": "*********",
							"subWaybillCode": "*********002",
							"currentDeptName": "蚌埠1",
							"currentDeptTypeValue": "分公司",
							"currentStatus": "网点入库",
							"sortNum": 2
						}
					]
				}
			],
			"total": 5
		},
		"msg": ""
	})})
}

// 获取打印机品牌信息
export const getByParentId = (parentId : number) => {
	return request({
		url: '/uni/printer-info/getByParentId?parentId=' + parentId,
		method: 'GET'
	})
}

// 同步运单信息
export const reachWaybillInfoApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 批量同步运单信息
export const reachWaybillInfoAppBatch = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 获取运单详情页面数据
export const getWaybillDetailInfoApp = (waybillCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"id": 258,
			"waybillCode": "*********",
			"orderCode": "D25032500001",
			"collectName": "金酷",
			"collectPhone": "18818260203",
			"collectAddress": "安阳",
			"sendName": "张爱因",
			"sendPhone": "***********",
			"sendIdCard": null,
			"sendAddress": "河南省郑州市二七区花园口",
			"goodsType": "GOODS_TYPE_OTHER",
			"goodsTypeLabel": "食品饮料",
			"totalWeight": 1.0,
			"totalVolume": 1.0,
			"totalNum": 2,
			"goodsLong": null,
			"goodsWidth": null,
			"goodsHeight": null,
			"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
			"shouldPayAmount": 320.0,
			"actualPayAmount": 320.0,
			"logisticsCompaniesId": 1,
			"logisticsNetworkId": 1,
			"logisticsStatus": "0",
			"waybillCreateTime": 1742885385000,
			"waybillStartTime": null,
			"waybillEndTime": null,
			"signTime": null,
			"discDeptId": "000036",
			"orderStatus": "网点入库",
			"orderStatusDesc": "网点入库",
			"codStatus": "无代收",
			"profitStatus": "未结算",
			"lostStatus": "未挂失",
			"orderTransferStatus": "未转",
			"amountFreight": "309.00",
			"totalAmount": "320.0",
			"getStatus": "0",
			"networkName": "安阳网点",
			"logisticsCompaniesName": "远通物流-测试",
			"isReceipt": "0",
			"deliveryMethod": "1",
			"releaseMethod": "2",
			"packMethod": "2",
			"packMethodLabel": "纸",
			"collectionDelivery": 0.0,
			"remark": "测试app到站4444",
			"printCount": 0,
			"lastPrintTime": null,
			"deductStatus": "1",
			"networkCalcStatus": "0",
			"companyCalcStatus": "0",
			"networkCalcTime": null,
			"companyCalcTime": null,
			"transferFreezeStatus": "1",
			"billDeptName": "蚌埠1",
			"discDeptName": "安阳",
			"destDeptName": "",
			"thirdQrcode": null,
			"orderInfo": {
				"createTime": 1742885385000,
				"updateTime": 1742885385000,
				"creator": "2app",
				"updater": "2app",
				"deleted": false,
				"id": 260,
				"orderCode": "D25032500001",
				"collectName": "金酷",
				"collectPhone": "18818260203",
				"collectAddress": "安阳",
				"collectAddressOne": null,
				"collectAddressTwo": null,
				"collectAddressThree": null,
				"collectAddressFour": null,
				"collectAddressFive": null,
				"sendName": "张爱因",
				"sendPhone": "***********",
				"sendIdCard": null,
				"sendAddress": "河南省郑州市二七区花园口",
				"sendAddressOne": null,
				"sendAddressTwo": null,
				"sendAddressThree": null,
				"sendAddressFour": null,
				"sendAddressFive": null,
				"goodsType": "GOODS_TYPE_OTHER",
				"goodsTypeLabel": "食品饮料",
				"totalWeight": 1.0,
				"totalVolume": 1.0,
				"totalNum": 2,
				"goodsLong": null,
				"goodsWidth": null,
				"goodsHeight": null,
				"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
				"shouldPayAmount": 320.0,
				"actualPayAmount": 320.0,
				"logisticsCompaniesId": 1,
				"logisticsNetworkId": 1,
				"orderStatus": "1",
				"logisticsStatus": "0",
				"orderTransportStatus": "网点入库",
				"orderStartTime": *************,
				"orderEndTime": null,
				"isReceipt": "0",
				"deliveryMethod": "1",
				"releaseMethod": "2",
				"packMethod": "2",
				"collectionDelivery": 0.0,
				"collectionName": null,
				"collectionBankName": null,
				"collectionCardNum": null,
				"amountFreight": 309.00,
				"totalAmount": 320.00,
				"deptId": null,
				"remark": "测试app到站4444",
				"deliveryAmount": 0.00,
				"insuredAmount": 0.00,
				"printCount": 0,
				"lastPrintTime": null,
				"receiptType": "1",
				"receiptAmount": 0.00,
				"releaseAmount": 0.00,
				"insuredPayAmount": 0.00,
				"brokerageAmount": 10.00,
				"brokerageBackType": "1",
				"otherAmount": 0.00,
				"discountAmount": 0.00,
				"collectionFreightAmount": 0.00,
				"collectionPayAmount": 0.00,
				"amountZdf": 1.00,
				"rateBzf": 1.00,
				"collectionProcRate": 3.00,
				"taxAmount": 0.00,
				"whetherInvoice": "1",
				"invoiceStatus": "0",
				"discDeptId": "000036",
				"discDeptName": "安阳",
				"destDeptName": "",
				"tryOutFlag": "0",
				"createDate": ********
			},
			"subWaybillList": [
				{
					"createTime": *************,
					"updateTime": *************,
					"creator": "2app",
					"updater": "2app",
					"deleted": false,
					"createDate": ********,
					"id": 1436,
					"waybillCode": "*********",
					"subWaybillCode": "*********001",
					"currentDeptName": "蚌埠1",
					"currentDeptTypeValue": "分公司",
					"currentStatus": "网点入库",
					"sortNum": 1
				},
				{
					"createTime": *************,
					"updateTime": *************,
					"creator": "2app",
					"updater": "2app",
					"deleted": false,
					"createDate": ********,
					"id": 1437,
					"waybillCode": "*********",
					"subWaybillCode": "*********002",
					"currentDeptName": "蚌埠1",
					"currentDeptTypeValue": "分公司",
					"currentStatus": "网点入库",
					"sortNum": 2
				}
			]
		},
		"msg": ""
	})})
}
// 获取填充后的运单面单数据
export const getFinalWaybillPrintInfoApp = (waybillCode : string, templateKey : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": printDataTemp,
		"msg": ""
	})})
}
// 获取填充后的运单面单数据-子单列表
export const getFinalSubWaybillPrintInfoApp = (waybillCode : string, templateKey : string, subNum : number) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [printDataTemp, printDataTemp],
		"msg": ""
	})})
}

// 保存运单打印记录
export const saveWayBillPrintRecord = (waybillCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 获取客户单面单数据
export const getOrderPrintInfoApp = (orderCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"orderCode": "D25032500001",
			"orderCode1": "D250325000",
			"orderCode2": "01",
			"collectName": "金酷",
			"collectPhone": "18818260203",
			"collectAddress": "安阳",
			"sendAddressOne": null,
			"sendAddressTwo": null,
			"sendAddressThree": null,
			"sendAddressFour": null,
			"sendAddressFive": null,
			"collectAddressOne": null,
			"collectAddressTwo": null,
			"collectAddressThree": null,
			"collectAddressFour": null,
			"collectAddressFive": null,
			"sendName": "张爱因",
			"sendPhone": "***********",
			"sendIdCard": null,
			"sendAddress": "河南省郑州市二七区花园口",
			"totalWeight": "1.0",
			"totalNum": "2",
			"totalVolume": "1.0",
			"goodsType": "其他",
			"goodsTypeLabel": "食品饮料",
			"logisticsCompaniesId": 1,
			"logisticsCompaniesName": "远通物流-测试",
			"logisticsNetworkId": 1,
			"logisticsNetworkName": "安阳网点",
			"logisticsStatus": "0",
			"receiveNetworkName": "安阳",
			"dateTime01": "2025/03/25 16:35",
			"dateTime02": "2025/03/25",
			"apiChannel": "SN",
			"qrcode": "https://hwscm.com.cn/orderdetail?orderCode=D25032500001",
			"destination": "安阳",
			"packMethodAndNum": "纸/2件",
			"deliveryMethod": "自提",
			"weightAndVolume": "1.0公斤/1.0方",
			"releaseMethod": "",
			"isReceipt": "否",
			"shouldPayAmount": 320.0,
			"actualPayAmount": 320.0,
			"collectionDelivery": 0.0,
			"collectionName": null,
			"collectionBankName": null,
			"collectionCardNum": null,
			"amountFreight": 309.00,
			"totalAmount": 320.00,
			"deliveryAmount": 0.00,
			"insuredAmount": 0.00,
			"payMethod": "现付",
			"payTotal": "0.0元（含代收）",
			"remark": "测试app到站4444",
			"remark1": "测试app到站444",
			"remark2": "4",
			"remark3": null,
			"remark4": null,
			"logisticsNetworkAddress": "河南省郑州市二七区",
			"logisticsNetworkPhone": "***********",
			"printCount": "[1]",
			"nowDate": "2025/03/25",
			"nowTime": "16:35",
			"waybillCode": "*********",
			"billDeptName": "蚌埠1",
			"discDeptName": "安阳"
		},
		"msg": ""
	})})
}

// 示例打印数据
const printDataTemp = [
	"! 0 203 203 800 1",
	"SETBOLD 2",
	"SETMAG 1 1",
	"TEXT 2 0 235 18 客户联",
	"SETBOLD 0",
	"SETMAG 1 1",
	"TEXT 2 0 425 7 2025/03/25",
	"TEXT 2 0 425 28 14:43",
	"TEXT 2 0 500 28 [1]",
	"TEXT 2 0 3 64 单号",
	"TEXT 55 2 110 62 D25032400001",
	"TEXT 2 0 272 64 时间",
	"TEXT 2 0 410 62 2025/03/24",
	"TEXT 2 0 3 103 发站",
	"TEXT 2 0 112 108 蚌埠1",
	"TEXT 2 0 272 108 到站",
	"TEXT 2 0 410 108 安阳",
	"TEXT 2 0 3 195 发货人",
	"TEXT 2 0 112 199 张爱因",
	"TEXT 2 0 272 199 电话",
	"TEXT 2 0 410 199 ***********",
	"B QR 369 521 M 2 U 6",
	"MA,https://hwscm.com.cn/orderdetail?orderCode=D25032400001",
	"ENDQR",
	"TEXT 2 0 30 749 河南省郑州市二七区",
	"TEXT 2 0 363 749 ***********",
	"FORM",
	"PRINT"
]

// 获取填充后的客户单面单数据
export const getFinalOrderPrintInfoApp = (orderCode : string, templateKey : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": printDataTemp,
		"msg": ""
	})})
}

// 保存订单打印记录
export const saveOrderPrintRecord = (orderCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 获取运单轨迹信息
export const getWaybillTrackInfo = (waybillCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [
			{
				"id": 44,
				"waybillCode": "241226005",
				"thirdNodeName": "",
				"nodeType": "delivery",
				"nodeName": "派送中",
				"nodeTime": 1735198892000,
				"nodeContent": "快件交给【梁金山，联系电话15729384345】，正在派送途中（如有任何问题可先联系我，我将尽全力为您解决。您的认可，是我最大的动力！）",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "快件交给【梁金山，联系电话"
					},
					{
						"type": "phone",
						"value": "15729384345"
					},
					{
						"type": "text",
						"value": "】，正在派送途中（如有任何问题可先联系我，我将尽全力为您解决。您的认可，是我最大的动力！）"
					}
				],
				"content": "2024-12-26 15:41:32\n快件交给【梁金山，联系电话15729384345】，正在派送途中（如有任何问题可先联系我，我将尽全力为您解决。您的认可，是我最大的动力！）",
				"showTitle": true
			},
			{
				"id": 43,
				"waybillCode": "241226005",
				"thirdNodeName": "卸货入库",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735177292000,
				"nodeContent": "货物已由【许昌市】【许昌南】卸货入库【联系电话：17837436769】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "货物已由【许昌市】【许昌南】卸货入库【联系电话："
					},
					{
						"type": "phone",
						"value": "17837436769"
					},
					{
						"type": "text",
						"value": "】"
					}
				],
				"content": "2024-12-26 09:41:32\n货物已由【许昌市】【许昌南】卸货入库【联系电话：17837436769】",
				"showTitle": true
			},
			{
				"id": 42,
				"waybillCode": "241226005",
				"thirdNodeName": "分拨发车",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735142905000,
				"nodeContent": "货物已由【郑州分拨中心】随车手机【许昌南】，车牌号【豫WZ1234】驾驶员【杨明】随车手机【18939440910】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "货物已由【郑州分拨中心】随车手机【许昌南】，车牌号【豫WZ1234】驾驶员【杨明】随车手机【"
					},
					{
						"type": "phone",
						"value": "18939440910"
					},
					{
						"type": "text",
						"value": "】"
					}
				],
				"content": "2024-12-26 00:08:25\n货物已由【郑州分拨中心】随车手机【许昌南】，车牌号【豫WZ1234】驾驶员【杨明】随车手机【18939440910】",
				"showTitle": false
			},
			{
				"id": 41,
				"waybillCode": "241226005",
				"thirdNodeName": "货物配载",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735125458000,
				"nodeContent": "【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】"
					}
				],
				"content": "2024-12-25 19:17:38\n【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】",
				"showTitle": false
			},
			{
				"id": 40,
				"waybillCode": "241226005",
				"thirdNodeName": "货物配载",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735125455000,
				"nodeContent": "【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】"
					}
				],
				"content": "2024-12-25 19:17:35\n【郑州分拨中心】装车，装车编号【SN241225000118】下一站网点【许昌南】",
				"showTitle": false
			},
			{
				"id": 39,
				"waybillCode": "241226005",
				"thirdNodeName": "卸货入库",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735100655000,
				"nodeContent": "货物已由【郑州市】【郑州分拨中心】卸货入库【联系电话：】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "货物已由【郑州市】【郑州分拨中心】卸货入库【联系电话：】"
					}
				],
				"content": "2024-12-25 12:24:15\n货物已由【郑州市】【郑州分拨中心】卸货入库【联系电话：】",
				"showTitle": false
			},
			{
				"id": 38,
				"waybillCode": "241226005",
				"thirdNodeName": "强制入库",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735100654000,
				"nodeContent": "车辆尚未抵达进行强制入库",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "车辆尚未抵达进行强制入库"
					}
				],
				"content": "2024-12-25 12:24:14\n车辆尚未抵达进行强制入库",
				"showTitle": false
			},
			{
				"id": 37,
				"waybillCode": "241226005",
				"thirdNodeName": "网点发车",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735100578000,
				"nodeContent": "【多多物流超市】发往【郑州分拨中心】，车牌号【豫V2EY82】驾驶员【李龙】随车手机【18650283881】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "【多多物流超市】发往【郑州分拨中心】，车牌号【豫V2EY82】驾驶员【李龙】随车手机【"
					},
					{
						"type": "phone",
						"value": "18650283881"
					},
					{
						"type": "text",
						"value": "】"
					}
				],
				"content": "2024-12-25 12:22:58\n【多多物流超市】发往【郑州分拨中心】，车牌号【豫V2EY82】驾驶员【李龙】随车手机【18650283881】",
				"showTitle": false
			},
			{
				"id": 36,
				"waybillCode": "241226005",
				"thirdNodeName": "货物配载",
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735100391000,
				"nodeContent": "【多多物流超市】装车，装车编号【SN241218000038】下一站网点【郑州分拨中心】",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "0",
				"createTime": 1735183003000,
				"contentList": [
					{
						"type": "text",
						"value": "【多多物流超市】装车，装车编号【SN241218000038】下一站网点【郑州分拨中心】"
					}
				],
				"content": "2024-12-25 12:19:51\n【多多物流超市】装车，装车编号【SN241218000038】下一站网点【郑州分拨中心】",
				"showTitle": false
			},
			{
				"id": 23,
				"waybillCode": "241226005",
				"thirdNodeName": null,
				"nodeType": "transit",
				"nodeName": "运送中",
				"nodeTime": 1735093003000,
				"nodeContent": "货品抵达【河南省郑州市新郑市】，准备分拣。",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "1",
				"createTime": 1735182503000,
				"contentList": [
					{
						"type": "text",
						"value": "货品抵达【河南省郑州市新郑市】，准备分拣。"
					}
				],
				"content": "2024-12-25 10:16:43\n货品抵达【河南省郑州市新郑市】，准备分拣。",
				"showTitle": false
			},
			{
				"id": 22,
				"waybillCode": "241226005",
				"thirdNodeName": null,
				"nodeType": "collect",
				"nodeName": "已揽收",
				"nodeTime": 1735093003000,
				"nodeContent": "安阳 已收取货品。",
				"opUserName": null,
				"opComName": null,
				"routeId": null,
				"defaultNode": "1",
				"createTime": 1735182503000,
				"contentList": [
					{
						"type": "text",
						"value": "安阳 已收取货品。"
					}
				],
				"content": "2024-12-25 10:16:43\n安阳 已收取货品。",
				"showTitle": true
			}
		],
		"msg": ""
	})})
}

// 获取订单展示数据
export const getOrderShowInfo = (orderCode : string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"id": 260,
			"collectName": "金酷",
			"collectPhone": "18818260203",
			"collectAddress": "安阳",
			"sendAddressOne": null,
			"sendAddressTwo": null,
			"sendAddressThree": null,
			"sendAddressFour": null,
			"sendAddressFive": null,
			"collectAddressOne": null,
			"collectAddressTwo": null,
			"collectAddressThree": null,
			"collectAddressFour": null,
			"collectAddressFive": null,
			"sendName": "张爱因",
			"sendPhone": "***********",
			"sendIdCard": null,
			"sendAddress": "河南省郑州市二七区花园口",
			"goodsType": "GOODS_TYPE_OTHER",
			"goodsTypeLabel": "食品饮料",
			"totalWeight": 1.0,
			"totalVolume": 1.0,
			"totalNum": 2,
			"goodsLong": null,
			"goodsWidth": null,
			"goodsHeight": null,
			"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
			"shouldPayAmount": 320.0,
			"actualPayAmount": 320.0,
			"logisticsCompaniesId": 1,
			"logisticsNetworkId": 1,
			"orderStatus": "1",
			"orderStartTime": *************,
			"orderEndTime": null,
			"isReceipt": "0",
			"deliveryMethod": "1",
			"releaseMethod": "2",
			"packMethod": "2",
			"collectionDelivery": 0.0,
			"collectionName": null,
			"collectionBankName": null,
			"collectionCardNum": null,
			"amountFreight": 309.00,
			"totalAmount": 320.00,
			"remark": "测试app到站4444",
			"deliveryAmount": 0.00,
			"insuredAmount": 0.00,
			"amountZdf": 1.00,
			"rateBzf": 1.00,
			"receiptType": "1",
			"receiptAmount": 0.00,
			"releaseAmount": 0.00,
			"insuredPayAmount": 0.00,
			"brokerageAmount": 10.00,
			"brokerageBackType": "1",
			"otherAmount": 0.00,
			"discountAmount": 0.00,
			"collectionFreightAmount": 0.00,
			"collectionPayAmount": 0.00,
			"collectionProcRate": 3.00,
			"taxAmount": 0.00,
			"whetherInvoice": "1",
			"invoiceStatus": "0",
			"discDeptId": "000036",
			"discDeptName": "安阳",
			"discDeptMobile": null,
			"discDeptAddr": null,
			"destDeptId": null,
			"destDeptName": "",
			"modifyReason": null,
			"orderCode": "D25032500001",
			"waybillCode": "*********",
			"billDeptName": "蚌埠1",
			"qrcode": "https://hwscm.com.cn/orderdetail?orderCode=*********",
			"logisticsStatus": "0",
			"logisticsNetworkInfo": {
				"createTime": *************,
				"updateTime": *************,
				"creator": "",
				"updater": "1",
				"deleted": false,
				"createDate": ********,
				"id": 1,
				"name": "安阳网点",
				"address": "河南省郑州市二七区",
				"organizationClass": "1",
				"organizationCode": "**********",
				"organizationType": "CRED_ORG_USCC",
				"organizationPhone": "***********",
				"organizationEmail": null,
				"organizationBankCardNum": null,
				"corporationName": null,
				"corporationPhone": null,
				"corporationEmail": null,
				"corporationIdentityNum": null,
				"corporationIdentityType": null,
				"principalName": "杨一一",
				"principalPhone": "***********",
				"principalEmail": null,
				"principalIdentityNum": "******************",
				"principalIdentityType": "CRED_PSN_CH_IDCARD",
				"networkRank": "1",
				"startStatus": "1",
				"certification": "1",
				"deptId": 121,
				"networkCode": "WD********001",
				"allowBrokerage": "1",
				"receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/7a91de64d43ec2260b77ff3792b24ec71ae569710ead8bff562203a3607d56b4.png",
				"receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/1ab2e52b0660151c925ef261a8620df351848a565b8466defbff13abd234af0f.png",
				"receiveThree": "https://dachisc.wang:9000/wljh-sanqian/bdac73eb40b5b82046788ea0974cb0500e2b421a862d40cc9800b2a245050f68.jpg",
				"receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/141e480512f20abd06a374d21e6409ccf0267257ced12fdd4925bb818d826da0.png",
				"receiveCodeAudit": "1",
				"accountPwd": "$2a$04$zjHw/n5myK0GYyoUIwhjh.OSs8.dytVVj0Bjevhex32zQvOqQ9En2",
				"brokerId": null,
				"principalAuditStatus": "1",
				"levelSort": 3
			},
			"logisticsCompaniesInfo": {
				"id": 1,
				"name": "远通物流-测试",
				"address": "河南省郑州市新郑市",
				"organizationCode": "**********",
				"organizationType": "CRED_ORG_USCC",
				"organizationPhone": "***********",
				"organizationEmail": null,
				"organizationBankCardNum": null,
				"corporationName": null,
				"corporationPhone": null,
				"corporationEmail": null,
				"corporationIdentityNum": null,
				"corporationIdentityType": null,
				"principalName": "张三",
				"principalPhone": "***********",
				"principalEmail": null,
				"principalIdentityNum": "******************",
				"principalIdentityType": "CRED_PSN_CH_IDCARD",
				"networkRank": "1",
				"startStatus": "1",
				"partners": "1",
				"createTime": *************,
				"optionDisabled": null,
				"apiChannel": "SN",
				"groupId": null,
				"comId": "000846",
				"comName": null,
				"apiUsername": "蚌埠",
				"apiPassword": "123456",
				"needSendIdCard": "0",
				"qrcodePre": "",
				"allowBrokerage": "1",
				"receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
				"receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/61cd684eb2564fe618f6ae38a04e80da67b5b2274b54b9ebd4513aea40405962.jpg",
				"receiveThree": "https://hwscm.com.cn:9000/wljh-hongwei/8c28ee14fcbd68caef50448a096bc7cb645992be324a0ca69487624809324322.jpeg",
				"receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/141e480512f20abd06a374d21e6409ccf0267257ced12fdd4925bb818d826da0.png",
				"receiveCodeAudit": "2",
				"amountZdf": 1.00,
				"rateBzf": 1.00,
				"rateFhkhwhf": null,
				"expressionFhkhwhf": null,
				"expressionFreight": null,
				"expressionDelivery": null,
				"collectionProcRate": 3.00,
				"collectionProcMin": 1.00,
				"collectionProcMax": 50.00,
				"receiptAmount": 0.00,
				"releaseAmount": 10.00,
				"allowOtherAmount": "0",
				"allowDiscountAmount": "1"
			},
			"subWaybillList": [
				{
					"createTime": *************,
					"updateTime": *************,
					"creator": "2app",
					"updater": "2app",
					"deleted": false,
					"createDate": ********,
					"id": 1436,
					"waybillCode": "*********",
					"subWaybillCode": "*********001",
					"currentDeptName": "蚌埠1",
					"currentDeptTypeValue": "分公司",
					"currentStatus": "网点入库",
					"sortNum": 1
				},
				{
					"createTime": *************,
					"updateTime": *************,
					"creator": "2app",
					"updater": "2app",
					"deleted": false,
					"createDate": ********,
					"id": 1437,
					"waybillCode": "*********",
					"subWaybillCode": "*********002",
					"currentDeptName": "蚌埠1",
					"currentDeptTypeValue": "分公司",
					"currentStatus": "网点入库",
					"sortNum": 2
				}
			]
		},
		"msg": ""
	})})
}

