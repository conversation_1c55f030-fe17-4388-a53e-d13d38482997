/**
 * 日期格式化
 * @param value 时间戳
 * @param format 格式
 * @returns {string}
 */
export function formatDate(value, format = 'yyyy-MM-dd') {
    const date = new Date(value);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');

    return format.replace('yyyy', year)
        .replace('MM', month)
        .replace('dd', day)
        .replace('HH', hour)
        .replace('mm', minute)
        .replace('ss', second)
}

/**
 * 数字保留指定小数位
 * @param value 值
 * @param pointNum 小数位数
 * @returns {string}
 */
export function formatMoney(value, pointNum = 2) {
    if (value === undefined || value === null || value === '') {
        value = 0;
    }
    let amount;
    try {
        amount = Number(value);
    } catch (e) {
        amount = 0;
    }
    if (isNaN(amount)) {
        amount = 0
    }

    return amount.toFixed(pointNum);
}

/**
 * 金额保留指定小数位
 * @param value 值
 * @param pointNum 小数位数
 * @returns {string}
 */
export function formatDecimal(value, pointNum = 2) {
    if (value === undefined || value === null || value === '') {
        value = 0;
    }
    let amount;
    try {
        amount = Number(value);
    } catch (e) {
        amount = 0;
    }
    if (isNaN(amount)) {
        amount = 0
    }
    // 每三位加英文逗号
    return '¥' + amount.toLocaleString("zh", {
        minimumFractionDigits: pointNum,
        maximumFractionDigits: pointNum
    })
}

