<template>
	<view class="layout-task">
    <z-paging ref="paging" v-model="dataList" @query="queryList" @onRefresh="handleDownRefresh">
      <template #top>
        <!-- 头部 -->
        <view class="layout-task-header">
          <!-- tab切换栏 -->
          <view class="custom-tabs-menu">
            <up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" :itemStyle="tabsItemStyle"
                     lineWidth="58" @click="handleTabsClick" scrollable></up-tabs>
          </view>
          <view class="custom-tabs-btn">
            <up-button :plain="true" text="筛选" @click="handleScreenFn"></up-button>
          </view>
        </view>
        <!-- 搜索框 -->
        <view class="layout-task-search">
          <up-search placeholder="请输入姓名/手机号" v-model="searchParam" shape="round" :showAction="false" bgColor="#fff"
                     @clickIcon="handleSearchClick" @search="getOrderListApi"></up-search>
        </view>
      </template>

      <!-- 内容 -->
      <view class="layout-task-body">
        <!-- 订单 -->
        <view v-if="tabsSelected === 'order'" >
          <view class="custom-card" v-for="(item, index) in dataList" :key="index">
          <view class="custom-card-hender">
            <view class="custom-card-text">
              <text>订单号：</text>
              <up-text :text="item.orderCode" color="#fff" size="14"></up-text>
              <up-button :plain="true" text="复制" @click="copyContent(item.orderCode)"></up-button>
            </view>
            <view>
              <up-text :text="item.logisticsCompaniesName" color="#fff" size="15"></up-text>
            </view>
          </view>

          <view class="custom-card-body" @click="handleDetailsOrder(item)">
            <up-row :customStyle="rowFontStyleStatus">
              <up-col :span="4">{{ item.sendAddress }}</up-col>
              <up-col :span="4" customStyle="color: #ec818e">》》》</up-col>
              <up-col :span="4">{{ item.collectAddress }}</up-col>
            </up-row>
            <up-row :customStyle="rowFontStyleInfo">
              <up-col :span="4">{{ item.sendName }}</up-col>
              <up-col :span="4"></up-col>
              <up-col :span="4">{{ item.collectName }}</up-col>
            </up-row>
          </view>

          <view class="custom-card-footer" style="padding-bottom: 0;" @click="handleDetailsOrder(item)">
            <view class="custom-card-footer-item">
              <text style="font-size: 27rpx;color: #606266;">下单时间：</text>
              <up-text :text="item.orderStartTime" color="#606266" size="14" format="yyyy/mm/dd hh:MM" mode="date"></up-text>
            </view>
          </view>
          <view style="padding-left: 20rpx;padding-right: 20rpx;">
            <up-line color="#e1e4e9"></up-line>
          </view>
          <view class="custom-card-footer" >
            <view class="custom-card-footer-item">
            </view>
            <view class="custom-card-footer-item">
              <view class="custom-card-footer-item-pad-right">
                <up-button :plain="true" text="再开一单" @click="reCreateOrderFn(item)"></up-button>
              </view>
              <view class="custom-card-footer-item-pad-right">
                <up-button :plain="true" text="订单打印" @click="handlePrintOrderFn(item)"></up-button>
              </view>
              <view>
                <up-button :plain="true" text="订单详情" @click="handleDetailsOrder(item)"></up-button>
              </view>
            </view>
          </view>
        </view>
        </view>
        <!-- 运单 -->
        <view v-if="tabsSelected === 'waybill'" >
          <view class="custom-card" v-for="(item, index) in dataList" :key="index">
          <view class="custom-card-hender">
            <view class="custom-card-text">
              <text>运单号：</text>
              <up-text :text="item.waybillCode" color="#fff" size="14"></up-text>
              <up-button :plain="true" text="复制" @click="copyContent(item.waybillCode)"></up-button>
            </view>
            <view>
              <up-text :text="item.logisticsCompaniesName" color="#fff" size="14"></up-text>
            </view>
          </view>

          <view class="custom-card-body" @click="handleDetailsWaybill(item)">
            <up-row :customStyle="rowFontStyleStatus">
              <up-col :span="4">{{ item.sendAddress }}</up-col>
              <up-col :span="4" customStyle="color: #ec818e">》》》</up-col>
              <up-col :span="4">{{ item.collectAddress }}</up-col>
            </up-row>
            <up-row :customStyle="rowFontStyleInfo">
              <up-col :span="4">{{ item.sendName }}</up-col>
              <up-col :span="4">{{ item.orderStatusDesc }}</up-col>
              <up-col :span="4">{{ item.collectName }}</up-col>
            </up-row>
          </view>

          <view class="custom-card-footer" style="padding-bottom: 0;" @click="handleDetailsWaybill(item)">
            <view class="custom-card-footer-item">
              <text style="font-size: 27rpx;color: #606266;">下单时间：</text>
              <up-text :text="item.waybillCreateTime" color="#606266" size="14" format="yyyy/mm/dd hh:MM" mode="date"></up-text>
            </view>
          </view>
          <view style="padding-left: 20rpx;padding-right: 20rpx;">
            <up-line color="#e1e4e9"></up-line>
          </view>
          <view class="custom-card-footer">
            <view class="custom-card-footer-item">
            </view>
            <view class="custom-card-footer-item">
              <view class="custom-card-footer-item-pad-right">
                <up-button :plain="true" text="双联打印" @click="waybillPrintClick(true, item, index)"></up-button>
              </view>
              <view class="custom-card-footer-item-pad-right">
<!--                  <up-button :plain="true" text="运单打印" @click="handlePrintWaybillFn(item)"></up-button>-->
                <up-button :plain="true" text="运单打印" @click="waybillPrintClick(false, item, index)"></up-button>
              </view>
              <view class="custom-card-footer-item-pad-right">
                <up-button :plain="true" text="状态同步" @click="handleTongBu(item)"></up-button>
              </view>
              <view>
                <up-button :plain="true" icon="more-dot-fill" @click="handleMoreDot(index)"></up-button>
              </view>
<!--              <view>
                <up-button :plain="true" text="运单详情" @click="handleDetailsWaybill(item)"></up-button>
              </view>-->
            </view>
          </view>

          <view class="custom-card-footer custom-popup-card1" v-if="moreDotShow && moreDotIndexShow === index && lastWayBillIndex!==index">
            <view class="custom-card-footer-item"></view>
            <view class="custom-card-footer-item-more">
              <view>
                <up-button :plain="true" text="运单详情" @click="handleDetailsWaybill(item)"></up-button>
              </view>
              <view style="padding-top:10rpx;">
                <up-button :plain="true" text="申请改单" @click="handleUpdateWaybill(item)"></up-button>
              </view>
              <view style="padding-top:10rpx;">
                <up-button :plain="true" text="申请删单" @click="handleDeleteWaybill(item)"></up-button>
              </view>
              <view style="padding-top:10rpx;">
                <up-button :plain="true" text="申请废单" @click="handleCancelWaybill(item)"></up-button>
              </view>
              <up-icon size="10" name="arrow-up-fill" color="#909399"></up-icon>
            </view>
          </view>
          <view class="custom-card-footer custom-popup-card2" v-if="moreDotShow && moreDotIndexShow === index && lastWayBillIndex===index">
            <view class="custom-card-footer-item"></view>
            <view class="custom-card-footer-item-more">
              <view style="padding-bottom:10rpx;">
                <up-button :plain="true" text="运单详情" @click="handleDetailsWaybill(item)"></up-button>
              </view>
              <view style="padding-bottom:10rpx;">
                <up-button :plain="true" text="申请改单" @click="handleUpdateWaybill(item)"></up-button>
              </view>
              <view style="padding-bottom:10rpx;">
                <up-button :plain="true" text="申请删单" @click="handleDeleteWaybill(item)"></up-button>
              </view>
              <view>
                <up-button :plain="true" text="申请废单" @click="handleCancelWaybill(item)"></up-button>
              </view>
              <up-icon size="10" name="arrow-down-fill" color="#909399"></up-icon>
            </view>
          </view>

          <view class="custom-card-footer custom-popup-card3" v-if="printSubShow && printSubIndexShow === index && lastWayBillIndex!==index">
            <view class="custom-card-footer-item"></view>
            <view class="custom-card-footer-item-more">
              <view style="padding-bottom:10rpx;">
                <view>
                  <text>货品<text style="color: red">{{ item.subWaybillList.length }}</text>件</text>
                </view>
                <view>
                  <text>可打印子单<text style="color: red">{{ item.subWaybillList.length }}</text>个</text>
                </view>
              </view>
              <view >
                <up-line color="#e1e4e9"></up-line>
              </view>
              <view>
                <view style="padding-top:10rpx;width: 140rpx;margin-left: 78rpx;">
                  <up-button :plain="true" text="全部打印" @click="handlePrintWithSub(true, item)"></up-button>
                </view>
                <view style="padding-top:10rpx;">
                  <up-number-box integer :min="1" :max="item.subWaybillList.length" v-model="printSubNum"
                                 inputWidth="40"
                  ></up-number-box>
                </view>
                <view style="padding-top:10rpx;width: 140rpx;margin-left: 78rpx;">
                  <up-button :plain="true" text="部分打印" @click="handlePrintWithSub(false, item)"></up-button>
                </view>
              </view>
              <view class="card3arrow">
                <up-icon v-if="!printBoth" size="10" name="arrow-up-fill" color="#909399"></up-icon>
              </view>
              <view class="card3arrow-both">
                <up-icon v-if="printBoth" size="10" name="arrow-up-fill" color="#909399"></up-icon>
              </view>
            </view>
          </view>

          <view class="custom-card-footer custom-popup-card4" v-if="printSubShow && printSubIndexShow === index && lastWayBillIndex===index">
            <view class="custom-card-footer-item"></view>
            <view class="custom-card-footer-item-more">
              <view style="padding-bottom:10rpx;">
                <view>
                  <text>货品<text style="color: red">{{ item.subWaybillList.length }}</text>件</text>
                </view>
                <view>
                  <text>可打印子单<text style="color: red">{{ item.subWaybillList.length }}</text>个</text>
                </view>
              </view>
              <view >
                <up-line color="#e1e4e9"></up-line>
              </view>
              <view>
                <view style="padding-top:10rpx;width: 140rpx;margin-left: 78rpx;">
                  <up-button :plain="true" text="全部打印" @click="handlePrintWithSub(true, item)"></up-button>
                </view>
                <view style="padding-top:10rpx;">
                  <up-number-box integer :min="1" :max="item.subWaybillList.length" v-model="printSubNum"
                                 inputWidth="40"
                  ></up-number-box>
                </view>
                <view style="padding-top:10rpx;width: 140rpx;margin-left: 78rpx;">
                  <up-button :plain="true" text="部分打印" @click="handlePrintWithSub(false, item)"></up-button>
                </view>
              </view>
              <view class="card4arrow">
                <up-icon v-if="!printBoth" size="10" name="arrow-down-fill" color="#909399"></up-icon>
              </view>
              <view class="card4arrow-both">
                <up-icon v-if="printBoth" size="10" name="arrow-down-fill" color="#909399"></up-icon>
              </view>
            </view>
          </view>

        </view>
        </view>

      </view>
    </z-paging>

		<!-- 弹窗提示（未授权） -->
		<up-modal :show="modalShow" :content='modalContent' showCancelButton contentTextAlign="center"
			@confirm="handleConfirm" @cancel="handleCancel"></up-modal>

		<!-- 筛选弹窗 -->
		<view class="custom-popup" v-if="customPopupShow" @click="handlePopupClick">
			<view class="custom-popup-card" @click.stop="customPopupShow = true">
        <up-text text="物流状态" lineHeight="30" color="#606266" ></up-text>
        <view class="custom-popup-card-tag" >
          <up-tag v-for="(item, index) in radiosLogisticsList" :key="index" borderColor="#f4f4f5"
            :color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
            :name="index" @click="handlePopupCardFn(item, index)"></up-tag>
        </view>
        <up-text text="支付方式" lineHeight="30" color="#606266"></up-text>
        <view class="custom-popup-card-tag">
          <up-tag v-for="(item, index) in radiosPayList" :key="index" borderColor="#f4f4f5"
            :color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
            :name="index" @click="handlePopupCardFn(item, index)"></up-tag>
        </view>
        <up-text text="时间范围" lineHeight="30" color="#606266"></up-text>
        <view class="custom-popup-card-tag" style="margin-bottom: 20rpx;">
          <up-tag v-for="(item, index) in radiosDateList" :key="index" borderColor="#f4f4f5"
            :color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
            :name="index" @click="handlePopupCardFn(item, index)"></up-tag>
        </view>
        <view>
          <up-calendar color="#d81e06" :show="calendarShow" mode="range" title="自定义时间" :minDate="minDate"
            :maxDate="maxDate" monthNum="25" @close="handleCalendar" @confirm="handleCalendarEnter"
            :defaultDate="orderParam.customDateList"></up-calendar>
        </view>
				<view class="custom-card-foot">
					<up-row :gutter="60">
						<up-col :offset="1" :span="5">
							<up-button type="error" shape="circle" text="重置" customStyle="height: 30px;"
								@click.stop="handleRest"></up-button>
						</up-col>
						<up-col :span="5">
							<up-button shape="circle" text="确定" customStyle="height: 30px;"
								@click.stop="handleEnter"></up-button>
						</up-col>
					</up-row>
				</view>
			</view>
		</view>
		<canvas id="imgCanvas" type="2d" style="display: none; width:284px;height:378px;" />
		<view class="disapleBox">
			<w-barcode :options="barcodeOptions" ref="barcodeRef" @generate="handleGenerateBar"></w-barcode>
			<w-qrcode :options="qrcodeOptions" ref="qrcodeRef" @generate="handleGenerateQr"></w-qrcode>
		</view>

	</view>

  <!-- 跳转认证弹窗 -->
  <up-modal
      :content="principal.principalAuditContent"
      :show="principal.showPrincipalAudit" showCancelButton
      @confirm="handlePrincipalAuditApplyForm" :confirmText="principal.principalAuditConfirmText"
      @cancel="handleLogout" cancelText="退出登录"
  ></up-modal>
</template>

<script setup>
import {
  reactive, ref, onMounted, watch
} from 'vue'
import {
	getToken
} from "@/utils/auth";
import {
  getOrderInfoPageClient,getWaybillInfoApp,
  getFinalWaybillPrintInfoApp, getFinalSubWaybillPrintInfoApp, getFinalOrderPrintInfoApp,
  saveWayBillPrintRecord, saveOrderPrintRecord
} from '@/api/wljh/task'
import customStorage from "@/utils/customStorage"
import modal from '@/plugins/modal'
import {onLoad, onUnload} from "@dcloudio/uni-app";
import {formatDate} from "@/api/common";
import {getPrincipalAuditCheckResult, handlePrincipalAuditApplyForm, getUserInfo, handleLogout} from "@/api/wljh/mine";
import {printCpcl} from '@/pages/wljh/utils/print'

const paging = ref(null)
const dataList = ref([])

const total = ref(0) // 列表的总页数
const moreDotShow = ref(false)
const moreDotIndexShow = ref(undefined)

const printSubShow = ref(false)
const printSubIndexShow = ref(undefined)
const printSubNum = ref(0)
const printBoth = ref(false)

const barcodeRef = ref(null)
const barcodeTemplatePath = ref()
const barcodeOptions = ref({ // 生成条形码
	code: '241127205', // 生成条形码的值
	width: 260,
	height: 40
})
const qrcodeRef = ref(null)
const qrcodeTemplatePath = ref()
const qrcodeOptions = ref({ // 生成二维码的值
	code: '241127205',
	text: { //二维码绘制文字 非必传
		opacity: 1, //文字透明度 默认不透明1  0~1 非必传
		font: 'bold 20px system-ui', //文字是否加粗 默认normal 20px system-ui 非必传
		color: ["#000000"], // 文字颜色 多个颜色支持渐变色 默认黑色 非必传
		content: "" //文字内容
	},
	size: 110, //生成的二维码的宽高
	padding: 8,
	type: 'none',
	color: ['#000']
})

const canvasRef = ref(null)

const maxDate = ref(formatDate(new Date, 'yyyy-MM-dd')) // 最大日期
const minDate = ref(formatDate(new Date, 'yyyy-MM-dd')) // 最小日期
const radiosPayList = ref([]) // 筛选条件(支付)
const radiosLogisticsList = ref([]) // 筛选条件(物流状态)
const radiosDateList = ref([ // 筛选条件（时间范围）
    {
      'name': '近一周',
      'value': 'week',
      'dictType': 'DATE_TIME',
      'checked': false
    },
    {
      'name': '近一月',
      'value': 'month',
      'dictType': 'DATE_TIME',
      'checked': false
    },
    {
      'name': '三月以上',
      'value': 'threeMonth',
      'dictType': 'DATE_TIME',
      'checked': false
    },
    {
      'name': '自定义时间',
      'value': 'customTime',
      'dictType': 'DATE_TIME',
      'checked': false
    }
]) // 筛选条件(物流状态)
const customPopupShow = ref(false) // 筛选弹窗是否显示
const calendarShow = ref(false) // 筛选弹窗自定义日期是否显示

const tabsSelected = ref('order') // tab选中Key

const modalShow = ref(false) // 弹窗是否显示
const modalContent = ref('请先登录您的账号！') // 弹窗内容
const token = getToken() // 获取密钥

const lastWayBillIndex = ref(-1)// 最后一条运单的索引

const searchParam = ref() // 搜索框参数
// const orderTableList = ref([]) // 运单数据集合
const orderParam = ref({ // 订单查询条件
	pageNo: 1,
	pageSize: 10,
	searchParam: undefined,
	logisticsStatus: undefined,
	payMethod: undefined,
	customDateList: [],
	customDateType: undefined
})
const waybillTableList = ref([]) // 运单数据集合
const waybillIdList = ref([]) // 运单id集合
const waybillParam = ref({ // 运单查询条件
	pageNo: 1,
	pageSize: 10,
	searchParam: undefined,
	logisticsStatus: undefined,
	payMethod: undefined,
	customDateList: [],
	customDateType: undefined
})
const tabsList = ref([ // tab切换栏菜单数据
    {
      name: '订单',
      badge: {
        value: 0
      },
      key: 'order'
    },
    {
      name: '运单',
      key: 'waybill'
    }
])


// 获取字典所有值
const getDictDataInfo = async (dictType) => {
    let dictData
    switch (dictType) {
      case 'PAY_METHOD':
        dictData = [
          {
            "id": 1616,
            "label": "现付",
            "value": "PAY_METHOD_SEND_PAY_SETTLE",
            "dictType": "PAY_METHOD"
          },
          {
            "id": 1617,
            "label": "到付",
            "value": "PAY_METHOD_COLLECT_PAY_SETTLE",
            "dictType": "PAY_METHOD"
          }
        ]
        radiosPayList.value = dictData.map(item => {
          let param = {
            'name': item.label,
            'value': item.value,
            'dictType': dictType,
            'checked': false
          }
          return param
        })
        break;
      case 'LOGISTICS_STATUS':
        dictData = [
          {
            "id": 1621,
            "label": "待寄出",
            "value": "0",
            "dictType": "LOGISTICS_STATUS"
          },
          {
            "id": 1622,
            "label": "运送中",
            "value": "1",
            "dictType": "LOGISTICS_STATUS"
          },
          {
            "id": 1623,
            "label": "待派送",
            "value": "2",
            "dictType": "LOGISTICS_STATUS"
          },
          {
            "id": 1624,
            "label": "派送中",
            "value": "3",
            "dictType": "LOGISTICS_STATUS"
          },
          {
            "id": 1625,
            "label": "待取件",
            "value": "4",
            "dictType": "LOGISTICS_STATUS"
          }
        ]
        radiosLogisticsList.value = dictData.map(item => {
          let param = {
            'name': item.label,
            'value': item.value,
            'dictType': dictType,
            'checked': false
          }
          return param
        })
        break;
    }
}
// (中国大陆)手机号校验
const checkMobile = (obj) => {
	const regex = /^1[3-9]\d{9}$/; //正则表达式
	return regex.test(obj);
}
// tabs 切换事件
const handleTabsClick = (item, index) => {
	tabsSelected.value = item.key
	customPopupShow.value = false
	if (item.key === 'waybill') { // 运单
    waybillParam.value.searchParam = searchParam.value
    waybillParam.value.pageNo = 1
	} else if (item.key === 'order') { // 订单
    orderParam.value.searchParam = searchParam.value
    orderParam.value.pageNo = 1
	}
  paging.value.reload(false)
}
// 页面初始化校验
const initCheck = () => {
	getDictDataInfo('PAY_METHOD')
	getDictDataInfo('LOGISTICS_STATUS')
	barcodeRef.value = null
	barcodeTemplatePath.value = undefined
  qrcodeRef.value = null
  qrcodeTemplatePath.value = undefined
}
// 未授权弹窗确定事件
const handleConfirm = () => {
	// 跳转到登录页面
	uni.redirectTo({
		url: '/page_mine/login'
	});
}
// 未授权弹窗取消事件
const handleCancel = () => {
	// 跳转到首页
	uni.switchTab({
		url: '/pages/wljh/home'
	})
}

// 获取订单信息
const getOrderListApi = () => {
	getOrderInfoPageClient(orderParam.value).then(res => {
		if (res.code === 0) {
			total.value = res.data.total
		}
	}).catch(error => {
		console.log(error)
	})
}
// 订单详情
const handleDetailsOrder = (item) => {
	uni.navigateTo({
		url: '/pages/wljh/sub_pages/orderDetails?orderCode=' + item.orderCode,
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			// res.eventChannel.emit('orderData', {
			// 	data: item
			// })
		}
	})
}

// 搜索框事件
const handleSearchClick = () => {
	waybillParam.value.searchParam = searchParam.value
	orderParam.value.searchParam = searchParam.value
  paging.value.reload(false)
}

// 筛选遮罩层点击事件
const handlePopupClick = () => {
	customPopupShow.value = false
  console.log('遮罩层点击', false)
}
// 筛选事件
const handleScreenFn = () => {
	customPopupShow.value = !customPopupShow.value
  console.log('筛选点击', customPopupShow.value)
}
// 筛选选中
const handlePopupCardFn = (item, index) => {
	if (item.dictType === 'LOGISTICS_STATUS') {
		item.checked = !item.checked
		if (item.checked) {
			orderParam.value.logisticsStatus = item.value
			waybillParam.value.logisticsStatus = item.value
		} else {
			orderParam.value.logisticsStatus = undefined
			waybillParam.value.logisticsStatus = undefined
		}
		radiosLogisticsList.value.map((obj, inx) => {
			if (index !== inx) {
				obj.checked = false
			}
		})
	} else if (item.dictType === 'PAY_METHOD') {
		item.checked = !item.checked
		if (item.checked) {
			orderParam.value.payMethod = item.value
			waybillParam.value.payMethod = item.value
		} else {
			orderParam.value.payMethod = undefined
			waybillParam.value.payMethod = undefined
		}
		radiosPayList.value.map((obj, inx) => {
			if (index != inx) {
				obj.checked = false
			}
		})
	} else if (item.dictType === 'DATE_TIME') {
		item.checked = !item.checked
		if (item.value != 'customTime') {
			calendarShow.value = false
			orderParam.value.customDateList = []
			waybillParam.value.customDateList = []
		} else {
			calendarShow.value = true
			item.checked = true
			getTwoYearsBeforeDate()
		}
		if (item.checked) {
			orderParam.value.customDateType = item.value
			waybillParam.value.customDateType = item.value
		} else {
			orderParam.value.customDateType = undefined
			waybillParam.value.customDateType = undefined
		}
		radiosDateList.value.map((obj, inx) => {
			if (index != inx) {
				obj.checked = false
			}
		})
	}
}
// 获取当前日期两年前的日期
function getTwoYearsBeforeDate() {
	const today = new Date();
	const twoYearsAgo = new Date(today);
	twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
	minDate.value = formatDate(twoYearsAgo, 'yyyy-MM-dd')
	return twoYearsAgo;
}
// 筛选确定按钮
const handleEnter = () => {
  paging.value.reload(false)
  setTimeout(() => {
    // 手机端与遮罩层点击冲突，故延时关闭
    customPopupShow.value = false
    console.log('筛选确定', false)
  }, 300)
}
// 筛选重置按钮
const handleRest = () => {
	radiosLogisticsList.value.map(obj => {
		obj.checked = false
	})
	radiosPayList.value.map(obj => {
		obj.checked = false
	})
	radiosDateList.value.map(obj => {
		obj.checked = false
	})
	orderParam.value.logisticsStatus = undefined
	orderParam.value.payMethod = undefined
	orderParam.value.customDateList = []
	orderParam.value.customDateType = undefined

	waybillParam.value.logisticsStatus = undefined
	waybillParam.value.payMethod = undefined
	waybillParam.value.customDateList = []
	waybillParam.value.customDateType = undefined
  customPopupShow.value = true
  console.log('筛选重置', true)
  paging.value.reload(false)

}
// 日历关闭时触发
const handleCalendar = () => {
	calendarShow.value = false
}
// 日历确定时触发
const handleCalendarEnter = (obj) => {
	orderParam.value.customDateList = obj
	calendarShow.value = false
}

// 运单详情
const handleDetailsWaybill = async (obj) => {
  moreDotShow.value = false
  printSubShow.value = false

  uni.navigateTo({
		// url: '/pages/wljh/sub_pages/waybillDetails',
		url: '/pages/wljh/sub_pages/waybillDetails?waybillCode=' + obj.waybillCode,
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据--20241229改为通过路径传递
			// res.eventChannel.emit('parentPageData', obj)
		}
	})
}


  /**
   * 检查打印机是否配置
   * @returns {boolean}
   */
  const checkPrinter = () => {
    // 调用打印机
    const blueInfo = customStorage.get("BlueInfo")
    const wifiInfo = customStorage.get("WifiInfo")
    if (!blueInfo && !wifiInfo) {
      uni.hideLoading()
      modal.msgError("请先设置打印机配置")
      return false
    } else {
      return true
    }
  }

  /**
   * CPCL方式双联打印-先打客户联，再打标签
   * @param obj 选中的运单数据
   * @returns {Promise<void>}
   */
  const handleBothPrintFn = async (obj) => {
    console.log('双联打印：', obj.orderCode, obj.waybillCode)
    if (checkPrinter()) {
      // 先打客户联
      await handlePrintOrderFn(obj)
      // 停顿一秒
      setTimeout(() => {
        console.log('客户联打印完毕，准备打印标签')
      }, 1000)
      // 再打标签
      await handlePrintWaybillFn(obj)
    }
  }

  /**
   * CPCL方式打印客户联
   * @param obj 选中的订单数据--仅取orderCode
   * @returns {Promise<void>}
   */
  const handlePrintOrderFn = async (obj) => {
    console.log('打印客户联：', obj.orderCode)
    if (checkPrinter()) {
      const resData = await getFinalOrderPrintInfoApp(obj.orderCode, '20003')
      if (resData.code === 0 && resData.data) {
        // console.log(resData.data)
        // 调用打印
        printCpcl(resData.data)
        // 记录打印次数和打印时间
        await saveOrderPrintRecord(obj.orderCode)
      }
    }
  }

  /**
   * CPCL方式打印标签
   * @param obj 选中的运单数据--仅取waybillCode
   * @returns {Promise<void>}
   */
  const handlePrintWaybillFn = async (obj) => {
    console.log('打印标签：', obj.waybillCode)
    if (checkPrinter()) {
      const resData = await getFinalWaybillPrintInfoApp(obj.waybillCode, '20002')
      if (resData.code === 0 && resData.data) {
        console.log(resData.data)
        // 调用打印
        printCpcl(resData.data)
        // 记录打印次数和打印时间
        await saveWayBillPrintRecord(obj.waybillCode)
      }
    }
  }

  /**
   * 运单打印点击事件
   * @param isBoth 是否双联打印
   * @param obj
   * @param inx
   */
  const waybillPrintClick = (isBoth, obj, inx) => {
    if (obj.subWaybillList.length <= 1) {
      // 子单数量 <= 1
      printSubShow.value = false
    } else if (printSubIndexShow.value === undefined) {
      // 子单>1 -弹窗未展示
      printSubShow.value = !printSubShow.value
      printSubNum.value = 1;
    } else if (printSubIndexShow.value !== inx) {
      // 子单>1 -弹窗已展示但不是选中数据的
      printSubShow.value = true
      printSubNum.value = 1;
    } else if (printSubShow.value && printSubIndexShow.value === inx && printBoth.value !== isBoth) {
      // 子单>1 -已点开选中数据的 双联打印/运单打印 弹窗，且本次点击的是 运单打印/双联打印
      printSubShow.value = true
    } else {
      printSubShow.value = !printSubShow.value
      printSubNum.value = 1;
    }
    if (printSubShow.value) {
      // 弹窗下标改为选中数据下标
      printSubIndexShow.value = inx
    } else {
      printSubIndexShow.value = undefined
    }
    printBoth.value = isBoth
    // console.log('打印弹窗：', printSubShow.value, printSubIndexShow.value, printBoth.value)

    if (obj.subWaybillList.length > 0) {
      // 有子单
      if (obj.subWaybillList.length === 1) {
        // 子单数量为1，直接打印
        printSubNum.value = 1;
        console.log('子单数量为1，直接打印')
        if (printBoth.value) {
          // 双联打印
          handleBothPrintFn(obj)
        } else {
          // 只打运单
          handlePrintWaybillFn(obj)
        }
      } else {
        // 子单数量大于1，需用户在弹窗中操作触发打印
        console.log('子单数量为', obj.subWaybillList.length)
      }
    } else {
      console.log('无子单，直接打印')
      // 无子单-直接触发打印
      if (printBoth.value) {
        // 双联打印
        handleBothPrintFn(obj)
      } else {
        // 只打运单
        handlePrintWaybillFn(obj)
      }
    }

  }

  /**
   * 带子单的打印
   * @param isAll 是否全部打印
   * @param obj
   */
  const handlePrintWithSub = async (isAll, obj) => {
    // 关闭子单弹窗
    printSubShow.value = false
    if (printBoth.value) {
      // 双联打印-先打印客户联
      await handlePrintOrderFn(obj)
      // 停顿一秒
      setTimeout(() => {
        console.log('客户联打印完毕，准备打印标签')
      }, 1000)
    }
    let subNum = printSubNum.value
    if (isAll) {
      subNum = obj.subWaybillList.length
    }
    console.log('打印子单：', obj.waybillCode, subNum)
    if (checkPrinter()) {
      const resData = await getFinalSubWaybillPrintInfoApp(obj.waybillCode, '20002', subNum)
      if (resData.code === 0 && resData.data) {
        // 循环调用打印
        for (let i = 0; i < resData.data.length; i++) {
          printCpcl(resData.data[i])
        }
        // 记录打印次数和打印时间
      }
    }
  }


// 监听码制作成功
const handleGenerateBar = async () => {
	const res = await barcodeRef.value.GetCodeImg()
	barcodeTemplatePath.value = res.tempFilePath
	console.log('条形码barcodeTemplatePath=', barcodeTemplatePath.value)
}
const handleGenerateQr = async () => {
	const res = await qrcodeRef.value.GetCodeImg()
	qrcodeTemplatePath.value = res.tempFilePath
	console.log('二维码qrcodeTemplatePath=', qrcodeTemplatePath.value)
}

function getDate() {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0');
	const day = String(now.getDate()).padStart(2, '0');
	return year + month + day;
}

// 初始化
onMounted(() => {
  checkPrincipalAudit()
	initCheck()
})

// 更多按钮事件
const handleMoreDot = (inx) => {
	if (moreDotIndexShow.value === undefined) {
		moreDotShow.value = !moreDotShow.value
	} else if (moreDotIndexShow.value !== inx) {
		moreDotShow.value = true
	} else {
		moreDotShow.value = !moreDotShow.value
	}

	if (moreDotShow.value) {
		moreDotIndexShow.value = inx
	} else {
		moreDotIndexShow.value = undefined
	}
}
// 同步按钮事件
const handleTongBu = async (obj) => {
	await uni.showLoading({
    title: '正在同步',
    mask: true
  })

  waybillParam.value.pageNo = 1
	await pagingWaybillList(waybillParam.value.pageNo, waybillParam.value.pageSize)
	uni.hideLoading()
	modal.msgSuccess("同步运单成功")
}

onLoad(() => {
  // 会自动调用z-paging的@query函数
  console.log('onLoad')
  // 监听个人认证返回
  uni.$on('childFnPrincipal',d=>{
    checkPrincipalAudit()
  })
})
onUnload(() => {
  // 移除监听事件
  uni.$off('childFnPrincipal');
});

// z-paging @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可
const queryList = (pagenum, pagesize)=>{
  pagingDataList(pagenum, pagesize)
}

// 下拉刷新
const handleDownRefresh = async () => {
  console.log('下拉刷新')
  if (tabsSelected.value === 'waybill' && waybillIdList.value) {
    // 运单下拉时同步页面中数据的运单状态
  }
}
// 请求分页数据
const pagingDataList = async (pagenum, pagesize) => {
  if (pagenum === 1) {
    // 请求第一页数据时先清空历史数据
    // orderTableList.value = []
    waybillTableList.value = []
    lastWayBillIndex.value = -1
    // 滚动到顶部
    paging.value.scrollToTop()
  }
  if (tabsSelected.value === 'order') {
    orderParam.value.pageNo = pagenum
    orderParam.value.pageSize = pagesize
    await pagingOrderList(pagenum, pagesize)
  } else if (tabsSelected.value === 'waybill') {
    waybillParam.value.pageNo = pagenum
    waybillParam.value.pageSize = pagesize
    await pagingWaybillList(pagenum, pagesize)
  }
}

// 获取订单分页数据
const pagingOrderList = async (pagenum, pagesize) => {
  console.log('查找订单列表', pagenum, pagesize)
  getOrderInfoPageClient(orderParam.value).then((res) => {
    if (res && res.data) {
      total.value = res.data.total
      paging.value.complete(res.data.list)

      // const newList = orderTableList.value.concat(res.data.list)
      // orderTableList.value = newList
    } else {
      paging.value.complete(false);
    }
  })
}
// 获取运单分页数据
const pagingWaybillList = async (pagenum, pagesize) => {
  console.log('查找运单列表', pagenum, pagesize)
  await getWaybillInfoApp(waybillParam.value).then((res) => {
    if (res && res.data) {
      total.value = res.data.total
      paging.value.complete(res.data.list)
      // 逻辑参数处理
      const newList = waybillTableList.value.concat(res.data.list)
      waybillTableList.value = newList
      waybillIdList.value = waybillTableList.value.map(item => item.id)
      lastWayBillIndex.value = waybillTableList.value.length - 1
    } else {
      paging.value.complete(false);
    }
    console.log('最后运单索引', lastWayBillIndex.value)
  })
}

// 申请改单
const handleUpdateWaybill = (data) => {
  moreDotShow.value = false
  printSubShow.value = false

  uni.navigateTo({
    url: '/pages/wljh/sub_pages/orderUpdateApply',
    success: function (res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('parentPageData', data)
    }
  })
}
// 申请删单
const handleDeleteWaybill = async (data) => {
  moreDotShow.value = false
  printSubShow.value = false

  uni.navigateTo({
    url: '/pages/wljh/sub_pages/orderDeleteApply',
    success: function (res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('parentPageData', data)
    }
  })
}
// 申请废单
const handleCancelWaybill = () => {
	modal.msgSuccess('暂不提供服务')
}
// 转运单
const handleZhuanWaybill = () => {
	modal.msgSuccess('暂不提供服务')
}
// 转外发
const handleZhuanOutbound = () => {
	modal.msgSuccess('暂不提供服务')
}

// 复制到粘贴板
const copyContent = (value) => {
  uni.setClipboardData({
    data: value,
    success: function () {
      console.log('复制成功', value);
      modal.msgSuccess("已复制")
    }
  })
}

// 再开一单
const reCreateOrderFn = (data) => {
  // 导航页跳转，eventChannel不可用
  uni.setStorageSync('reCreateOrderData', data);
  setTimeout(function() {
    uni.reLaunch({
      url: '/pages/wljh/home',
      success: function(res) {
      }
    })
  }, 300)
}

// 负责人认证判断内容
const principal = ref({
  showPrincipalAudit: false,
  principalAuditContent: '',
  principalAuditConfirmText: ''
})
// 判断是否需要进行个人认证
const checkPrincipalAudit = async () => {
  principal.value = await getPrincipalAuditCheckResult()
}

// 样式
const tabsItemStyle = reactive({
	width: '160rpx',
	height: '100rpx'
})
const rowFontStyleStatus = reactive({
	fontSize: '18px',
	fontWeight: '600',
	padding: '20rpx 0',
	color: '#000'
})
const rowFontStyleInfo = reactive({
	fontSize: '15px',
	// color: '#909399',
	color: '#606266',
	padding: '20rpx 0'
})
</script>

<style scoped lang="scss">
.layout-task {
	background-color: #f1f1f1;
	// height: 100vh;
	height: calc(100vh - var(--window-bottom) - var(--window-top));
	//overflow: auto;

	.layout-task-header {
		background-color: #fff;
		height: 100rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.custom-tabs-menu {
			width: 500rpx;
		}

		.custom-tabs-btn {
			padding: 0 20rpx;

			:deep(.u-button--plain.u-button--info) {
				height: 60rpx;
				border-radius: 20px;
				padding: 0 40rpx;
				color: #606266;
			}
		}
	}

	.layout-task-search {
		padding: 20rpx;
	}

	.layout-task-body {
		padding: 0 10px 10px;
		//height: calc(100% - 210rpx);
		overflow: auto;

		.custom-card {
			border-radius: 5px;
			font-size: 15px;
			background-color: #fff;
			margin-bottom: 20rpx;
			position: relative;
			margin: 20rpx 0;

			.custom-card-hender {
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #d81e06;
				color: #fff;
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 10rpx;
				border-top-left-radius: 5px;
				border-top-right-radius: 5px;

				.custom-card-text {
					display: flex;
				}

        :deep(.u-button--plain.u-button--info) {
          border-radius: 40px;
          width: 64rpx;
          height: 40rpx;
          flex: 1 1 0%;
          margin-top: 10rpx;
          margin-left: 6rpx;
          background-color: #d81e06;
        }

        :deep(.u-button__text) {
          font-size: 12px!important;
          color: #ffffff!important;
        }

			}

			.custom-card-body {
				padding: 0 20rpx;

				:deep(.u-col) {
					text-align: center !important;
				}
			}

			.custom-card-footer {
				padding: 20rpx;
				display: flex;
				justify-content: space-between;

				.custom-card-footer-item {
					display: flex;
					align-items: center;

					:deep(.u-button--plain.u-button--info) {
						height: 50rpx;
						border-radius: 20px;
						color: #606266;
					}

					:deep(.u-button__text) {
						font-size: 12px !important;
					}

          .custom-card-footer-item-pad-right {
            padding-right:2rpx;
          }
				}
        .custom-card-footer-item-more {
          //display: flex;
          align-items: center;

          :deep(.u-button--plain.u-button--info) {
            display: flex;
            height: 50rpx;
            border-radius: 20px;
            color: #606266;
          }

          :deep(.u-button__text) {
            font-size: 12px !important;
          }

          :deep(.u-number-box__minus) {
            border-top-left-radius: 30px;
            border-bottom-left-radius: 30px;
          }
          :deep(.u-number-box__input) {
            background-color: #ffffff!important;
          }
          :deep(.u-number-box__plus) {
            border-top-right-radius: 30px;
            border-bottom-right-radius: 30px;
          }
        }
			}
		}

		.custom-card:last-child {
			margin-bottom: 0;
		}
	}

	.custom-popup {
		position: absolute;
		top: 100rpx;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #9093998c;
		z-index: 10000;

		.custom-popup-card {
			background-color: #fff;
			padding: 20rpx;

			.custom-popup-card-tag {
				padding: 20rpx;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				column-gap: 20rpx;
				row-gap: 20rpx;

				:deep(.u-tag) {
					justify-content: center;
					min-width: 200rpx;
				}
			}

			.custom-card-foot {
				padding-bottom: 26rpx;

				:deep(.u-button--circle) {
					border-radius: 20px;
				}

				:deep(.u-button--error) {
					background-color: #d81e06;
				}
			}
		}
	}

	:deep(.u-tag--primary) {
		background-color: #d81e06;
	}

	.disapleBox {
		//display: flex;
		display: none;
		position: absolute;
		top: -100px;
		left: -200;
	}

	.custom-popup-card1 {
		// border-top: 1px solid rgb(229, 229, 229);
		box-shadow: rgba(50, 50, 93, 0.1) 2px 2px 20px 5px;
		position: absolute;
		//bottom: 90rpx;
		right: 10rpx;
		border-radius: 20rpx;
		z-index: 1000;
		background-color: #fff;

    :deep(.u-icon--right) {
      position: absolute;
      bottom: 268rpx;
      right: 50rpx;
    }
	}
  .custom-popup-card2 {
		// border-top: 1px solid rgb(229, 229, 229);
		box-shadow: rgba(50, 50, 93, 0.1) 2px 2px 20px 5px;
		position: absolute;
		bottom: 90rpx;
		right: 10rpx;
		border-radius: 20rpx;
		z-index: 1000;
		background-color: #fff;

    :deep(.u-icon--right) {
      position: absolute;
      bottom: -18rpx;
      right: 50rpx;
    }
	}

  .custom-popup-card3 {
    // border-top: 1px solid rgb(229, 229, 229);
    box-shadow: rgba(50, 50, 93, 0.1) 2px 2px 20px 5px;
    position: absolute;
    //bottom: -300rpx;
    right: 280rpx;
    border-radius: 20rpx;
    z-index: 1000;
    background-color: #fff;

    :deep(.card3arrow){
      position: absolute;
      bottom: 320rpx;
      right: 50rpx;
    }

    :deep(.card3arrow-both) {
      position: absolute;
      bottom: 320rpx;
      right: 180rpx;
    }
	}

  .custom-popup-card4 {
    // border-top: 1px solid rgb(229, 229, 229);
    box-shadow: rgba(50, 50, 93, 0.1) 2px 2px 20px 5px;
    position: absolute;
    bottom: 85rpx;
    right: 280rpx;
    border-radius: 20rpx;
    z-index: 1000;
    background-color: #fff;

    :deep(.card4arrow){
      position: absolute;
      bottom: -20rpx;
      right: 50rpx;
    }

    :deep(.card4arrow-both) {
      position: absolute;
      bottom: -20rpx;
      right: 180rpx;
    }
	}

	.custom-popup-card::after {
		z-index: 1000
	}
}

</style>