<template>
	<view class="layout-task">
    <z-paging ref="paging" v-model="dataList" @query="queryList" @onRefresh="handleDownRefresh" default-page-size="20">
      <template #top>
        <!-- 头部 -->
        <view class="layout-task-header">
          <!-- tab切换栏 -->
          <view class="custom-tabs-menu">
            <up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" :itemStyle="tabsItemStyle"
              lineWidth="58" @click="handleTabsClick" scrollable></up-tabs>
          </view>
          <!--<view class="custom-tabs-btn">
            <up-button :plain="true" text="筛选" @click="handleScreenFn"></up-button>
          </view>-->
        </view>
      </template>
      <!-- 内容 -->
      <view class="layout-mine">
        <!-- 未读 -->
        <view v-if="tabsSelected === 'unread'" class="layout-mine-essential">
          <view v-if="!dataList || dataList.length === 0" style="text-align: center;color: #606266; padding-top: 20rpx;">
            暂无数据
          </view>
          <view v-for="(item, index) in dataList">
            <up-collapse :border="false" @open="handleCollapseClick(item)">
              <up-collapse-item title="标题" :name="item.id" :showRight="false">
                <template #title>
                  <up-row>
                    <up-col span="4">
                      <up-text :text="item.createTime" color="#606266" size="15" format="yyyy/mm/dd" mode="date"></up-text>
                    </up-col>
                    <up-col span="8">
                      <up-text :text="item.templateShowContent" size="15" ></up-text>
                    </up-col>
                  </up-row>
                </template>
                <text>{{ item.templateContent }}</text>
              </up-collapse-item>
            </up-collapse>
            <up-line v-if="index !== dataList.length - 1" color="#f4f4f5"></up-line>
          </view>
        </view>
          <!-- 已读 -->
        <view v-if="tabsSelected === 'read'" class="layout-mine-essential">
          <view v-if="!dataList || dataList.length === 0" style="text-align: center;color: #606266; padding-top: 20rpx;">
            暂无数据
          </view>
          <view v-for="(item, index) in dataList">
            <up-collapse :border="false">
              <up-collapse-item title="标题" :name="item.id" :showRight="false">
                <template #title>
                  <up-row>
                    <up-col span="4">
                      <up-text :text="item.createTime" color="#606266" size="15" format="yyyy/mm/dd" mode="date"></up-text>
                    </up-col>
                    <up-col span="8">
                      <up-text :text="item.templateShowContent" size="15" ></up-text>
                    </up-col>
                  </up-row>
                </template>
                <text>{{ index }}{{ item.templateContent }}</text>
              </up-collapse-item>
            </up-collapse>
            <up-line v-if="index !== dataList.length - 1" color="#f4f4f5" :hairline="false"></up-line>
          </view>
        </view>
      </view>
    </z-paging>
		<!-- 筛选弹窗 -->
		<view class="custom-popup" v-if="customPopupShow" @click="handlePopupClick">
			<view class="custom-popup-card" @click.stop="customPopupShow = true">
				<up-text text="时间范围" lineHeight="30" color="#606266"></up-text>
				<view class="custom-popup-card-tag" style="margin-bottom: 20rpx;">
					<up-tag v-for="(item, index) in radiosDateList" :key="index" borderColor="#f4f4f5"
						:color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
						:name="index" @click="handlePopupCardFn(item, index)"></up-tag>
				</view>
				<view>
					<up-calendar color="#d81e06" :show="calendarShow" mode="range" title="自定义时间" :minDate="minDate"
						:maxDate="maxDate" monthNum="25" @close="handleCalendar" @confirm="handleCalendarEnter"
						:defaultDate="unReadParam.customDateList"></up-calendar>
				</view>
				<view class="custom-card-foot">
					<up-row :gutter="60">
						<up-col :offset="1" :span="5">
							<up-button type="error" shape="circle" text="重置" customStyle="height: 30px;"
								@click="handleRest"></up-button>
						</up-col>
						<up-col :span="5">
							<up-button shape="circle" text="确定" customStyle="height: 30px;"
								@click="handleEnter"></up-button>
						</up-col>
					</up-row>
				</view>
			</view>
		</view>
	</view>


</template>

<script setup>
import {
	reactive,
	ref,
	onMounted,
	watch
} from 'vue'
import {
	getToken
} from "@/utils/auth";
import {
  getMyNotifyMessagePage, updateNotifyMessageRead
} from '@/api/wljh/mine'
import {limitStr} from "@/utils/common";

const total = ref(0) // 列表的总页数
const moreDotShow = ref(false)
const moreDotIndexShow = ref(undefined)

const maxDate = ref(new Date) // 最大日期
const minDate = ref(new Date) // 最小日期
const radiosDateList = ref([{ // 筛选条件（时间范围）
	'name': '近一周',
	'value': 'week',
	'dictType': 'DATE_TIME',
	'checked': false
},
{
	'name': '近一月',
	'value': 'month',
	'dictType': 'DATE_TIME',
	'checked': false
},
{
	'name': '三月以上',
	'value': 'threeMonth',
	'dictType': 'DATE_TIME',
	'checked': false
},
{
	'name': '自定义时间',
	'value': 'customTime',
	'dictType': 'DATE_TIME',
	'checked': false
}
]) // 筛选条件(物流状态)
const customPopupShow = ref(false) // 筛选弹窗是否显示
const calendarShow = ref(false) // 筛选弹窗自定义日期是否显示

const tabsSelected = ref('unread') // tab选中Key

const modalShow = ref(false) // 弹窗是否显示
const token = getToken() // 获取密钥


const searchParam = ref() // 搜索框参数
// const unReadTableList = ref([]) // 订单数据集合
const unReadParam = ref({ // 订单查询条件
	pageNo: 1,
	pageSize: 20,
	searchParam: undefined,
  readStatus: false,
	customDateList: [],
	customDateType: undefined
})
// const readTableList = ref([]) // 运单数据集合
const readParam = ref({ // 运单查询条件
	pageNo: 1,
	pageSize: 20,
	searchParam: undefined,
  readStatus: true,
	customDateList: [],
	customDateType: undefined
})
const tabsList = ref([{ // tab切换栏菜单数据
	name: '未读',
	badge: {
		value: 0
	},
	key: 'unread'
},
{
	name: '已读',
	key: 'read'
}
])
// tabs 切换事件
const handleTabsClick = (item, index) => {
	tabsSelected.value = item.key
	customPopupShow.value = false
	if (item.key === 'unread') {
    // 未读
    unReadParam.value.searchParam = searchParam.value
    unReadParam.value.pageNo = 1
	} else if (item.key === 'read') {
    // 已读
    readParam.value.searchParam = searchParam.value
    readParam.value.pageNo = 1
	}
  paging.value.reload(false)
}
// 页面初始化校验
const initCheck = () => {
	// getUnReadListApi()
}

// 筛选遮罩层点击事件
const handlePopupClick = () => {
	customPopupShow.value = false
}
// 筛选事件
const handleScreenFn = () => {
	customPopupShow.value = !customPopupShow.value
}
// 筛选选中
const handlePopupCardFn = (item, index) => {
	if (item.dictType === 'DATE_TIME') {
		item.checked = !item.checked
		if (item.value !== 'customTime') {
			calendarShow.value = false
			unReadParam.value.customDateList = []
			readParam.value.customDateList = []
		} else {
			calendarShow.value = true
			item.checked = true
			getTwoYearsBeforeDate()
		}
		if (item.checked) {
			unReadParam.value.customDateType = item.value
			readParam.value.customDateType = item.value
		} else {
			unReadParam.value.customDateType = undefined
			readParam.value.customDateType = undefined
		}
		radiosDateList.value.map((obj, inx) => {
			if (index != inx) {
				obj.checked = false
			}
		})
	}
}
// 获取当前日期两年前的日期
function getTwoYearsBeforeDate() {
	const today = new Date();
	const twoYearsAgo = new Date(today);
	twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
	minDate.value = twoYearsAgo
	return twoYearsAgo;
}
// 筛选确定按钮
const handleEnter = () => {
	if (tabsSelected.value === 'unread') {
    // 未读
	} else if (tabsSelected.value === 'read') {
    // 已读
	}
	customPopupShow.value = false
  paging.value.reload(false)
}
// 筛选重置按钮
const handleRest = () => {
	radiosDateList.value.map(obj => {
		obj.checked = false
	})
	unReadParam.value.customDateList = []
	unReadParam.value.customDateType = undefined

	readParam.value.customDateList = []
	readParam.value.customDateType = undefined

	if (tabsSelected.value === 'unread') {
    // 未读
	} else if (tabsSelected.value === 'read') {
    // 已读
	}
  console.log('筛选重置', true)
  paging.value.reload(false)

}
// 日历关闭时触发
const handleCalendar = () => {
	calendarShow.value = false
}
// 日历确定时触发
const handleCalendarEnter = (obj) => {
	unReadParam.value.customDateList = obj
	calendarShow.value = false
}

// 点击标为已读
const handleCollapseClick = async (item, index) => {
  console.log(item)
  if (!item.readStatus) {
    await updateNotifyMessageRead({ids: item.id}).then(res => {
      item.readStatus = true
    })
  }
}


function getDate() {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0');
	const day = String(now.getDate()).padStart(2, '0');
	return year + month + day;
}


const paging = ref(null)
const dataList = ref([])

// z-paging @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可
const queryList = (pagenum, pagesize)=>{
  pagingDataList(pagenum, pagesize)
}

// 下拉刷新
const handleDownRefresh = async () => {
  console.log('下拉刷新')
}
// 请求分页数据
const pagingDataList = async (pagenum, pagesize) => {
  if (pagenum === 1) {
    // 请求第一页数据时先清空历史数据
    // orderTableList.value = []
    // 滚动到顶部
    paging.value.scrollToTop()
  }
  if (tabsSelected.value === 'unread') {
    unReadParam.value.pageNo = pagenum
    unReadParam.value.pageSize = pagesize
    await pagingUnReadList(pagenum, pagesize)
  } else if (tabsSelected.value === 'read') {
    readParam.value.pageNo = pagenum
    readParam.value.pageSize = pagesize
    await pagingReadList(pagenum, pagesize)
  }
}
const pagingUnReadList = async (pagenum, pagesize) => {
  console.log('查找未读列表', pagenum, pagesize)
  await getMyNotifyMessagePage(unReadParam.value).then(res => {
    if (res && res.data) {
      res.data.list.map(item => {
        item.templateShowContent = limitStr(item.templateContent, 20)
      })
      total.value = res.data.total
      paging.value.complete(res.data.list)
      console.log(total.value, res.data.list.length)

      // const newList = orderTableList.value.concat(res.data.list)
      // orderTableList.value = newList
    } else {
      paging.value.complete(false);
    }
  })
}
const pagingReadList = async (pagenum, pagesize) => {
  console.log('查找已读列表', pagenum, pagesize)
  await getMyNotifyMessagePage(readParam.value).then(res => {
    if (res && res.data) {
      res.data.list.map(item => {
        item.templateShowContent = limitStr(item.templateContent, 20)
      })
      total.value = res.data.total
      paging.value.complete(res.data.list)
      console.log(total.value, res.data.list.length)

      // const newList = orderTableList.value.concat(res.data.list)
      // orderTableList.value = newList
    } else {
      paging.value.complete(false);
    }
  })
}

// 初始化
onMounted(() => {
	initCheck()
})

// 样式
const tabsItemStyle = reactive({
	width: '160rpx',
	height: '100rpx'
})
</script>

<style scoped lang="scss">
.layout-mine {
  background-color: #f1f1f1;
  //max-height: calc(100vh - var(--window-bottom) - var(--window-top) - 150rpx);
  //max-height: calc(100vh - var(--window-bottom) - var(--window-top));
  //height: calc(100% - 190rpx);
  overflow: auto;
  padding: 20rpx 20rpx 0rpx 20rpx;

  .layout-mine-essential {
    background-color: #fff;
    border-radius: 5px;
    margin-bottom: 10rpx;
    padding: 0 20rpx 0 20rpx;

    :deep(.u-cell__body) {
      //height: 120rpx;
    }

    .custom-cell-title {
      display: flex;

      .custom-cell-title-item {
        margin-left: 26rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}
.layout-task {
	background-color: #f1f1f1;
	// height: 100vh;
  height: calc(100vh - var(--window-bottom) - var(--window-top));
	//overflow: hidden;

	.layout-task-header {
		background-color: #fff;
		height: 100rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.custom-tabs-menu {
			width: 500rpx;
		}

		.custom-tabs-btn {
			padding: 0 20rpx;

			:deep(.u-button--plain.u-button--info) {
				height: 60rpx;
				border-radius: 20px;
				padding: 0 40rpx;
				color: #606266;
			}
		}
	}

	.layout-task-search {
		padding: 20rpx;
	}

	.layout-task-body {
		padding: 0 10px 10px;
		height: calc(100% - 230rpx);
		overflow: auto;

		.custom-card {
			border-radius: 5px;
			font-size: 15px;
			background-color: #fff;
			margin-bottom: 20rpx;
			position: relative;
			//margin: 20rpx 0;

			.custom-card-hender {
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #d81e06;
				color: #fff;
				height: 60rpx;
				line-height: 60rpx;
				padding: 0 20rpx;
				border-top-left-radius: 5px;
				border-top-right-radius: 5px;

				.custom-card-text {
					display: flex;
				}
			}

			.custom-card-body {
				padding: 0 20rpx;

				:deep(.u-col) {
					text-align: center !important;
				}
			}

			.custom-card-footer {
				padding: 20rpx;
				display: flex;
				justify-content: space-between;

				.custom-card-footer-item {
					display: flex;
					align-items: center;

					:deep(.u-button--plain.u-button--info) {
						height: 50rpx;
						border-radius: 20px;
						color: #606266;
					}

					:deep(.u-button__text) {
						font-size: 12px !important;
					}
				}
			}
		}

		.custom-card:last-child {
			margin-bottom: 0;
		}
	}

	.custom-popup {
		position: absolute;
		top: 100rpx;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #9093998c;
		z-index: 10000;

		.custom-popup-card {
			background-color: #fff;
			padding: 20rpx;

			.custom-popup-card-tag {
				padding: 20rpx;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				column-gap: 20rpx;
				row-gap: 20rpx;

				:deep(.u-tag) {
					justify-content: center;
					min-width: 200rpx;
				}
			}

			.custom-card-foot {
				padding-bottom: 26rpx;

				:deep(.u-button--circle) {
					border-radius: 20px;
				}

				:deep(.u-button--error) {
					background-color: #d81e06;
				}
			}
		}
	}

	:deep(.u-tag--primary) {
		background-color: #d81e06;
	}

	.disapleBox {
		//display: flex;
		display: none;
		position: absolute;
		top: -100px;
		left: -200;
	}

	.custom-popup-card1 {
		// border-top: 1px solid rgb(229, 229, 229);
		box-shadow: rgba(50, 50, 93, 0.1) 2px 2px 20px 5px;
		position: absolute;
		bottom: 90rpx;
		right: 10rpx;
		border-radius: 20rpx;
		z-index: 1000;
		background-color: #fff;

		:deep(.u-icon--right) {
			position: absolute;
			bottom: -18rpx;
			right: 50rpx;
		}
	}

	.custom-popup-card::after {
		z-index: 1000
	}
}
</style>