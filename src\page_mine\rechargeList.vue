<template>
  <view class="container">

    <z-paging ref="paging" v-model="dataList" @query="queryList" @onRefresh="handleDownRefresh">
      <template #top>
      <!-- 筛选栏 -->
      <view class="search-top" >
        <up-row>
          <up-col span="3">
            <button class="search-button" @click="handleShowTypeSelect">
              {{ typeSelectLabel }}
              <up-icon size="20rpx" name="arrow-down-fill" customStyle="margin-left: 10rpx;"></up-icon>
            </button>
          </up-col>
          <up-col span="6"></up-col>
          <up-col span="3">
            <button class="add-button" @click="handleAddApply">
<!--              <up-icon size="36rpx" name="plus-circle" color="#fff" customStyle="margin-right:6rpx;"></up-icon>-->
              新增
            </button>
          </up-col>
        </up-row>
      </view>
      </template>

      <!-- 账单列表 -->
      <view class="log-list">
        <view v-for="item in dataList" :style="getLogDataViewStyle(item.type)">
          <!-- 日期行 -->
          <view v-if="item.type === 'date'" class="log-data">
            <up-row>
              <up-col span="4">
                <view style="display: flex;align-items: center;" @click="handleShowDatetimePicker(item.applyTime)">
                  <text>{{ formatDate(item.applyTime, 'yyyy年MM月') }}</text>
                  <up-icon size="20rpx" name="arrow-down"></up-icon>
                </view>
              </up-col>
              <up-col span="8" customStyle="text-align: right;">
                <text style="font-size: 13px;color: #6a6a6a;">
                  申请提现 ¥{{ formatMoney(item.applyAmount, 2) }} 已提现 ¥{{ formatMoney(item.finishAmount, 2) }}
                </text>
              </up-col>
            </up-row>
          </view>
          <!-- 数据行 -->
          <view v-if="item.type === 'item'" class="log-item" @click="handleShowForm(item)">
            <up-line color="#e5e5e5" customStyle="margin-bottom: 20rpx;"></up-line>
            <view class="log-item-row-1">
              <up-row>
                <up-col span="6">{{ getRechargeApplyStatus(item.applyStatus) }}</up-col>
                <up-col span="6" customStyle="text-align: right;">
                  <text v-if="['4'].indexOf(item.applyStatus)<0" style="color: #3a3a3a" >{{ formatMoney(item.rechargeAmount, 2) }}</text>
                  <text v-if="['4'].indexOf(item.applyStatus)>=0" style="color: #edb703" >{{ formatMoney(item.rechargeAmount, 2) }}</text>
                </up-col>
              </up-row>
            </view>
            <view class="log-item-row-2">
              <up-row>
                <up-col span="6">{{ formatDate(item.applyTime, 'yyyy-MM-dd HH:mm:ss') }}</up-col>
                <up-col span="6" customStyle="text-align: right;">
                  {{ item.applyUserName }}
                </up-col>
              </up-row>
            </view>
          </view>

        </view>
      </view>
    </z-paging>

  </view>

  <!-- 日期选择 -->
  <up-datetime-picker
      :show="showDatetimePicker"
      v-model="selectChangeTime"
      mode="year-month"
      closeOnClickOverlay
      @confirm="confirmDatetimePicker"
      @cancel="showDatetimePicker=false"
      @close="showDatetimePicker=false"
  ></up-datetime-picker>
  <!-- 申请状态弹窗 -->
  <up-popup :show="showTypeSelect" :round="10" :closeable="true" mode="bottom" @close="handleShowTypeSelect" @touchmove.stop.prevent="">
    <view class="type-select">
      <view class="type-select-title">选择申请状态</view>
      <view class="type-select-items">
        <view class="type-select-item">
          <up-tag v-for="(item, index) in radiosApplyStatusList" :key="index" borderColor="#f4f4f5"
                  :color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
                  :name="index" @click="handlePopupCardFn(item, index)"></up-tag>
        </view>
      </view>
      <view class="type-select-bottom">
        <up-row :gutter="60">
          <up-col :offset="1" :span="5">
            <up-button shape="circle" text="取消" customStyle="height: 30px;"
                       @click.stop="handleShowTypeSelect"></up-button>
          </up-col>
          <up-col :span="5">
            <up-button shape="circle" text="确定" customStyle="height: 30px;" type="error"
                       @click.stop="handleEnterTypeSelect"></up-button>
          </up-col>
        </up-row>
      </view>
    </view>
  </up-popup>
</template>

<script setup>
import {ref, onMounted, getCurrentInstance} from "vue"
import {formatDate, formatMoney} from "@/api/common";
import {onLoad} from "@dcloudio/uni-app";
import {getRechargePage, getRechargeApplyStatus} from "@/page_mine/api/sub_mine";

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const paging = ref(null)
const queryParam = ref({ // 查询条件
  pageNo: 1,
  pageSize: 10,
  logisticsNetworkId: undefined, // 网点id
  applyStatus: undefined, // 申请状态
  maxApplyTime: undefined,
  applyTime: [],
  dateList: [],
})
const dataList = ref([]) // 数据列表
const timeList = ref([]) // 日期列表

// 日期选择
const showDatetimePicker = ref(false) // 日期选择展示
const selectChangeTime = ref(undefined) // 当前选中的日期
// 日期选择展示
const handleShowDatetimePicker = (date) => {
  selectChangeTime.value = date
  showDatetimePicker.value = !showDatetimePicker.value
}
// 选中日期
const confirmDatetimePicker = (e) => {
  console.log(e.value, e.mode)
  // 获取选中月份的1号和下月1号的时间
  const date = new Date(e.value)
  // const startDate = new Date(date.getFullYear(), date.getMonth() , 1, 0, 0, 0)
  const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 1, 0, 0, 0)
  // endDate减一秒
  endDate.setSeconds(endDate.getSeconds() - 1)
  // queryParam.value.applyTime[0] = formatDate(startDate, 'yyyy-MM-dd HH:mm:ss')
  // queryParam.value.applyTime[1] = formatDate(endDate, 'yyyy-MM-dd HH:mm:ss')
  queryParam.value.maxApplyTime = formatDate(endDate, 'yyyy-MM-dd HH:mm:ss')
  paging.value.reload(false)
  showDatetimePicker.value = false
}

const typeSelectLabel = ref('全部') // 类型筛选文本
const showTypeSelect = ref(false) // 申请状态弹窗展示
// 选择申请状态
const handleShowTypeSelect = () => {
  showTypeSelect.value = !showTypeSelect.value
}

// 申请状态列表 1-待审核 2-审核通过 3-审核不通过 4-已充值
const radiosApplyStatusList = ref([
  {
    name: '全部',
    checked: true,
    value: undefined
  },
  {
    name: '待审核',
    checked: false,
    value: '1'
  },
  {
    name: '审核通过',
    checked: false,
    value: '2'
  },
  {
    name: '审核不通过',
    checked: false,
    value: '3'
  },
  {
    name: '已充值',
    checked: false,
    value: '4'
  },
])
// 筛选选中
const handlePopupCardFn = (item, index) => {
  item.checked = !item.checked
  if (item.checked) {
    queryParam.value.applyStatus = item.value
  } else {
    queryParam.value.applyStatus = undefined
  }
  radiosApplyStatusList.value.map((obj, inx) => {
    if (index !== inx) {
      obj.checked = false
    }
  })
}
// 类型筛选弹窗确定
const handleEnterTypeSelect = () => {
  showTypeSelect.value = false
  if (!queryParam.value.applyStatus) {
    typeSelectLabel.value = '全部'
  } else {
    radiosApplyStatusList.value.map((obj, inx) => {
      if (obj.value === queryParam.value.applyStatus) {
        typeSelectLabel.value = obj.name
      }
    })
  }
  paging.value.reload(false)
}

// z-paging @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可
const queryList = (pagenum, pagesize)=>{
  pagingDataList(pagenum, pagesize)
}
// 下拉刷新
const handleDownRefresh = async () => {
  console.log('下拉刷新')
  paging.value.reload(false)
}
// 请求分页数据
const pagingDataList = async (pagenum, pagesize) => {
  if (pagenum === 1) {
    // 请求第一页数据时先清空历史数据
    dataList.value = []
    timeList.value = []
    // 滚动到顶部
    paging.value.scrollToTop()
  }
  console.log('请求分页数据', pagenum, pagesize)
  queryParam.value.pageNo = pagenum
  queryParam.value.pageSize = pagesize
  queryParam.value.dateList = timeList.value
  await getRechargePage(queryParam.value).then(res => {
    if (res && res.data) {
      // 处理list数据
      let tempList = res.data.list
      for (let item of tempList) {
        if (item.date && timeList.value.indexOf(item.date) < 0) {
          timeList.value.push(item.date)
        }
      }
      // console.log(tempList)

      paging.value.complete(tempList)
    } else {
      paging.value.complete(false);
    }
  })
}

// 新增申请
const handleAddApply = () => {
  uni.navigateTo({
    url: '/page_mine/rechargeForm',
    events: {
      childFn: function(data) {
        if (data) {
          paging.value.reload(false)
        }
      }
    },
    success: function(res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('parentParam', {
        data: {accountRole: '2', logisticsNetworkId: queryParam.value.logisticsNetworkId},
        type: 'create'
      })
    }
  })
}
// 查看申请
const handleShowForm = (item) => {
  uni.navigateTo({
    url: '/page_mine/rechargeForm',
    success: function(res) {
      // 通过eventChannel向被打开页面传送数据
      res.eventChannel.emit('parentParam', {
        data: item,
        type: 'info'
      })
    }
  })
}

onLoad(() => {
  // 会自动调用z-paging的@query函数
  console.log('onLoad')
})

onMounted(() => {
  console.log('onMounted')
  eventChannel.on('rechargeListParams', function(data) {
    if (data) {
      console.log(data)
      queryParam.value.logisticsNetworkId = data.id
      paging.value.reload(false)
    }
  })
})

const getLogDataViewStyle = (type) => {
  if (type === 'date') {
    return 'position: sticky;top: 0;z-index:999;'
  } else {
    return ''
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f1f1f1;
  height: calc(100vh - var(--window-bottom) - var(--window-top));
}
.search-top {
  height: 52px;
  padding: 20rpx;
  border-bottom: 1px solid #e5e5e5;

  .search-button{
    color:#000;
    height:30px;
    background-color: #dedede;
    border-radius: 100px;
    border-color: #ebedf0;
    border-width: 1px;
    border-style: solid;
    padding: 0 12px;
    font-size: 12px;
    position: relative;
    align-items: center;
    justify-content: center;
    display: flex;
    width: 220rpx;
  }
  .search-button::after{
    border: none;
  }

  .add-button{
    color: #fff;
    height:30px;
    background-color: #d81e06;
    border-radius: 100px;
    border-color: #ebedf0;
    border-width: 1px;
    border-style: solid;
    padding: 0 12px;
    font-size: 12px;
    position: relative;
    align-items: center;
    justify-content: center;
    display: flex;
    width: 100%;
  }
  .add-button::after{
    border: none;
  }
}
.log-list {
  //height: calc(100vh - var(--window-bottom) - var(--window-top) - 52px);
  //overflow: auto;
  background-color: #fff;

  .log-data {
    //border-bottom: 1px solid #e5e5e5;
    padding: 20rpx;
    background-color: #ededed;
    position: sticky;
  }
  .log-item {
    //border-bottom: 1px solid #e5e5e5;
    padding: 0 20rpx 20rpx 20rpx;

    .log-item-row-1 {

    }
    .log-item-row-2 {
      font-size: 12px;
      color: #6a6a6a;
    }
  }
}
.type-select {
  //height: calc(100vh - var(--window-bottom) - var(--window-top) - 200rpx);
  //height: 40vh;
  overflow: auto;

  .type-select-title {
    width: 100vw;
    font-size: 14px;
    position: absolute;
    background-color: #ffffff;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding: 20rpx;
    z-index: 100;
  }
  .type-select-items {
    padding: 20rpx;
    margin-top: 50rpx;

    .type-select-item {
      padding: 20rpx;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      column-gap: 20rpx;
      row-gap: 20rpx;

      :deep(.u-tag) {
        justify-content: center;
        min-width: 300rpx;
        border-radius: 7px;
      }
    }
  }

  .type-select-bottom {
    padding-bottom: 26rpx;

    :deep(.u-button--circle) {
      border-radius: 20px;
    }

    :deep(.u-button--error) {
      background-color: #d81e06;
    }
  }

  :deep(.u-tag--primary) {
    background-color: #d81e06;
  }
}
</style>