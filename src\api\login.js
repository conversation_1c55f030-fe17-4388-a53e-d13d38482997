import request from '@/utils/request'

// 登录方法
export function login(account, password, socialCode) {
	const data = {
		account,
		password,
		socialCode
	}
	return request({
		url: '/client/auth/login',
		headers: {
			isToken: false
		},
		method: 'POST',
		data: data
	})
}

// 注册方法
export function register(data) {
	return request({
		url: '/register',
		headers: {
			isToken: false
		},
		method: 'POST',
		data: data
	})
}


// 退出方法
export function logout() {
	return request({
		url: '/client/auth/logout',
		method: 'POST'
	})
}
