<template>
	<view class="layout-mine">
		<view class="layout-mine-essential">
			<up-cell-group>
				<up-cell>
					<template #title>
						<view class="custom-cell-title">
							<up-image shape="circle" :src="dataParam.headPortrait" width="80rpx"
								height="80rpx"></up-image>
							<view class="custom-cell-title-item">
                <!-- 账号名称 -->
								<up-text :text="dataParam.nickname"></up-text>
                <view style="display: flex;justify-content: center;">
                  <!-- 网点编号 -->
                  <up-text :text="dataParam.logisticsNetworkInfo.networkCode" size="12" color="#82848a"></up-text>
                  <!-- 网点等级图标 -->
                  <view style="margin-left: 10rpx">
                    <up-icon size="60rpx" :name="dataParam.levelInfo.levelImg" ></up-icon>
                  </view>
                  <!-- 试用图标 -->
                  <view v-if="dataParam.levelInfo.levelSort<1" style="margin-left: 10rpx">
                    <up-icon size="60rpx" :name="tryOutIcon" ></up-icon>
                  </view>
                </view>
              </view>
						</view>
					</template>

          <!--<template #value>
            <up-icon name="weixin-fill" size="30"></up-icon>
          </template>-->

          <template #right-icon>
            <up-icon size="60rpx" :name="chatIcon" @click="handleChat"></up-icon>
          </template>
				</up-cell>
			</up-cell-group>
		</view>
    <view class="layout-mine-essential">
      <up-cell-group>
        <up-cell title="经营账户" isLink @click="handleAccountInfo" ></up-cell>
      </up-cell-group>
    </view>
    <view class="layout-mine-essential">
      <up-cell-group>
        <up-cell title="账号资料" isLink @click="handleUserInfo" ></up-cell>
        <up-cell title="网点资料" isLink @click="handleNetworkInfo" ></up-cell>
<!--        <up-cell title="网点等级" isLink @click="handleNetworkLevel" ></up-cell>-->
      </up-cell-group>
    </view>
		<view class="layout-mine-essential">
      <up-cell-group>
				<up-cell title="打印机配置" isLink @click="handlePrintSetting" :value="printConfigStatus"></up-cell>
			</up-cell-group>
		</view>
		<view v-if="dataParam.levelInfo.levelSort>=2" class="layout-mine-essential">
      <up-cell-group>
				<up-cell v-if="dataParam.levelInfo.levelSort>=2" title="邀新" isLink @click="handleInviteNetwork" ></up-cell>
				<up-cell v-if="dataParam.levelInfo.levelSort>=3" title="审核" isLink @click="handleAuditNetwork" ></up-cell>
<!--				<up-cell v-if="dataParam.levelInfo.levelSort>=2" title="注册" isLink @click="handleRegisterNetwork" ></up-cell>-->
			</up-cell-group>
		</view>
		<view class="layout-mine-essential">
			<up-cell-group>
				<up-cell title="关于我们" isLink @click="handleAboutUs"></up-cell>
				<up-cell title="服务条款" isLink @click="handleUserAgrement"></up-cell>
				<up-cell title="隐私政策" isLink @click="handlePrivacy"></up-cell>
			</up-cell-group>
		</view>
		<view>
			<up-button :plain="true" text="退出登录" shape="circle"
				customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleLogout"></up-button>
		</view>
	</view>

  <!-- 跳转认证弹窗 -->
  <up-modal
      :content="principal.principalAuditContent"
      :show="principal.showPrincipalAudit" showCancelButton
      @confirm="handlePrincipalAuditApplyForm" :confirmText="principal.principalAuditConfirmText"
      @cancel="handleLogout" cancelText="退出登录"
  ></up-modal>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from "vue"
	import customStorage from "@/utils/customStorage"
  import {
    handleLogout, getUserInfo, getPrincipalAuditCheckResult, handlePrincipalAuditApplyForm
  } from '@/api/wljh/mine'
  import {onLoad, onUnload} from "@dcloudio/uni-app";

  const chatIcon = ref('https://dachisc.wang:9000/wljh/4570dbbbf85d4835c09543be5a257b3ee35bb88aca85f016c492e708337bf266.svg')
  const defaultHeader = ref('https://dachisc.wang:9000/wljh/4e29406c6510fb74ce5ce3538304a07b1dd7fa6333937e7da6b5e9384af75fb7.svg')

  const tryOutIcon = ref('https://dachisc.wang:9000/wljh-sanqian/bff1899e2c8297b0d3f3db1621019e4f4faf1ebe38cae20eb561c165b2d29c3d.png')

	const dataParam = ref({
		id: undefined,
		headPortrait: defaultHeader,
		nickname: undefined,
		account: undefined,
		logisticsNetworkInfo: {
			id: undefined,
			certification: '0',
			name: undefined,
			address: undefined,
      networkCode: undefined
		}
	})
  const printConfigStatus = ref()
	const defaultNickname = ref() //默认昵称
  // 认证审核状态
  const authApprovalStatus = ref('')

  const handleChat = () => {
    console.log(dataParam.value);
    uni.navigateTo({
      url: '/page_mine/chat',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('params', dataParam.value)
      }
    })
  }

	// 获取个人详细信息
	const getUserInfoApi = async (param) => {
		await getUserInfo(param).then(res => {
			if (res.code === 0) {
				dataParam.value = res.data
				// 设置头像
				if (!res.data.headPortrait) {
					dataParam.value.headPortrait = defaultHeader
				}
        // 默认昵称
				defaultNickname.value = JSON.parse(JSON.stringify(res.data)).nickname
        // 实名认证：0-未认证 1-已认证 2-认证待审核 3-认证审核不通过
        if (res.data.logisticsNetworkInfo.certification === '1') {
          authApprovalStatus.value = '已认证'
        } else if (res.data.logisticsNetworkInfo.certification === '2') {
          authApprovalStatus.value = '认证待审核'
        } else if (res.data.logisticsNetworkInfo.certification === '3') {
          authApprovalStatus.value = '认证审核不通过'
        } else {
          authApprovalStatus.value = '未认证'
        }
			}
		})
	}
  // 打印机配置
  const getPrintConfigStatus = async () => {
    const blueInfo = customStorage.get("BlueInfo")
    const wifiInfo = customStorage.get("WifiInfo")
    if (!blueInfo && !wifiInfo) {
      printConfigStatus.value = '未设置'
    } else {
      printConfigStatus.value = '已设置'
    }
  }
	// 初始化页面
	const initPage = () => {
		const userInfo = customStorage.get("AccountInfo")
		if (userInfo) {
		}
			getUserInfoApi(userInfo)
    // 刷新打印机配置
    getPrintConfigStatus()
	}
	// 经营账户
	const handleAccountInfo = () => {
    uni.navigateTo({
      url: '/page_mine/accountInfo',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
      }
    })
	}
	// 账号资料
	const handleUserInfo = () => {
		uni.navigateTo({
			url: '/page_mine/userInfo',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
			}
		})
	}
	// 网点资料
	const handleNetworkInfo = () => {
		uni.navigateTo({
			url: '/page_mine/networkInfo',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
			}
		})
	}
	// 网点等级
	const handleNetworkLevel = () => {

	}
	// 打印机配置
	const handlePrintSetting = () => {
    // 刷新打印机配置
    getPrintConfigStatus()
		uni.navigateTo({
			url: '/page_mine/printSetting',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
			}
		})
	}
  // 邀新
  const handleInviteNetwork = () => {
    uni.navigateTo({
      url: '/page_mine/myNetworkList',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
      }
    })
  }
  // 审核
  const handleAuditNetwork = () => {
    uni.navigateTo({
      url: '/page_mine/myPrincipalApprovalList',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
      }
    })
  }
  // 注册
  const handleRegisterNetwork = () => {
    uni.navigateTo({
      url: '/page_mine/networkRegister'
    })
  }
	// 隐私协议
	function handlePrivacy() {
		uni.navigateTo({
			url: `/page_word/userPrivacy`
		});
	};
	// 用户协议
	function handleUserAgrement() {
		uni.navigateTo({
			url: `/page_word/userService`
		});
	};
	// 关于我们
	function handleAboutUs() {
		uni.navigateTo({
			url: `/page_word/aboutUs`
		});
		
	};

  // 负责人认证判断内容
  const principal = ref({
    showPrincipalAudit: false,
    principalAuditContent: '',
    principalAuditConfirmText: ''
  })
  // 判断是否需要进行个人认证
  const checkPrincipalAudit = async () => {
    principal.value = await getPrincipalAuditCheckResult()
  }

	onMounted(() => {
    checkPrincipalAudit()
		initPage()
	});
  onLoad(() => {
    // 监听事件
    uni.$on('printerChange',d=>{
      console.log(d.msg)
      // 刷新打印机配置
      getPrintConfigStatus()
    })
    // 监听个人认证返回
    uni.$on('childFnPrincipal',d=>{
      checkPrincipalAudit()
    })
  });
  onUnload(() => {
    // 移除监听事件
    uni.$off('printerChange');
  });

</script>

<style scope lang="scss">
	.layout-mine {
		background-color: #f1f1f1;
    height: calc(100vh - var(--window-bottom) - var(--window-top));
		overflow: auto;
		padding: 20rpx;

		.layout-mine-essential {
			background-color: #fff;
			border-radius: 5px;
			margin-bottom: 10rpx;

			:deep(.u-cell__body) {
				height: 120rpx;
			}

			.custom-cell-title {
				display: flex;

				.custom-cell-title-item {
					margin-left: 26rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
				}
			}
		}
	}
</style>