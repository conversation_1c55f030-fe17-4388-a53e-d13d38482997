<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">

					<up-form-item label="网点名称" prop="name" borderBottom="true">
            <up-input v-model="formData.name" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item v-if="formType==='create'" label="网点地址" prop="address">
            <up-input v-model="formData.address" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item v-if="formType!=='create'" label="网点地址" prop="address" borderBottom="true">
            <up-input v-model="formData.address" border="none" inputAlign="right"></up-input>
					</up-form-item>
          <up-form-item v-if="formType!=='create'" label="创建时间" prop="createTime" borderBottom="true">
            <view style="text-align: right">{{ formatDate(formData.createTime, 'yyyy-MM-dd HH:mm:ss') }}</view>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="负责人" prop="principalName" borderBottom="true">
            <view style="text-align: right">{{ formData.principalName }}</view>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="联系方式" prop="principalPhone" borderBottom="true">
            <view style="text-align: right">{{ formData.principalPhone }}</view>
          </up-form-item>
          <up-form-item v-if="formType!=='create' && !formData.principalPhone" label="" prop="principalPhone">
            <view style="text-align: center">邀新码</view>
            <view style="display: flex; align-items: center; justify-content: center;">
              <canvas id="imgCanvas" type="2d" style="width:234px;height:234px;" />
              <view class="disapleBox">
                <w-qrcode :options="qrcodeOptions" ref="qrcodeRef" @generate="handleGenerateQr"></w-qrcode>
              </view>
            </view>
          </up-form-item>
				</up-form>
			</view>
		</view>
		<view class="custom-footer" >
			<view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
  import {
    saveNetworkInfoApp
  } from "@/page_mine/api/sub_mine";
	import modal from '@/plugins/modal'
  import {formatDate} from "@/api/common";

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    id: undefined,
    name: undefined,
    address: undefined,
    createTime: undefined,
    principalName: undefined,
    principalPhone: undefined,
  })

  const inviteQrCodePre = ref('') // 邀新码前缀

	// 提交信息
	const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }
    let data = {...formData.value}
    // 编辑时后端只保存名称和地址
		await saveNetworkInfoApp(data).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFn', true);
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}

  // 校验表单
  const validateForm = () => {
    if (!formData.value.name) {
      modal.msgError('请输入网点名称')
      return false
    }
    if (!formData.value.address) {
      modal.msgError('请输入网点地址')
      return false
    }
    return true
  }

	// 初始化页面
	const initPage = (obj) => {
    // console.log(obj)
    // 动态改变顶部标题
    uni.setNavigationBarTitle({
      title: obj.type === 'create' ? '创建网点' : '编辑网点'
    });
		if (obj.data) {
			formType.value = obj.type
			formData.value = {...obj.data}

      if (obj.data.inviteQrCodePre) {
        inviteQrCodePre.value = obj.data.inviteQrCodePre
      }
      if (obj.data.id) {
        makeQrCodeImg(obj.data.id)
      }
    }
	}

  // 二维码
  const qrcodeRef = ref(null)
  const qrcodeTemplatePath = ref('')
  const qrcodeOptions = ref({ // 生成二维码的值
    code: '1',
    text: { //二维码绘制文字 非必传
      opacity: 1, //文字透明度 默认不透明1  0~1 非必传
      font: 'bold 20px system-ui', //文字是否加粗 默认normal 20px system-ui 非必传
      color: ["#000000"], // 文字颜色 多个颜色支持渐变色 默认黑色 非必传
      content: "" //文字内容
    },
    size: 110, //生成的二维码的宽高
    padding: 8,
    type: 'none',
    color: ['#000']
  })
  const handleGenerateQr = async () => {
    const res = await qrcodeRef.value.GetCodeImg()
    qrcodeTemplatePath.value = res.tempFilePath
    console.log('二维码qrcodeTemplatePath=', qrcodeTemplatePath.value)
  }
  // 制作二维码
  const makeQrCodeImg = async (networkId) => {
    //创建面单画布
    await wx.createSelectorQuery()
        .select('#imgCanvas')
        .fields({
          node: true,
          size: true
        })
        .exec(async (res) => {
          // Canvas 对象
          const canvas = res[0].node
          // Canvas 画布的实际绘制宽高
          const canvasWidth = 234
          const canvasHeight = 234
          // 绘制上下文
          const ctx = canvas.getContext('2d')
          // 初始化画布大小
          const dpr = wx.getWindowInfo().pixelRatio
          canvas.width = canvasWidth * 2
          canvas.height = canvasHeight * 2
          ctx.scale(2, 2)
          // 清空画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.fillStyle = "#ffffff"
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          const qrcodeWidth = 234
          const qrcodeHeight = 234
          // 绘制
          qrcodeOptions.value.code = inviteQrCodePre.value + networkId
          qrcodeOptions.value.size = 234
          await handleGenerateQr()
          setTimeout(() => {
            //console.log("二维码生成")
            //console.log("二维码地址：", qrcodeTemplatePath.value)
            if (qrcodeTemplatePath.value) {
              const image = canvas.createImage()
              image.onload = () => {
                ctx.drawImage(image, 0, 0, qrcodeWidth, qrcodeHeight)
              }
              image.src = qrcodeTemplatePath.value
            }
          }, 500)
        })
  }

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">

.disapleBox {
  //display: flex;
  position: absolute;
  top: -1000px;
  left: -200;
}
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

</style>