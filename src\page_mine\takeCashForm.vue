<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
					<up-form-item label="提现账户" prop="accountId" borderBottom="true">
<!--            <up-input v-model="formData.accountName" border="none" inputAlign="right"
                      readonly></up-input>-->
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowAccountSelect" >
                <text>{{ formData.accountName }}</text>
                <up-icon v-if="formType==='create'" name="arrow-right" size="20"></up-icon>
              </view>
              </template>
					</up-form-item>
          <up-form-item v-if="selectedAccount && formType==='create'" label="" prop="amountInfo" borderBottom="true">
              <up-row>
                <up-col span="12">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="总金额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.totalAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
              </up-row>
              <up-row>
                <up-col span="6">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="可用余额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.availableAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
                <up-col span="6">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="冻结金额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.freezeAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
              </up-row>
          </up-form-item>
          <up-form-item label="申请金额" prop="takeAmount" borderBottom="true">
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowAmountSelect" >
                <text>{{ formData.takeAmountLabel }}</text>
                <up-icon v-if="formType==='create'" name="arrow-right" size="20"></up-icon>
              </view>
            </template>
          </up-form-item>
					<up-form-item v-if="formType==='create'" label="收款码" prop="payQrCode" borderBottom="true">
            <up-album :urls="formData.payQrCode"></up-album>
					</up-form-item>
					<up-form-item v-if="formType==='create'" label="" prop="tips" borderBottom="true">
            <view style="color: #606266;text-align: center;">请确认收款码是否正确，以免影响提现结果！</view>
					</up-form-item>
					<up-form-item v-if="formType==='create'" label="交易密码" prop="accountPwd" >
            <uni-easyinput type="password" v-model="formData.accountPwd" placeholder="请输入交易密码"
                           style="text-align: right;" :inputBorder="false"
            ></uni-easyinput>
					</up-form-item>
          <up-form-item v-if="formType!=='create'" label="申请时间" prop="applyTimeLabel" borderBottom="true">
            <up-input v-model="formData.applyTimeLabel" border="none" inputAlign="right" readonly></up-input>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="申请状态" prop="applyStatus" borderBottom="true">
            <view style="text-align: right">{{ getTakeCashApplyStatusLabel(formData.applyStatus) }}</view>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="支付凭证" prop="certUrl" >
            <up-album :urls="certUrlList"></up-album>
          </up-form-item>
				</up-form>
			</view>
		</view>
		<view class="custom-footer" v-if="formType==='create'">
			<view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>

    <up-action-sheet :show="showAccountSelect" :actions="radiosAccountIdList" title="请选择提现账户" @close="showAccountSelect=false"
                     @select="handleAccountSelect">
    </up-action-sheet>
    <up-action-sheet :show="showAmountSelect" :actions="radiosAmountList" title="请选择申请金额" @close="showAmountSelect=false"
                     @select="handleAmountSelect">
    </up-action-sheet>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
  import {
    accountListSingle,
    getAccountTypeLabel,
    getMainQrCode,
    createTakeCashApply,
    getTakeCashApplyStatusLabel
  } from "@/page_mine/api/sub_mine";
	import modal from '@/plugins/modal'
  import {formatDate} from "@/api/common";

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    id: undefined,
    accountRole: '2',
    mainId: undefined,
    accountId: undefined,
    accountName: undefined,
    takeAmount: '1000',
    takeAmountLabel: undefined,
    certUrl: undefined,
    applyUserId: undefined,
    applyTime: undefined,
    applyTimeLabel: undefined,
    applyStatus: undefined,
    auditUserId: undefined,
    auditRemark: undefined,
    payQrCode: undefined,
    accountPwd: undefined,
  })
  // 支付凭证图片
  const certUrlList = ref([])

  const radiosAccountIdList = ref([]) // 账户列表
  const selectedAccount = ref(undefined) // 选中的账户
  const showAccountSelect = ref(false) // 显示账户选择
  // 显示账户选择
  const handleShowAccountSelect = () => {
    if(formType.value==='create') {
      showAccountSelect.value=true
    }
  }
  // 选中账户
  const handleAccountSelect = (item) => {
    formData.value.accountId = item.value
    formData.value.accountName = item.name
    selectedAccount.value = item
    showAccountSelect.value = false
  }

  // 金额列表
  const radiosAmountList = ref([
    {
      value: '1000',
      name: '1,000元'
    }, {
      value: '2000',
      name: '2,000元'
    }, /*{
      value: '3000',
      name: '3,000元'
    }, {
      value: '5000',
      name: '5,000元'
    },*/
  ])
  const showAmountSelect = ref(false) // 显示金额选择
  // 显示金额选择
  const handleShowAmountSelect = () => {
    if(formType.value==='create') {
      showAmountSelect.value=true
    }
  }
  // 选中金额
  const handleAmountSelect = (item) => {
    formData.value.takeAmount = item.value
    formData.value.takeAmountLabel = item.name
    formData.value.payQrCode = [item.url]
    if (!item.url) {
      modal.msgError('请先上传对应金额的收款码，以免影响提现结果！')
    }
    showAmountSelect.value = false
  }

	// 提交信息
	const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }
    let data = {...formData.value}
		await createTakeCashApply(data).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFn', true);
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}

  // 校验表单
  const validateForm = () => {
    if (!formData.value.accountId) {
      modal.msgError('请选择提现账户')
      return false
    }
    if (!formData.value.takeAmount) {
      modal.msgError('请选择申请金额')
      return false
    }
    if (!formData.value.accountPwd) {
      modal.msgError('请输入交易密码')
      return false
    }
    return true
  }

	// 初始化页面
	const initPage = (obj) => {
    // console.log(obj)
		if (obj.data) {
			formType.value = obj.type
			formData.value = {...obj.data}
      if (formType.value === 'create') {
        formData.value.takeAmount = 1000
      }
      formData.value.applyTimeLabel = formatDate(formData.value.applyTime, 'yyyy-MM-dd HH:mm:ss')

      accountListSingle({accountRole: '2', mainId: formData.value.mainId}).then(res => {
        if (res && res.data) {
          for (let item of res.data) {
            if (['NETWORK_FREIGHT_PRE','NETWORK_FREIGHT_DIVIDE'].indexOf(item.accountType) < 0) {
              continue
            }
            item.name = getAccountTypeLabel(item.accountType)
            item.checked = false
            item.value = item.id
            radiosAccountIdList.value.push(item)
          }
          if (!formData.value.accountId) {
            formData.value.accountId = res.data[0].id
          }
          selectedAccount.value = radiosAccountIdList.value.find(item => item.value === formData.value.accountId)
          formData.value.accountName = radiosAccountIdList.value.find(item => item.value === formData.value.accountId)?.name
        }
      })
      getMainQrCode({accountRole: '2', mainId: formData.value.mainId}).then(res => {
        if (res && res.data) {
          for (let item of radiosAmountList.value) {
            if (item.value === '1000') {
              item.url = res.data.receiveOne
            } else if (item.value === '2000') {
              item.url = res.data.receiveTwo
            } else if (item.value === '3000') {
              item.url = res.data.receiveThree
            } else if (item.value === '5000') {
              item.url = res.data.receiveFive
            }
          }
          if (formData.value.takeAmount === 1000) {
            formData.value.payQrCode = [res.data.receiveOne]
          } else if (formData.value.takeAmount === 2000) {
            formData.value.payQrCode = [res.data.receiveTwo]
          } else if (formData.value.takeAmount === 3000) {
            formData.value.payQrCode = [res.data.receiveThree]
          } else if (formData.value.takeAmount === 5000) {
            formData.value.payQrCode = [res.data.receiveFive]
          }
        }
      })
      formData.value.takeAmountLabel = radiosAmountList.value.find(item => item.value === formData.value.takeAmount || item.value === formData.value.takeAmount + '')?.name
      if (formData.value.certUrl) {
        certUrlList.value=[formData.value.certUrl]
      }
    }
	}

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

</style>