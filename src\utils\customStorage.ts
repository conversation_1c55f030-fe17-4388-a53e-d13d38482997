const customStorage = {
	get: function (key : string) {
		return uni.getStorageSync(key)
	},
	set: function (key : string, data : any) {
		uni.setStorageSync(key, data)
	},
	remove: function(key : string){
		uni.removeStorageSync(key)
	},
	clear: function(){
		uni.clearStorageSync()
	},
	clearWithoutLoginForm: function(){
		let loginFormData = uni.getStorageSync("loginFormData")
		uni.clearStorageSync()
		uni.setStorageSync("loginFormData", loginFormData)
	}
}

export default customStorage