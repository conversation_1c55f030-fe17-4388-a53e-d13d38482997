const TokenKey = 'App-Token'
const TenantId = 'tenant-id'

export function getToken():string {
  return uni.getStorageSync(TokenKey)
}

export function setToken(token:string) {
  return uni.setStorageSync(TokenKey, token)
}

export function removeToken() {
  return uni.removeStorageSync(TokenKey)
}

export const getTenantId = () => {
  const tenantId = uni.getStorageSync(TenantId)
  return tenantId ? tenantId : 1
}

export const setTenantId = (tenantId: string) => {
  uni.setStorageSync(TenantId, tenantId)
}