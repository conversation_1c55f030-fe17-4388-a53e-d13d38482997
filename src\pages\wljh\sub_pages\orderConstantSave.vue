<template>
	<view class="layout-sub-goodsSave">
		<view class="custom-body">
			<up-form labelPosition="left" ref="formRef" :model="orderInfo" labelWidth="auto">
        <view class="name-title">发件人</view>
				<up-form-item label="名称" prop="sendName" borderBottom="true" class="no-bottom-input">
					<up-input v-model="orderInfo.sendName" border="none" inputAlign="right" clearable></up-input>
				</up-form-item>
				<up-form-item label="手机号" prop="sendPhone" borderBottom="true" class="no-bottom-input">
					<up-input v-model="orderInfo.sendPhone" border="none" inputAlign="right" type="idcard" clearable ></up-input>
				</up-form-item>
				<up-form-item label="身份证号" prop="sendIdCard" borderBottom="true" class="no-bottom-input">
					<up-input v-model="orderInfo.sendIdCard" border="none" inputAlign="right" type="idcard" clearable ></up-input>
				</up-form-item>

        <!-- 级联地址 -->
        <up-form-item label="地址" prop="sendAddressOne" borderBottom="true">
          <view class="addressRow">
            <up-row>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.sendAddressOne" placeholder="省" :localdata="sendAddress1Arr" @change="addressChange(1, '1', $event)"></uni-data-select>
              </up-col>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.sendAddressTwo" placeholder="市" :localdata="sendAddress2Arr" @change="addressChange(2, '1', $event)"></uni-data-select>
              </up-col>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.sendAddressThree" placeholder="区/县" :localdata="sendAddress3Arr" @change="addressChange(3, '1', $event)"></uni-data-select>
              </up-col>
            </up-row>
          </view>
          <view class="addressRow">
            <up-row>
              <up-col :span="6">
                <uni-data-select v-model="orderInfo.sendAddressFour" placeholder="乡/镇" :localdata="sendAddress4Arr" @change="addressChange(4, '1', $event)"></uni-data-select>
              </up-col>
              <up-col :span="6">
                <uni-data-select v-model="orderInfo.sendAddressFive" placeholder="村/社区" :localdata="sendAddress5Arr" @change="addressChange(5, '1', $event)"></uni-data-select>
              </up-col>
            </up-row>
          </view>
        </up-form-item>
				<up-form-item label="详细地址" prop="sendAddress" borderBottom="true">
					<up-input v-model="orderInfo.sendAddress" border="none" inputAlign="right" clearable></up-input>
				</up-form-item>
        <view class="name-title">收件人</view>
        <up-form-item label="名称" prop="collectName" borderBottom="true" class="no-bottom-input">
          <up-input v-model="orderInfo.collectName" border="none" inputAlign="right" clearable></up-input>
        </up-form-item>
        <up-form-item label="手机号" prop="collectPhone" borderBottom="true" class="no-bottom-input">
          <up-input v-model="orderInfo.collectPhone" border="none" inputAlign="right" type="idcard" clearable ></up-input>
        </up-form-item>

        <!-- 级联地址 -->
        <up-form-item label="地址" prop="collectAddressOne" borderBottom="true">
          <view class="addressRow">
            <up-row>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.collectAddressOne" placeholder="省" :localdata="collectAddress1Arr" @change="addressChange(1, '2', $event)"></uni-data-select>
              </up-col>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.collectAddressTwo" placeholder="市" :localdata="collectAddress2Arr" @change="addressChange(2, '2', $event)"></uni-data-select>
              </up-col>
              <up-col :span="4">
                <uni-data-select v-model="orderInfo.collectAddressThree" placeholder="区/县" :localdata="collectAddress3Arr" @change="addressChange(3, '2', $event)"></uni-data-select>
              </up-col>
            </up-row>
          </view>
          <view class="addressRow">
            <up-row>
              <up-col :span="6">
                <uni-data-select v-model="orderInfo.collectAddressFour" placeholder="乡/镇" :localdata="collectAddress4Arr" @change="addressChange(4, '2', $event)"></uni-data-select>
              </up-col>
              <up-col :span="6">
                <uni-data-select v-model="orderInfo.collectAddressFive" placeholder="村/社区" :localdata="collectAddress5Arr" @change="addressChange(5, '2', $event)"></uni-data-select>
              </up-col>
            </up-row>
          </view>
        </up-form-item>
        <up-form-item label="详细地址" prop="collectAddress" borderBottom="true">
          <up-input v-model="orderInfo.collectAddress" border="none" inputAlign="right" clearable></up-input>
        </up-form-item>

				<up-form-item label="货品名称" prop="goodsType" borderBottom="true">
					<up-input v-model="orderInfo.goodsTypeLabel" border="none" inputAlign="right"></up-input>
					<template #right>
						<up-icon name="arrow-right" @click="handleGoodsTypeFn" size="20"></up-icon>
					</template>
				</up-form-item>
				<up-form-item label="总重量" prop="totalWeight" borderBottom="true">
					<!-- <up-number-box v-model="orderInfo.totalWeight" min="0"></up-number-box> -->
					<up-input v-model="orderInfo.totalWeight" inputAlign="right" placeholder="总重量" type="digit" border="none" >
						<template #suffix>
							<span>Kg</span>
						</template>
					</up-input>
					<!-- <template #right><span>Kg</span></template> -->
				</up-form-item>
				<view class="custom-body-form-row">
					<up-form-item label="总体积" prop="totalVolume" borderBottom="true">
						<view class="custom-body-form-item">
							<view class="custom-body-form-item-line ">
								<up-input type="digit" v-model="orderInfo.totalVolume" inputAlign="right"
									placeholder="总体积" border="none" >
									<template #suffix>
										<span>m³</span>
									</template>
								</up-input>
							</view>
              <!--<view class="custom-body-form-item-line custom-body-form-item-input">
                <up-input v-model="orderInfo.goodsLong" inputAlign="right" placeholder="长" type="digit">
                  <template #suffix>
                    <span>cm</span>
                  </template>
                </up-input>
                <up-input v-model="orderInfo.goodsWidth" inputAlign="right" placeholder="宽"
                  type="digit">
                  <template #suffix>
                    <span>cm</span>
                  </template>
                </up-input>
                <up-input v-model="orderInfo.goodsHeight" inputAlign="right" placeholder="高"
                  type="digit">
                  <template #suffix>
                    <span>cm</span>
                  </template>
                </up-input>
              </view>-->
            </view>
          </up-form-item>
        </view>
        <up-form-item label="总件数" prop="totalNum" borderBottom="true">
          <!-- <up-number-box v-model="orderInfo.totalNum" min="0"></up-number-box> -->
					<up-input v-model="orderInfo.totalNum" inputAlign="right" placeholder="总件数" type="number" border="none" >
						<template #suffix>
							<span>件</span>
						</template>
					</up-input>
					<!-- <template #right><span>件</span></template> -->
				</up-form-item>
				<!-- <up-form-item  label="包装方式" borderBottom="true">
					
				</up-form-item>
				<up-scroll-list  :indicator="false">
						<view class="custom-card-body-item-tag" v-for="(item, index) in packMethodList"
							:key="index">
							<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'" :text="item.name"
								size="mini" :plain="!item.checked" :name="index"
								@click="handlePopupCardFn(item, index)"></up-tag>
						</view>
					</up-scroll-list> -->
				<!-- 包装方式 -->
				<view class="custom-card-body-item border-bottom-line custom_line">
					<view class="custom-card-body-item-lable">
						<text >包装方式</text>
					</view>

					<up-scroll-list :indicator="false">
						<view class="custom-card-body-item-tag" v-for="(item, index) in packMethodList" :key="index">
							<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'" :text="item.name"
								size="mini" :plain="!item.checked" :name="index"
								@click="handlePopupCardFn(item, index)">
							</up-tag>
						</view>
					</up-scroll-list>
				</view>

        <up-line color="#d6d7d9"></up-line>
			</up-form>


			<view class="custom-btn">
				<up-button type="primary" text="确定" @click="handleSubmit"></up-button>
			</view>

			<up-action-sheet :show="showSheet" :actions="actionsSheet" title="请选择货品名称" @close="handleActionSheetClose"
				@select="handleActionSheetSelect">
			</up-action-sheet>
		</view>
	</view>
</template>

<script setup>
import {
	reactive,
	getCurrentInstance,
	ref,
	onMounted
} from 'vue'
import {levelSearchAddress} from "@/api/wljh/home";

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const orderInfo = ref({
  collectName: undefined,
  collectPhone: undefined,
  collectAddress: undefined,
  sendName: undefined,
  sendPhone: undefined,
  sendIdCard: undefined,
  sendAddress: undefined,
  sendAddressOne: undefined,
  sendAddressTwo: undefined,
  sendAddressThree: undefined,
  sendAddressFour: undefined,
  sendAddressFive: undefined,
  collectAddressOne: undefined,
  collectAddressTwo: undefined,
  collectAddressThree: undefined,
  collectAddressFour: undefined,
  collectAddressFive: undefined,
	goodsType: undefined,
	goodsTypeLabel: undefined,
	totalWeight: undefined,
	totalVolume: undefined,
	totalNum: undefined,
	goodsLong: undefined,
	goodsWidth: undefined,
	goodsHeight: undefined,
})
const actionsSheet = ref([]) // 选择参数值
const showSheet = ref(false) // 选择参数框是否显示
const packMethodList = ref([{ // 包装方式
	name: '箱',
	value: '1',
	dictType: 'Pack',
	checked: false
},
{
	name: '纸',
	value: '2',
	dictType: 'Pack',
	checked: false
},
{
	name: '袋',
	value: '3',
	dictType: 'Pack',
	checked: false
},
{
	name: '桶',
	value: '4',
	dictType: 'Pack',
	checked: false
},
{
	name: '木',
	value: '5',
	dictType: 'Pack',
	checked: false
},
{
	name: '膜',
	value: '6',
	dictType: 'Pack',
	checked: false
},
{
	name: '皮',
	value: '7',
	dictType: 'Pack',
	checked: false
},
{
	name: '布',
	value: '8',
	dictType: 'Pack',
	checked: false
}

])
const sendAddress1Arr = ref([])
const sendAddress2Arr = ref([])
const sendAddress3Arr = ref([])
const sendAddress4Arr = ref([])
const sendAddress5Arr = ref([])
const collectAddress1Arr = ref([])
const collectAddress2Arr = ref([])
const collectAddress3Arr = ref([])
const collectAddress4Arr = ref([])
const collectAddress5Arr = ref([])


// 物品类型点击事件
const handleGoodsTypeFn = () => {
	showSheet.value = true
  // getDictDataInfo('GOODS_TYPE')
  // 物品类型数据
  const goodsTypeData = [
        {
          "id": 1598,
          "label": "食品饮料",
          "value": "GOODS_TYPE_FOOD_BEVERAGE",
          "dictType": "GOODS_TYPE"
        },
          {
            "id": 1599,
            "label": "服装",
            "value": "GOODS_TYPE_CLOTHING",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1600,
            "label": "饰品",
            "value": "GOODS_TYPE_ORNAMENTS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1601,
            "label": "玩具",
            "value": "GOODS_TYPE_TOYS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1602,
            "label": "包包",
            "value": "GOODS_TYPE_BAG",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1603,
            "label": "鞋子",
            "value": "GOODS_TYPE_SHOES",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1604,
            "label": "书籍",
            "value": "GOODS_TYPE_BOOKS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1605,
            "label": "数码配件",
            "value": "GOODS_TYPE_DIGITAL_ACCESSORIES",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1606,
            "label": "居家用品",
            "value": "GOODS_TYPE_HOUSE_ITEM",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1607,
            "label": "液体",
            "value": "GOODS_TYPE_LIQUID",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1608,
            "label": "药品",
            "value": "GOODS_TYPE_DRUGS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1609,
            "label": "植物",
            "value": "GOODS_TYPE_PLANT",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1610,
            "label": "化妆品",
            "value": "GOODS_TYPE_COSMETICS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1611,
            "label": "电子电器",
            "value": "GOODS_TYPE_WEEE",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1612,
            "label": "磁性用品",
            "value": "GOODS_TYPE_MAGNETIC_PRODUCTS",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1613,
            "label": "粉末/颗粒",
            "value": "GOODS_TYPE_PARTICLE",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1614,
            "label": "情趣用品",
            "value": "GOODS_TYPE_SEX_TOY",
            "dictType": "GOODS_TYPE"
          },
          {
            "id": 1615,
            "label": "其他",
            "value": "GOODS_TYPE_OTHER",
            "dictType": "GOODS_TYPE"
          }
      ]
  actionsSheet.value = goodsTypeData.map(item => {
    let param = {
      'name': item.label,
      'text': item.value,
      'dictType': 'GOODS_TYPE'
    }
    return param
  })

}
// 选择参数框关闭事件
const handleActionSheetClose = () => {
	showSheet.value = false
	actionsSheet.value = []
}
// 选择参数框选中事件
const handleActionSheetSelect = (item) => {
	switch (item.dictType) {
		case 'GOODS_TYPE':
			orderInfo.value.goodsTypeLabel = item.name
			orderInfo.value.goodsType = item.text
			break;
	}
}
const handlePopupCardFn = (item, index) => {
	item.checked = !item.checked
	packMethodList.value.map((obj, inx) => {
		obj.checked = index != inx ? false : item.checked
	})
	orderInfo.value.packMethod = item.checked ? item.value : undefined
}


/** 地址变化 type 1-发货 2-收货 */
const addressChange = (level, type, value) => {
  console.log(value)
  if (type === '1') {
    if (level <= 4) {
      sendAddress5Arr.value = []
      orderInfo.value.sendAddressFive = undefined
    }
    if (level <= 3) {
      sendAddress4Arr.value = []
      orderInfo.value.sendAddressFour = undefined
    }
    if (level <= 2) {
      sendAddress3Arr.value = []
      orderInfo.value.sendAddressThree = undefined
    }
    if (level === 1) {
      sendAddress2Arr.value = []
      orderInfo.value.sendAddressTwo = undefined
    }
  } else {
    if (level <= 4) {
      collectAddress5Arr.value = []
      orderInfo.value.collectAddressFive = undefined
    }
    if (level <= 3) {
      collectAddress4Arr.value = []
      orderInfo.value.collectAddressFour = undefined
    }
    if (level <= 2) {
      collectAddress3Arr.value = []
      orderInfo.value.collectAddressThree = undefined
    }
    if (level === 1) {
      collectAddress2Arr.value = []
      orderInfo.value.collectAddressTwo = undefined
    }

  }
  // 加载下级地址
  if (level >= 1 && level < 5 && value) {
    if (value.no) {
      loadLevelAddress(level + 1, type, value.no)
    }
  }

}

/** 加载指定层级地址数据 */
const loadLevelAddress = async (level, type, parentNo) => {
  await levelSearchAddress(level, parentNo).then(res => {
    const newAddress = res.data.map(item => {
      return {
        text: item.name,
        value: item
      }
    })
    if (type === '1') {
      if (level === 1) {
        sendAddress1Arr.value = newAddress
        // 回显一级地址
        if (orderInfo.value.sendAddressOne) {
          sendAddress1Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.sendAddressOne.name || item.value.name === orderInfo.value.sendAddressOne) {
              orderInfo.value.sendAddressOne = item.value
              loadLevelAddress(2, type, orderInfo.value.sendAddressOne.no)
            }
          })
        }
      } else if (level === 2) {
        sendAddress2Arr.value = newAddress
        // 回显二级地址
        if (orderInfo.value.sendAddressTwo) {
          sendAddress2Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.sendAddressTwo.name || item.value.name === orderInfo.value.sendAddressTwo) {
              orderInfo.value.sendAddressTwo = item.value
              loadLevelAddress(3, type, orderInfo.value.sendAddressTwo.no)
            }
          })
        }
      } else if (level === 3) {
        sendAddress3Arr.value = newAddress
        // 回显三级地址
        if (orderInfo.value.sendAddressThree) {
          sendAddress3Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.sendAddressThree.name || item.value.name === orderInfo.value.sendAddressThree) {
              orderInfo.value.sendAddressThree = item.value
              loadLevelAddress(4, type, orderInfo.value.sendAddressThree.no)
            }
          })
        }
      } else if (level === 4) {
        sendAddress4Arr.value = newAddress
        // 回显四级地址
        if (orderInfo.value.sendAddressFour) {
          sendAddress4Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.sendAddressFour.name || item.value.name === orderInfo.value.sendAddressFour) {
              orderInfo.value.sendAddressFour = item.value
              loadLevelAddress(5, type, orderInfo.value.sendAddressFour.no)
            }
          })
        }
      } else if (level === 5) {
        sendAddress5Arr.value = newAddress
        // 回显五级地址
        if (orderInfo.value.sendAddressFive) {
          sendAddress5Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.sendAddressFive.name || item.value.name === orderInfo.value.sendAddressFive) {
              orderInfo.value.sendAddressFive = item.value
            }
          })
        }
      }
    } else {
      if (level === 1) {
        collectAddress1Arr.value = newAddress
        // 回显一级地址
        if (orderInfo.value.collectAddressOne) {
          collectAddress1Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.collectAddressOne.name || item.value.name === orderInfo.value.collectAddressOne) {
              orderInfo.value.collectAddressOne = item.value
              loadLevelAddress(2, type, orderInfo.value.collectAddressOne.no)
            }
          })
        }
      } else if (level === 2) {
        collectAddress2Arr.value = newAddress
        // 回显二级地址
        if (orderInfo.value.collectAddressTwo) {
          collectAddress2Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.collectAddressTwo.name || item.value.name === orderInfo.value.collectAddressTwo) {
              orderInfo.value.collectAddressTwo = item.value
              loadLevelAddress(3, type, orderInfo.value.collectAddressTwo.no)
            }
          })
        }
      } else if (level === 3) {
        collectAddress3Arr.value = newAddress
        // 回显三级地址
        if (orderInfo.value.collectAddressThree) {
          collectAddress3Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.collectAddressThree.name || item.value.name === orderInfo.value.collectAddressThree) {
              orderInfo.value.collectAddressThree = item.value
              loadLevelAddress(4, type, orderInfo.value.collectAddressThree.no)
            }
          })
        }
      } else if (level === 4) {
        collectAddress4Arr.value = newAddress
        // 回显四级地址
        if (orderInfo.value.collectAddressFour) {
          collectAddress4Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.collectAddressFour.name || item.value.name === orderInfo.value.collectAddressFour) {
              orderInfo.value.collectAddressFour = item.value
              loadLevelAddress(5, type, orderInfo.value.collectAddressFour.no)
            }
          })
        }
      } else if (level === 5) {
        collectAddress5Arr.value = newAddress
        // 回显五级地址
        if (orderInfo.value.collectAddressFive) {
          collectAddress5Arr.value.forEach(item => {
            if (item.value.name === orderInfo.value.collectAddressFive.name || item.value.name === orderInfo.value.collectAddressFive) {
              orderInfo.value.collectAddressFive = item.value
            }
          })
        }
      }
    }

  })
}

// 确定事件
const handleSubmit = () => {
  let data = {...orderInfo.value}
  if (data.sendAddressOne && data.sendAddressOne.name) data.sendAddressOne = data.sendAddressOne.name
  if (data.sendAddressTwo && data.sendAddressTwo.name) data.sendAddressTwo = data.sendAddressTwo.name
  if (data.sendAddressThree && data.sendAddressThree.name) data.sendAddressThree = data.sendAddressThree.name
  if (data.sendAddressFour && data.sendAddressFour.name) data.sendAddressFour = data.sendAddressFour.name
  if (data.sendAddressFive && data.sendAddressFive.name) data.sendAddressFive = data.sendAddressFive.name
  if (data.collectAddressOne && data.collectAddressOne.name) data.collectAddressOne = data.collectAddressOne.name
  if (data.collectAddressTwo && data.collectAddressTwo.name) data.collectAddressTwo = data.collectAddressTwo.name
  if (data.collectAddressThree && data.collectAddressThree.name) data.collectAddressThree = data.collectAddressThree.name
  if (data.collectAddressFour && data.collectAddressFour.name) data.collectAddressFour = data.collectAddressFour.name
  if (data.collectAddressFive && data.collectAddressFive.name) data.collectAddressFive = data.collectAddressFive.name
  // 发送保存请求

  // 父页面发送数据
  eventChannel.emit('orderConstantChild', data);
  uni.navigateBack()
}

onMounted(() => {
	// 接收数据
	eventChannel.on('orderConstantParent', function (data) {
		console.log(data)
		if (data.goodsType) {
			// getDictLable('GOODS_TYPE', data.goodsType)
		}
		orderInfo.value = {...data}
    // 选中包装方式
    packMethodList.value.map(item => {
      item.checked = orderInfo.value.packMethod === item.value
    })
    // 地址回显
    if (orderInfo.value.sendAddressOne) {
      const addressValue = {no: '', name: orderInfo.value.sendAddressOne}
      sendAddress1Arr.value = [{value: addressValue, text: orderInfo.value.sendAddressOne}]
      orderInfo.value.sendAddressOne = addressValue
    }
    if (orderInfo.value.sendAddressTwo) {
      const addressValue = {no: '', name: orderInfo.value.sendAddressTwo}
      sendAddress2Arr.value = [{value: addressValue, text: orderInfo.value.sendAddressTwo}]
      orderInfo.value.asendAddressTwo = addressValue
    }
    if (orderInfo.value.sendAddressThree) {
      const addressValue = {no: '', name: orderInfo.value.sendAddressThree}
      sendAddress3Arr.value = [{value: addressValue, text: orderInfo.value.sendAddressThree}]
      orderInfo.value.sendAddressThree = addressValue
    }
    if (orderInfo.value.addressFour) {
      const addressValue = {no: '', name: orderInfo.value.sendAddressFour}
      sendAddress4Arr.value = [{value: addressValue, text: orderInfo.value.sendAddressFour}]
      orderInfo.value.sendAddressFour = addressValue
    }
    if (orderInfo.value.sendAddressFive) {
      const addressValue = {no: '', name: orderInfo.value.sendAddressFive}
      sendAddress5Arr.value = [{value: addressValue, text: orderInfo.value.sendAddressFive}]
      orderInfo.value.sendAddressFive = addressValue
    }
    if (orderInfo.value.collectAddressOne) {
      const addressValue = {no: '', name: orderInfo.value.collectAddressOne}
      collectAddress1Arr.value = [{value: addressValue, text: orderInfo.value.collectAddressOne}]
      orderInfo.value.collectAddressOne = addressValue
    }
    if (orderInfo.value.collectAddressTwo) {
      const addressValue = {no: '', name: orderInfo.value.collectAddressTwo}
      collectAddress2Arr.value = [{value: addressValue, text: orderInfo.value.collectAddressTwo}]
      orderInfo.value.acollectAddressTwo = addressValue
    }
    if (orderInfo.value.collectAddressThree) {
      const addressValue = {no: '', name: orderInfo.value.collectAddressThree}
      collectAddress3Arr.value = [{value: addressValue, text: orderInfo.value.collectAddressThree}]
      orderInfo.value.collectAddressThree = addressValue
    }
    if (orderInfo.value.addressFour) {
      const addressValue = {no: '', name: orderInfo.value.collectAddressFour}
      collectAddress4Arr.value = [{value: addressValue, text: orderInfo.value.collectAddressFour}]
      orderInfo.value.collectAddressFour = addressValue
    }
    if (orderInfo.value.collectAddressFive) {
      const addressValue = {no: '', name: orderInfo.value.collectAddressFive}
      collectAddress5Arr.value = [{value: addressValue, text: orderInfo.value.collectAddressFive}]
      orderInfo.value.collectAddressFive = addressValue
    }

    try {
      // 加载一级地址数据
      loadLevelAddress(1, '1', '')
      loadLevelAddress(1, '2', '')
    } catch (e) {
      sendAddress1Arr.value = []
      collectAddress1Arr.value = []
    }
	})
})

</script>

<style scoped lang="scss">
.layout-sub-goodsSave {
	background-color: #f1f1f1;
	height: calc(100vh - var(--window-top));
	overflow: auto;
	padding: 0 20rpx;

	.custom-body {
		background-color: #fff;
		padding: 0 20rpx;
		padding-bottom: 20rpx;
		margin: 20rpx 0;
		border-radius: 5px;

		:deep(.u-form-item__body__right__content__slot) {
			padding-right: 50rpx;
		}

		:deep(.u-number-box) {
			padding: 0 60rpx;
		}

		:deep(.u-number-box__input) {
			width: auto !important;
		}

		:deep(.u-form-item__body__left__content__label) {
			color: #606266;
		}

    :deep(.u-form-item__body__right__content__slot) {
      display: block;
    }

		.custom-body-form-row {
			.custom-body-form-item {
				display: flex;
				width: 100%;
				flex-direction: column;
				gap: 20rpx;

				.total-volume-item-input {
					width: 89%;
					height: 60rpx;
					margin-left: auto;
				}

				.custom-body-form-item-line {
					display: flex;

				}

				.custom-body-form-item-input {
					display: flex;
					flex-wrap: nowrap;
					gap: 5px;
					// margin-left: 30px;
					height: 30px;
				}
			}
		}

		.custom-btn {
			margin-top: 20rpx;

			:deep(.u-button--square) {
				border-radius: 20px;
			}

			:deep(.u-button--primary) {
				background-color: #d81e06;
				border: #d81e06;
			}

			:deep(.u-button) {
				height: 45px;
			}
		}

		// :deep(.u-scroll-list) {
		// 	width: 100%;
		// }

		.custom_line {
			display: flex;
			height: 60px;
			align-items: center;
			.custom-card-body-item-lable {
				font-size: 16px;
				color: #606266;
				width: 150rpx;
				// margin-right: 6rpx;
			}

			.custom-card-body-item-tag {
				// padding-left: 120rpx;
				width: 50px;
				height: 100%;
				display: flex;
				align-items: center;
        margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 18px;
				}
			}
		}


	}


	:deep(.u-scroll-list) {
		width: calc(100% - 220rpx);
		padding-bottom: 0;
	}


  :deep(.u-tag--mini) {
    height: 43px;
    line-height: 43px;
    padding: 0 15px;
    min-width: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.u-tag--primary) {
    background-color: #d81e06;
  }
}
.no-bottom-input {
  :deep(.u-line) {
    border-bottom: none!important;
  }
  :deep(.u-form-item__body) {
    padding: 10px 0 0 0 !important;
  }
}
.name-title {
  padding-top: 10px;
}

.addressRow {
  display: block;

  :deep(.uni-select__selector-scroll) {
    max-height: 50vh;
  }
  :deep(.uni-select__selector-item) {
    text-align: left;
    border-bottom: 1px solid #f4f4f5;
    line-height: 15px;
    padding: 10px 10px;
  }
  :deep(.uni-scroll-view) {
    //width: max-content;
  }
  :deep(.uniui-clear) {
    //position: absolute;
    //right: 0;
  }
  :deep(.uni-select__input-box) {
    width: 100%;
  }
  :deep(.uni-select__input-text) {
    white-space: unset;
  }
}
</style>