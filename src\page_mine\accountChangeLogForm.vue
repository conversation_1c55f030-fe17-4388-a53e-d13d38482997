<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
					<up-form-item label="发生账户" prop="accountId" borderBottom="true">
            <up-input v-model="formData.accountInfo.accountTypeLabel" border="none" inputAlign="right" readonly></up-input>
					</up-form-item>
          <up-form-item label="变更类型" prop="changeSource" borderBottom="true">
            <up-input v-model="formData.changeSourceLabel" border="none" inputAlign="right" readonly></up-input>
          </up-form-item>
          <up-form-item label="变更前金额" prop="amountTotalBefore" borderBottom="true">
<!--            <up-input v-model="formData.amountTotalBefore" mode="price" fontSize="15px" color="#000"
                      border="none" inputAlign="right" :adjustPosition="true" safe-area
                      cursor-spacing="0" type="digit"></up-input>-->
            <view style="text-align: right">{{ formatMoney(formData.amountTotalBefore, 2) }}</view>
          </up-form-item>
          <up-form-item label="变更金额" prop="changeAmount" borderBottom="true">
            <view style="text-align: right">{{ (formData.changeAmount >= 0 ? '+' : '') + formatMoney(formData.changeAmount, 2) }}</view>
          </up-form-item>
          <up-form-item label="变更后金额" prop="amountTotalAfter" borderBottom="true">
            <view style="text-align: right">{{ formatMoney(formData.amountTotalAfter, 2) }}</view>
          </up-form-item>
          <up-form-item label="变更时间" prop="toAccountId" >
            <up-input v-model="formData.changeTimeLabel" border="none" inputAlign="right" readonly></up-input>
          </up-form-item>
				</up-form>
			</view>
		</view>

	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
  import {
    getAccountTypeLabel, getAccountRoleLabel, getAccountChangeSourceLabel
  } from "@/page_mine/api/sub_mine";
  import {formatDate, formatMoney} from "@/api/common";

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    id: undefined,
    accountId: undefined,
    relyAccountId: undefined,
    changeSource: undefined,
    changeSourceLabel: undefined,
    changeAmount: undefined,
    amountBefore: undefined,
    amountAfter: undefined,
    changeTime: undefined,
    userId: undefined,
    businessId: undefined,
    accountInfo: {},
    toAccountInfo: {},
  })

	// 初始化页面
	const initPage = (obj) => {
    // console.log(obj)
		if (obj.data) {
			formType.value = obj.type
			formData.value = {...obj.data}

      formData.value.changeSourceLabel = getAccountChangeSourceLabel(formData.value.changeSource)
      formData.value.changeTimeLabel = formatDate(formData.value.changeTime, 'yyyy-MM-dd HH:mm:ss')
      if (formData.value.accountInfo) {
        // formData.value.accountInfo.accountTypeLabel = getAccountRoleLabel(formData.value.accountInfo.accountRole) + '-' + getAccountTypeLabel(formData.value.accountInfo.accountType)
        formData.value.accountInfo.accountTypeLabel = getAccountTypeLabel(formData.value.accountInfo.accountType)
      }
      if (formData.value.toAccountInfo) {
        formData.value.toAccountInfo.accountTypeLabel = getAccountRoleLabel(formData.value.toAccountInfo.accountRole) + '-' + getAccountTypeLabel(formData.value.toAccountInfo.accountType)
      }

		}
	}

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

</style>