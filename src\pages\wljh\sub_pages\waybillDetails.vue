<template>
  <!-- 输入联系方式后四位 -->
  <view v-show="!showDetail" class="phone-verify">
    <view class="phone-verify-box">
      <view class="phone-verify-box-header">
        <text style="color: #666aa1">{{ formData.logisticsCompaniesName }}</text>
        <text style="color: #818180; padding-left: 20rpx">{{ formData.waybillCode }}</text>
        <view class="phone-verify-box-header-copy">
          <up-icon name="file-text" size="20" color="#606266" @click="copyContent(formData.waybillCode)"></up-icon>
        </view>

      </view>
      <view class="phone-verify-box-content">
        <view class="phone-verify-box-content-txt">
          请输入寄件人或收件人联系方式后四位验证身份
        </view>
        <view class="phone-verify-box-content-input">
          <up-code-input v-model="verifyPhoneValue" mode="line" hairline size="45" :maxlength="4"
                         borderColor="#d81e06" @finish="verifyInputFinish"
          ></up-code-input>
        </view>
      </view>
    </view>
  </view>
  <!-- 运单详情 - 内有画布，不可隐藏 -->
  <view class="layout-task">
    <!-- 头部 -->
    <view class="layout-task-header">
      <!-- tab切换栏 -->
      <view class="custom-tabs-menu">
        <up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" :itemStyle="tabsItemStyle"
                 lineWidth="78" @click="handleTabsClick" scrollable></up-tabs>
      </view>
    </view>
    <!-- 内容 -->
    <view class="layout-task-body">
      <!-- 电子存根 -->
      <view v-show="tabsSelected === 'info'" class="custom-card">
        <view class="custom-card-header">
          <up-row>
            <!--<up-col :span="0.5" ></up-col>
            <up-col :span="7.5" customStyle="text-align: center; ">
              <canvas id="imgCanvas" type="2d" style="display: none; width:284px;height:50px;" />
              <w-barcode :options="barcodeOptions" ref="barcodeRef" @generate="handleGenerateBar"></w-barcode>
            </up-col>-->
            <up-col :span="8" customStyle="text-align: center; ">
              <canvas id="imgCanvas" type="2d" style="width:234px;height:50px;" />
              <view class="disapleBox">
                <w-barcode :options="barcodeOptions" ref="barcodeRef" @generate="handleGenerateBar"></w-barcode>
              </view>
            </up-col>
            <up-col :span="4" customStyle="text-align: center; border-left: 1px solid #f1f1f1;">
              <text style="display: block">目的地</text>
              <text>{{ formData.discDeptName ? formData.discDeptName : '--' }}</text>
            </up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <view class="color-gray">寄件方：</view>
          <up-row customStyle="align-items: flex-start;margin-bottom: 20rpx;">
            <up-col :span="1">
              <up-image :src="sendIcon" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
            </up-col>
            <up-col :span="11">
              <up-row>
                <up-col :span="7">
                  <view>{{ formData.sendName ? formData.sendName : '--' }}</view>
                </up-col>
                <up-col :span="4" style="text-align: right">
                  <view v-if="!showSendPhone">
                    <up-text mode="phone" :text="formData.sendPhone ? formData.sendPhone : '--'" format="encrypt"></up-text>
                  </view>
                  <view v-if="showSendPhone">
                    <up-text mode="text" :text="formData.sendPhone ? formData.sendPhone : '--'"></up-text>
                  </view>
                </up-col>
                <up-col :span="1" style="text-align: right">
                  <up-icon v-if="!showSendPhone" name="eye-fill" size="20" @click="showSendPhone=true"></up-icon>
                  <up-icon v-if="showSendPhone" name="eye-off" size="20" @click="showSendPhone=false"></up-icon>
                </up-col>
              </up-row>
              <text class="color-gray" style="display: block; padding-top: 15rpx">
                {{ formData.sendAddress ? formData.sendAddress : '--' }}
              </text>
            </up-col>
          </up-row>

          <view class="color-gray">收件方：</view>
          <up-row customStyle="align-items: flex-start;margin-bottom: 20rpx;">
            <up-col :span="1">
              <up-image :src="collIcon" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
            </up-col>
            <up-col :span="11">
              <up-row>
                <up-col :span="7">
                  <view>{{ formData.collectName ? formData.collectName : '--' }}</view>
                </up-col>
                <up-col :span="4" style="text-align: right">
                  <view v-if="!showCollectPhone">
                    <up-text mode="phone" :text="formData.collectPhone ? formData.collectPhone : '--'" format="encrypt"></up-text>
                  </view>
                  <view v-if="showCollectPhone">
                    <up-text mode="text" :text="formData.collectPhone ? formData.collectPhone : '--'"></up-text>
                  </view>
                </up-col>
                <up-col :span="1" style="text-align: right">
                  <up-icon v-if="!showCollectPhone" name="eye-fill" size="20" @click="showCollectPhone=true"></up-icon>
                  <up-icon v-if="showCollectPhone" name="eye-off" size="20" @click="showCollectPhone=false"></up-icon>
                </up-col>
              </up-row>
              <text class="color-gray" style="display: block; padding-top: 15rpx">
                {{ formData.collectAddress ? formData.collectAddress : '--' }}
              </text>
            </up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;color: #606266;">
            <up-col :span="4">托寄物</up-col>
            <up-col :span="4">包装/件数</up-col>
            <up-col :span="4">重量/体积</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4">{{ formData.goodsTypeLabel ? formData.goodsTypeLabel : '--'}}</up-col>
            <up-col :span="4">{{ formData.packMethodLabel ? (formData.packMethodLabel + '/' + formData.totalNum) : '--' }}</up-col>
            <up-col :span="4">{{ formData.totalWeight ? (formData.totalWeight + '公斤/' + formData.totalVolume + '方') : '--'}}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <up-row  customStyle="align-items: flex-start; margin-top: 15rpx;color: #606266;">
            <up-col :span="4">送货方式</up-col>
            <up-col :span="4">放货方式</up-col>
            <up-col :span="4">回单类型</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4">{{ formData.deliveryMethod ? formData.deliveryMethod==='1' ? '自提' : '送货' : '--'}}</up-col>
            <up-col :span="4">{{ formData.releaseMethod==='1' ? '等通知放货' : '无' }}</up-col>
            <up-col :span="4">{{ orderData.receiptTypeLabel }}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;color: #606266;">
            <up-col :span="4">是否保价</up-col>
            <up-col :span="4">是否代收货款</up-col>
            <up-col :span="4">是否返佣</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4">{{ orderData.insuredAmount&&orderData.insuredAmount>0 ? '是' : '否'}}</up-col>
            <up-col :span="4">{{ orderData.collectionDelivery&&orderData.collectionDelivery>0 ? '是' : '否'}}</up-col>
            <up-col :span="4">{{ orderData.brokerageAmount&&orderData.brokerageAmount>0 ? '是' : '否'}}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;border-bottom: 1px solid #f1f1f1;">
            <up-col :span="12" :customStyle="colorGray2">代收明细</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">代收货款：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.collectionDelivery, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;">
            <up-col :span="4" :customStyle="colorGray2">代收运费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.collectionFreightAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">代收合计：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.collectionDelivery, 2) }}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body">
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;border-bottom: 1px solid #f1f1f1;">
            <up-col :span="4" :customStyle="colorGray2">费用明细</up-col>
            <up-col :span="8" customStyle="text-align: right"><span :style="colorGray2">付款方式：</span>{{ formData.payMethodLabel }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">运费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.amountFreight, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">保价费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.insuredPayAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">送货费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.deliveryAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">控货费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.releaseAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">回单费：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.receiptAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">其他费用：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.otherAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">佣金：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.brokerageAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0; border-bottom: 1px solid #f1f1f1;" >
            <up-col :span="4" :customStyle="colorGray2">费用合计：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.totalAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx; border-bottom: 1px solid #f1f1f1;">
            <up-col :span="4" :customStyle="colorGray2">优惠金额：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.discountAmount, 2) }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;">
            <up-col :span="4" :customStyle="colorGray2">实付金额：</up-col>
            <up-col :span="8" customStyle="text-align: right">¥ {{ formatMoney(orderData.actualPayAmount, 2) }}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body" style="border-bottom: none!important;">
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;">
            <up-col :span="3" :customStyle="colorGray2">备注：</up-col>
            <up-col :span="9" customStyle="text-align: right">{{ formData.remark ? formData.remark : '' }}</up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin: 15rpx 0;" >
            <up-col :span="4" :customStyle="colorGray2">寄件时间：</up-col>
            <up-col :span="8" customStyle="text-align: right">
              <view style="position: relative;">
                <up-text :text="formData.waybillCreateTime" format="yyyy/mm/dd hh:MM" mode="date"
                       color="#606266" size="14" customStyle="position: absolute; right: 5rpx; top: 5rpx;"></up-text>
              </view>
            </up-col>
          </up-row>
          <up-row customStyle="align-items: flex-start; margin-top: 15rpx;">
            <up-col :span="4" :customStyle="colorGray2">揽收网点：</up-col>
            <up-col :span="8" customStyle="text-align: right">{{ formData.networkName ? formData.networkName : '' }}</up-col>
          </up-row>
        </view>

        <view class="custom-card-body" style="border-bottom: none!important;">
          <view style="height: 30rpx"></view>
        </view>

      </view>

      <!-- 运单状态-顶部 -->
      <view v-if="tabsSelected === 'track'" class="custom-card">
        <view class="custom-card-header">
          <up-row>
            <up-col :span="6" customStyle="text-align: center; border-right: 1px solid #f1f1f1;">
              <text>{{ statusLabel }}</text>
              <up-line color="#f1f1f1"></up-line>
              <text style="color: #606266; font-size: 13px">{{ statusTimeLabel }}时间：</text>
              <text style="font-size: 13px">{{ statusTimeStr }}</text>
            </up-col>
            <up-col :span="6" customStyle="text-align: center; ">
              <up-row customStyle="font-size:13px;line-height: normal;">
                <up-col :span="4.5" customStyle="text-align:center;color:#000000;font-weight: 500;">{{ formData.billDeptName }}</up-col>
                <up-col :span="3" customStyle="text-align:center;color:#ec818e">》》》</up-col>
                <up-col :span="4.5" customStyle="text-align:center;color:#000000;font-weight: 500;">{{ formData.discDeptName }}</up-col>
              </up-row>
            </up-col>
          </up-row>
        </view>
      </view>

      <!-- 运单状态-list -->
      <view v-if="tabsSelected === 'track'" class="custom-card trackView" style="padding: 20rpx;">
        <up-steps v-if="waybillTrackList.length > 0" :current="0" direction="column" activeColor="red">
          <up-steps-item v-for="(item, index) in waybillTrackList" :title="item.showTitle ? item.nodeName : ''" >
            <template #icon>
              <view v-if="item.showTitle" style="margin-top: 25rpx">
                <!-- 激活-已签收 -->
                <up-image v-if="index === 0 && item.nodeType === 'sign'" :src="signIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-运送中、派送中 -->
                <up-image v-if="index === 0 && (item.nodeType === 'transit' || item.nodeType === 'delivery')" :src="transitIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-已揽收 -->
                <up-image v-if="index === 0 && item.nodeType === 'collect'" :src="collectIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-其他 -->
                <up-image v-if="index === 0 && !item.nodeType" :src="collectIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>

                <!-- 未激活-已签收 -->
                <up-image v-if="index !== 0 && item.nodeType === 'sign'" :src="signIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-运送中、派送中 -->
                <up-image v-if="index !== 0 && (item.nodeType === 'transit' || item.nodeType === 'delivery')" :src="transitIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-已揽收 -->
                <up-image v-if="index !== 0 && item.nodeType === 'collect'" :src="collectIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-其他 -->
                <up-image v-if="index !== 0 && !item.nodeType" :src="collectIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
              </view>
              <view v-if="!item.showTitle">
                <span class="step-icon-dot">·</span>
              </view>
            </template>
            <template #desc>
              <view>
                <view :class="index===0?'step-content':'step-content step-content-text'">{{ formatDate(item.nodeTime, 'yyyy-MM-dd HH:mm:ss') }}</view>
                <span v-for="(item2, index2) in item.contentList"
                      :class="index===0&&item2.type==='text'?'step-content':'step-content step-content-'+item2.type"
                      @click=makeCall(item2)>
                  {{ item2.value }}
                </span>
              </view>
            </template>
          </up-steps-item>
        </up-steps>
        <view v-if="waybillTrackList.length <= 0" style="text-align: center;color: #606266;">暂无数据</view>
      </view>

    </view>

  </view>
</template>

<script setup>
import {
  onMounted,
  getCurrentInstance,
  ref, reactive
} from 'vue'
import { onLoad } from "@dcloudio/uni-app"
import {
  getDictDataInfoAll, getWaybillDetailInfoApp,
  getWaybillTrackInfo, reachWaybillInfoApp
} from '@/api/wljh/task'
import modal from '@/plugins/modal'
import {
  QRCode,
  BarCode
} from '@uni-ui/code-plugs';
import {formatDate, formatMoney } from '@/api/common.js'
import {useRoute} from 'vue-router'

const showDetail = ref(true)
const verifyPhoneValue = ref('')

const sendIcon = ref('https://dachisc.wang:9000/wljh/134cc43076fad1ccc6578ec1c80e188efeb55dd0665f7743ade3722e1ca59d54.png')
const collIcon = ref('https://dachisc.wang:9000/wljh/f455372bdd7e2cc155cf0dc4c316e1aea78f8c2803e2462a2a75e7dc7bec32b8.png')
const signIconRed = ref('https://dachisc.wang:9000/wljh/f455372bdd7e2cc155cf0dc4c316e1aea78f8c2803e2462a2a75e7dc7bec32b8.png')
const signIconGray = ref('https://dachisc.wang:9000/wljh/82ed88c0a82ffd3456061626c3c267f26138e0f94ce90f45b190305c0dbf4ec7.png')
const transitIconRed = ref('https://dachisc.wang:9000/wljh/a91062bd812e9122bbf26001c3bedc19847f312cbb58d4ac5364bbd6ff14b13a.png')
const transitIconGray = ref('https://dachisc.wang:9000/wljh/d86c6d5e6bee09bb222130efd1ae73ebceb851c3de09e14b26cde943c90c07ee.png')
const collectIconRed = ref('https://dachisc.wang:9000/wljh/2ec6dbeb56533074f7f97a9efe86179972660f02a3932d348f8a7744229bf391.png')
const collectIconGray = ref('https://dachisc.wang:9000/wljh/41fc277a0b0392dedd4cb7609aeab69fd36460819defc010acaf55dc52d17ba3.png')

  const tabsSelected = ref('info') // tab选中Key
  const tabsList = ref([{ // tab切换栏菜单数据
    name: '电子存根',
    badge: {
        value: 0
      },
      key: 'info'
    },
    {
      name: '运单状态',
      key: 'track'
    },
    // {
    //   name: '单据图片',
    //   key: 'pic'
    // }
  ])

// 生成条形码
const barcodeOptions = ref({
  code: '',
  width: 260,
  height: 40
})
const barcodeRef = ref(null)
const barcodeTemplatePath = ref()

// 是否展示原手机号
const showSendPhone = ref(false)
const showCollectPhone = ref(false)

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()
	const formData = ref({ // 运单数据
    orderCode: undefined,
    waybillCode: undefined,
		sendName: undefined,
		sendPhone: undefined,
		sendAddress: undefined,
		collectName: undefined,
		collectPhone: undefined,
		goodsType: undefined,
		totalWeight: undefined,
		totalVolume: undefined,
		totalNum: undefined,
		logisticsCompaniesId: undefined,
    networkName: undefined,
		payMethod: undefined,
		shouldPayAmount: undefined,
		actualPayAmount: undefined,
    amountFreight: undefined,
    totalAmount: 0,
		goodsTypeLabel: undefined,
		payMethodLabel: undefined,
    packMethodLabel: undefined,
    waybillCreateTime: undefined,
    subWaybillList: [],
	})
	const orderData = ref({ // 订单数据
    orderCode: undefined,
    amountFreight: undefined,// 运费
    totalAmount: 0, // 费用合计
    actualPayAmount: undefined,// 实付金额
    deliveryAmount: undefined,// 送货费
    insuredAmount: undefined,// 保价
    amountZdf: undefined,// 制单费
    receiptType: undefined,// 回单类型（1-电子回单 2-纸质回单）
    receiptTypeLabel: undefined,// 回单类型（1-电子回单 2-纸质回单）
    receiptAmount: undefined,// 回单费
    releaseAmount: undefined,// 控货费
    insuredPayAmount: undefined,// 支付的保价费
    brokerageAmount: undefined,// 佣金
    brokerageBackType: undefined,// 佣金返款方式（1-现返 2-欠返）
    brokerageBackTypeLabel: undefined,// 佣金返款方式（1-现返 2-欠返）
    otherAmount: undefined,// 其他费用
    discountAmount: undefined,// 优惠金额
    collectionFreightAmount: undefined,// 代收运费
    collectionPayAmount: undefined,// 代收手续费
    collectionProcRate: 0,// 代收款手续费率(‰)
    collectionProcMin: undefined,// 代收款手续费起步价
    collectionProcMax: undefined,// 代收款手续费封顶价
	})
// 运单轨迹数据
const waybillTrackList = ref({})
const firstTrackType = ref('')
const statusLabel = ref('')
const statusTimeLabel = ref('')
const statusTimeStr = ref('')

// tabs 切换事件
const handleTabsClick = async (item, index) => {
  tabsSelected.value = item.key
  if (item.key === 'info') { // 电子存根

  } else if (item.key === 'track') { // 运单状态

  } else if (item.key === 'pic') { // 单据图片
    modal.msgError('开发中')
  }
}

/**
 * 获取运单轨迹信息
 * @param waybillCode 运单号
 * @returns {Promise<void>}
 */
const getWaybillTrackInfoFn = (waybillCode) => {
  getWaybillTrackInfo(waybillCode).then(res => {
    if (res.code === 0) {
      waybillTrackList.value = res.data
      if (res.data.length > 0) {
        firstTrackType.value = res.data[0].nodeType
        if (res.data[0].nodeName) {
          statusLabel.value = res.data[0].nodeName
        } else {
          statusLabel.value = '已开单'
        }
        switch (res.data[0].nodeType) {
          case 'collect':
            statusTimeLabel.value = '揽收'
            break;
          case 'transit':
            statusTimeLabel.value = '运送'
            break;
          case 'delivery':
            statusTimeLabel.value = '派送'
            break;
          case 'sign':
            statusTimeLabel.value = '签收'
            break;
          default:
            statusTimeLabel.value = '下单'
            break;
        }
        if (res.data[0].nodeTime) {
          statusTimeStr.value = formatDate(res.data[0].nodeTime, 'yyyy/MM/dd HH:mm')
        } else {
          statusTimeStr.value = formatDate(formData.value.waybillCreateTime, 'yyyy/MM/dd HH:mm')
        }
      } else {
        statusLabel.value = '已开单'
        statusTimeLabel.value = '开单'
        statusTimeStr.value = formatDate(formData.value.waybillCreateTime, 'yyyy/MM/dd HH:mm')
      }
    }
  })
}
	
	// 获取单个字典标签
	const getDictLable = async (dictType, value) => {
		await getDictDataInfoAll({
			'type': dictType,
			'value': value
		}).then(res => {
			if (res.code === 0) {
				switch (dictType) {
					case 'GOODS_TYPE':
						formData.value.goodsTypeLabel = res.data[0].label
						break;
					case 'PAY_METHOD':
						formData.value.payMethodLabel = res.data[0].label
						break;
				}
			}
		})
	}
	
	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}

  // 初始化页面数据
  const initPageData = (waybillCode) => {
    // 获取运单详情
    getWaybillDetailInfoApp(waybillCode).then(res => {
      if (res.code === 0) {
        // 运单数据
        fillFormData(res.data)
        // 订单数据
        orderData.value = res.data.orderInfo
        if (orderData.value.receiptType === '1') {
          orderData.value.receiptTypeLabel = '电子回单'
        } else if (orderData.value.receiptType === '2') {
          orderData.value.receiptTypeLabel = '纸质回单'
        } else {
          orderData.value.receiptTypeLabel = '无'
        }
        // 条形码
        barcodeRef.value = null
        barcodeTemplatePath.value = undefined
        try {
          makeBarCodeImg(formData.value.waybillCode)
        } catch (e) {
          console.log(e)
        }
        // 未到签收状态-先同步状态
        if (formData.value.logisticsStatus !== '5') {
          console.log('未到签收状态-先同步状态')
          reachWaybillInfoApp(res.data)
          setTimeout(() => {
            // 5s后获取最新状态
            getWaybillDetailInfoApp(waybillCode).then(res => {
              if (res.code === 0) {
                // 刷新运单数据
                fillFormData(res.data)
                // 再次获取运单状态数据
                getWaybillTrackInfoFn(waybillCode)
              }
            })
          }, 5000)
        }
      }
    })
    // 获取运单状态数据
    getWaybillTrackInfoFn(waybillCode)
  }

  // 填充表单数据
  const fillFormData = (data) => {
    formData.value = data
    getDictLable('PAY_METHOD', formData.value.payMethod)
    formData.value.shouldPayAmount = formatMoney(formData.value.shouldPayAmount, 2)
    formData.value.collectionDelivery = formatMoney(formData.value.collectionDelivery, 2)
    formData.value.totalAmount = formatMoney(formData.value.totalAmount, 2)
    formData.value.actualPayAmount = formatMoney(formData.value.actualPayAmount, 2)
  }

  onLoad((option)=>{
    console.log('运单详情onLoad', option)
    // 页面跳转时传进来的运单号
    let waybillCode = option.waybillCode
    if (!waybillCode) {
      // 扫描二维码进入时 option如下
      // {q: "https%3A%2F%2Fhwscm.com.cn%2Fwaybilldetail%3FwaybillCode%3D241226006", scancode_time: "1735538022"}
      let uri = decodeURIComponent(option.q)
      waybillCode = uri.split('waybillCode=')[1]
      // 需要先输入联系方式后四位再展示订单详情
      showDetail.value = false
    }
    console.log('运单号', waybillCode)
    initPageData(waybillCode)
  })

	onMounted(() => {
    // try {
    //   const route = useRoute()
    //   let waybillCode = route.query.waybillCode
    //   if (waybillCode) {
    //     initPageData(waybillCode)
    //   }
    // } catch (e) {
    //   console.log(e)
    // }

		// eventChannel.on('parentPageData', function(data) {
    //   initPageData(data.waybillCode)
    // })

	})

 function makeBarCodeImg(waybillCode) {
   //创建面单画布
   wx.createSelectorQuery()
       .select('#imgCanvas')
       .fields({
         node: true,
         size: true
       })
       .exec((res) => {
         // Canvas 对象
         const canvas = res[0].node
         // Canvas 画布的实际绘制宽高
         const canvasWidth = 272
         const canvasHeight = 50
         // 绘制上下文
         const ctx = canvas.getContext('2d')
         // 初始化画布大小
         const dpr = wx.getWindowInfo().pixelRatio
         canvas.width = canvasWidth * 2
         canvas.height = canvasHeight * 2
         ctx.scale(2, 2)
         // 清空画布
         ctx.clearRect(0, 0, canvas.width, canvas.height)
         ctx.fillStyle = "#ffffff"
         ctx.fillRect(0, 0, canvas.width, canvas.height)
         // 绘制
         const barcodeWidth = 272
         const barcodeHeight = 50
         getBarcode(waybillCode, barcodeWidth, barcodeHeight)
         handleGenerateBar()
         setTimeout(() => {
           //console.log("条形码生成")
           //console.log("条形码地址：", barcodeTemplatePath.value)
           if (barcodeTemplatePath.value) {
             const imageBar = canvas.createImage()
             imageBar.onload = () => {
               ctx.drawImage(imageBar, 6, 0, barcodeWidth, barcodeHeight)
             }
             imageBar.src = barcodeTemplatePath.value
           }
         }, 500)
       })
 }

  // 制作条形码
  const getBarcode = (code, width, height) => {
    barcodeOptions.value.code = code
    barcodeOptions.value.width = width
    barcodeOptions.value.height = height
  }
  // 监听码制作成功
  const handleGenerateBar = async () => {
    const res = await barcodeRef.value.GetCodeImg()
    barcodeTemplatePath.value = res.tempFilePath
    // console.log('条形码barcodeTemplatePath=', barcodeTemplatePath.value)
  }


// 复制到粘贴板
const copyContent = (value) => {
  uni.setClipboardData({
    data: value,
    success: function () {
      console.log('复制成功', value);
      modal.msgSuccess("已复制")
    }
  })
}

// 联系方式后四位输入完成
const verifyInputFinish = (value) => {
  console.log('verifyInputFinish', value)
  if (formData.value.sendPhone && value === formData.value.sendPhone.slice(-4)) {
    // 判断是否发件人手机号后四位
    showDetail.value = true
    modal.msgSuccess('验证成功')
    uni.hideKeyboard()
  } else if (formData.value.collectPhone && value === formData.value.collectPhone.slice(-4)) {
    // 判断是否收件人手机号后四位
    showDetail.value = true
    modal.msgSuccess('验证成功')
    uni.hideKeyboard()
  } else {
    modal.msgError('验证失败，请重新输入')
  }
}

// 点击手机号拨打电话
const makeCall = (phoneContent) => {
  if (phoneContent.type === 'phone') {
    console.log('拨打电话', phoneContent.value)
    uni.makePhoneCall({
      phoneNumber: phoneContent.value
    })
  }
}


// 样式
const tabsItemStyle = reactive({
  width: '180rpx',
  height: '100rpx'
})
const colorGray2 = reactive({
  color: '#606266'
})
</script>

<style scoped lang="scss">
	.layout-sub-waybillDetails {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			background-color: #fff;
			overflow: auto;
			// height: calc(100% - 140rpx);
			margin: 20rpx 0;
			border-radius: 5px;
			padding: 20rpx;
			
			:deep(.u-form-item__body__left__content__label) {
				color: #606266;
			}
		}
		
		.custom-btn{
			margin-top: 20rpx;
			
			:deep(.u-button--square) {
				border-radius: 20px;
			}
			
			:deep(.u-button--primary) {
				background-color: #d81e06;
				border: #d81e06;
			}
			
			:deep(.u-button) {
				height: 45px;
			}
		}
		
		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
		}
	}

  .layout-task {
     background-color: #f1f1f1;
     // height: 100vh;
     height: calc(100vh - var(--window-bottom) - var(--window-top));
     //overflow: auto;

     .layout-task-header {
       background-color: #fff;
       height: 100rpx;
       display: flex;
       justify-content: space-between;
       align-items: center;

       .custom-tabs-menu {
         width: 500rpx;
       }

       .custom-tabs-btn {
         padding: 0 20rpx;

         :deep(.u-button--plain.u-button--info) {
           height: 60rpx;
           border-radius: 20px;
           padding: 0 40rpx;
           color: #606266;
         }
       }
     }


     .layout-task-body {
       padding: 0 10px 10px;
       height: calc(100% - 150rpx);
       overflow: auto;

       .custom-card {
         border-radius: 5px;
         font-size: 15px;
         background-color: #fff;
         margin-bottom: 20rpx;
         position: relative;
         margin: 20rpx 0;

         .custom-card-header {
           //display: flex;
           justify-content: space-between;
           align-items: center;
           border-bottom: 1px solid #f1f1f1;
           height: 120rpx;
           line-height: 60rpx;
           //padding: 0 20rpx;
           border-top-left-radius: 5px;
           border-top-right-radius: 5px;

         }

         .custom-card-body {
           padding: 0 20rpx;
           border-bottom: 1px solid #f1f1f1;
         }

         :deep(.u-text__value) {
           font-size: 15px!important;
         }


       }

     }
   }
  .trackView {
    :deep(.u-steps-item__content) {
      margin-left: 25rpx!important;
    }
  }
  .color-gray {
    color: #8a8a8a;
  }
  .color-gray2 {
    color: #606266;
  }

  .step-icon-text {
    width: 21px;
    height: 21px;
    background-color: red;
    border-radius: 100px;
    font-size: 12px;
    color: #fff;
    line-height: 21px;
    text-align: center;
  }
  .step-icon-dot {
    //width: 21px;
    //height: 21px;
    border-radius: 100px;
    font-size: 35px;
    color: #6a6a6a;
    background-color: rgb(255 255 255 / 0%);
    //line-height: 21px;
    text-align: center;
  }

  .disapleBox {
    //display: flex;
    position: absolute;
    top: -1000px;
    left: -200;
  }
  .phone-verify {
    position: absolute;
    height: 100vh;
    width: 100vw;
    z-index: 5;
    //background-color: #ededed;
    background-image: linear-gradient(to bottom, #d81e06, #ededed, #ededed, #ededed);
    padding: 10vh 20rpx 0 20rpx;

    .phone-verify-box {
      height: 35vh;
      border-radius: 5px;
      background-color: #ffffff;
      padding: 20rpx;

      .phone-verify-box-header {

        .phone-verify-box-header-copy {
          display: inline-block;
          position: relative;
          top: 4rpx;
        }

      }

      .phone-verify-box-content {
        padding: 80rpx;

        .phone-verify-box-content-txt {
          text-align: center;
        }
        .phone-verify-box-content-input {
          padding-top: 40rpx;
          display: flex;
          justify-content: center;
        }

      }


    }
  }
  .step-content {
    font-size: 13px!important;
    margin-top: 10rpx;
  }
  .step-content-text {
    color: #606266!important;
  }
  .step-content-phone {
    color: #0081ff!important;
  }
</style>