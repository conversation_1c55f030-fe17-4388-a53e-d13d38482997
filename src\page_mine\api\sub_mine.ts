import request from "@/utils/request";


/**
 * 总账户 - TOTAL
 * （网点）运费预付账户 - NETWORK_FREIGHT_PRE
 * （网点）运费分成账户 - NETWORK_FREIGHT_DIVIDE
 * （网点）红包账户 - NETWORK_RED_PACKET
 * （网点）税费账户 - NETWORK_TAX
 * （网点）佣金账户 - NETWORK_BROKERAGE
 * （网点）保证金账户 - NETWORK_DEPOSIT
 * （物流公司）运费账户 - COMPANY_FREIGHT
 * （物流公司）税费账户 - COMPANY_TAX
 * （物流公司）佣金账户 - COMPANY_BROKERAGE
 * 平台运费账户 - PLATFORM_FREIGHT
 * 平台毛利账户 - PLATFORM_PROFIT
 * 平台营销账户 - PLATFORM_MARKET
 * 平台税费账户 - PLATFORM_TAX
 * 平台佣金账户 - PLATFORM_BROKERAGE
 */
export const ACCOUNT_TYPE_LIST  = [
    { role: '', label: '总账户', value: 'TOTAL' },
    { role: '2', label: '运费预付账户', value: 'NETWORK_FREIGHT_PRE' },
    { role: '2', label: '运费分成账户', value: 'NETWORK_FREIGHT_DIVIDE' },
    { role: '2', label: '红包账户', value: 'NETWORK_RED_PACKET' },
    { role: '2', label: '税费账户', value: 'NETWORK_TAX' },
    { role: '2', label: '佣金账户', value: 'NETWORK_BROKERAGE' },
    { role: '2', label: '保证金账户', value: 'NETWORK_DEPOSIT' },
    { role: '3', label: '运费账户', value: 'COMPANY_FREIGHT' },
    { role: '3', label: '税费账户', value: 'COMPANY_TAX' },
    { role: '3', label: '佣金账户', value: 'COMPANY_BROKERAGE' },
    { role: '1', label: '运费账户', value: 'PLATFORM_FREIGHT' },
    { role: '1', label: '毛利账户', value: 'PLATFORM_PROFIT' },
    { role: '1', label: '营销账户', value: 'PLATFORM_MARKET' },
    { role: '1', label: '税费账户', value: 'PLATFORM_TAX' },
    { role: '1', label: '佣金账户', value: 'PLATFORM_BROKERAGE' }
]

export const getAccountTypeLabel = (type: string) => {
    if (!type) {
        return ''
    }
    return ACCOUNT_TYPE_LIST.find(item => item.value === type)?.label
}

/**
 * 账户角色（1- 平台 2-网点 3-物流公司 ）
 */
export const ACCOUNT_ROLE_LIST  = [
    { label: '平台', value: '1' },
    { label: '网点', value: '2' },
    { label: '物流公司', value: '3' }
]
export const getAccountRoleLabel = (type: string) => {
    if (!type) {
        return ''
    }
    return ACCOUNT_ROLE_LIST.find(item => item.value === type)?.label
}


/**
 * 变更来源（1-开单扣款 2-分成结算 3-划转 4-充值 5-提现 6-调账 7-删单退款 8-改单退款 9-改单重新扣款）
 */
export const ACCOUNT_CHANGE_SOURCE_LIST = [
    { label: '开单扣款', value: '1' },
    { label: '分成结算', value: '2' },
    { label: '划转', value: '3' },
    { label: '充值', value: '4' },
    { label: '提现', value: '5' },
    { label: '调账', value: '6' },
    { label: '删单退款', value: '7' },
    { label: '改单退款', value: '8' },
    { label: '改单重新扣款', value: '9' },
]
export const getAccountChangeSourceLabel = (type: string) => {
    if (!type) {
        return ''
    }
    return ACCOUNT_CHANGE_SOURCE_LIST.find(item => item.value === type)?.label
}


/**
 * 提现申请状态（1-待审核 2-审核通过 3-审核不通过 4-平台已转账 5-确认已收款）
 */
export const TAKE_CASH_APPLY_STATUS_LIST = [
    { label: '待审核', value: '1' },
    { label: '审核通过', value: '2' },
    { label: '审核不通过', value: '3' },
    { label: '平台已转账', value: '4' },
    { label: '确认已收款', value: '5' }
]
export const getTakeCashApplyStatusLabel = (type: string) => {
    if (!type) {
        return ''
    }
    return TAKE_CASH_APPLY_STATUS_LIST.find(item => item.value === type)?.label
}


/**
 * 充值申请状态（1-待审核 2-审核通过 3-审核不通过 4-已充值）
 */
export const RECHARGE_APPLY_STATUS_LIST = [
    { label: '待审核', value: '1' },
    { label: '审核通过', value: '2' },
    { label: '审核不通过', value: '3' },
    { label: '已充值', value: '4' }
]
export const getRechargeApplyStatus = (type: string) => {
    if (!type) {
        return ''
    }
    return RECHARGE_APPLY_STATUS_LIST.find(item => item.value === type)?.label
}

/**
 * 平台收款码
 */
export const getPlatformReceiveQrCode = (amount: string) => {
    if (amount === '1000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/f29b767262e650e45fbc53445a06db24e76a9bebb01b87f749d563b7fadbb96e.jpg'
    } else if (amount === '2000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/f73a5edcadbda9770ee8e3d852abb92281679fc7278351b646b78d892993dbb8.jpg'
    } else if (amount === '3000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/bdac73eb40b5b82046788ea0974cb0500e2b421a862d40cc9800b2a245050f68.jpg'
    } else if (amount === '5000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/fa7220441a55708afb96b6d383b138dd97b8c0211e788dd592e62ba9e80c2c79.jpg'
    } else {
        return ''
    }
}

// 新增打印机反馈
export const createPrintFeedback = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}

// 删除打印机反馈
export const deletePrintFeedback = (id: number) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 修改网点交易密码
export const updateNetworkAccountPwd = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 更新网点提现码
export const saveNetworkReceiveConfig = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 获取资金明细分页
export const getAccountChangeLogPage = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": "1904467965029023744",
                    "accountId": null,
                    "relyAccountId": null,
                    "changeSource": null,
                    "changeAmount": null,
                    "amountBefore": null,
                    "amountAfter": null,
                    "amountTotalBefore": null,
                    "amountTotalAfter": null,
                    "changeTime": *************,
                    "userId": null,
                    "userName": null,
                    "businessId": null,
                    "createTime": null,
                    "accountInfo": null,
                    "relyAccountInfo": null,
                    "type": "date",
                    "date": "2025年03月",
                    "inAmount": 4574.00,
                    "outAmount": 0
                },
                {
                    "id": 290,
                    "accountId": 30,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 10.00,
                    "amountBefore": 726.00,
                    "amountAfter": 736.00,
                    "amountTotalBefore": 18094.00,
                    "amountTotalAfter": 18104.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 12,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 281,
                    "accountId": 30,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 10.00,
                    "amountBefore": 716.00,
                    "amountAfter": 726.00,
                    "amountTotalBefore": 18084.00,
                    "amountTotalAfter": 18094.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 11,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 275,
                    "accountId": 30,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 10.00,
                    "amountBefore": 706.00,
                    "amountAfter": 716.00,
                    "amountTotalBefore": 18074.00,
                    "amountTotalAfter": 18084.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 10,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 204,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "8",
                    "changeAmount": 320.00,
                    "amountBefore": 18227.00,
                    "amountAfter": 18547.00,
                    "amountTotalBefore": 20082.00,
                    "amountTotalAfter": 20402.00,
                    "changeTime": *************,
                    "userId": 0,
                    "userName": "平台",
                    "businessId": 228,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 198,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "8",
                    "changeAmount": 320.00,
                    "amountBefore": 18227.00,
                    "amountAfter": 18547.00,
                    "amountTotalBefore": 20082.00,
                    "amountTotalAfter": 20402.00,
                    "changeTime": *************,
                    "userId": 0,
                    "userName": "平台",
                    "businessId": 229,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": "1904467971324674048",
                    "accountId": null,
                    "relyAccountId": null,
                    "changeSource": null,
                    "changeAmount": null,
                    "amountBefore": null,
                    "amountAfter": null,
                    "amountTotalBefore": null,
                    "amountTotalAfter": null,
                    "changeTime": *************,
                    "userId": null,
                    "userName": null,
                    "businessId": null,
                    "createTime": null,
                    "accountInfo": null,
                    "relyAccountInfo": null,
                    "type": "date",
                    "date": "2025年02月",
                    "inAmount": 17329.00,
                    "outAmount": 0
                },
                {
                    "id": 98,
                    "accountId": 27,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 120.00,
                    "amountBefore": 160.00,
                    "amountAfter": 280.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 3,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 27,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_DIVIDE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 100.00,
                        "availableAmount": 100.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 99,
                    "accountId": 30,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 10.00,
                    "amountBefore": 530.00,
                    "amountAfter": 540.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 3,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 84,
                    "accountId": 27,
                    "relyAccountId": null,
                    "changeSource": "2",
                    "changeAmount": 140.00,
                    "amountBefore": 20.00,
                    "amountAfter": 160.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 2,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 27,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_DIVIDE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 100.00,
                        "availableAmount": 100.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": "1904467974357155840",
                    "accountId": null,
                    "relyAccountId": null,
                    "changeSource": null,
                    "changeAmount": null,
                    "amountBefore": null,
                    "amountAfter": null,
                    "amountTotalBefore": null,
                    "amountTotalAfter": null,
                    "changeTime": *************,
                    "userId": null,
                    "userName": null,
                    "businessId": null,
                    "createTime": null,
                    "accountInfo": null,
                    "relyAccountInfo": null,
                    "type": "date",
                    "date": "2025年01月",
                    "inAmount": 7000.00,
                    "outAmount": 0
                },
                {
                    "id": 4,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "4",
                    "changeAmount": 5000.00,
                    "amountBefore": 2000.00,
                    "amountAfter": 7000.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": 2,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 3,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "4",
                    "changeAmount": 2000.00,
                    "amountBefore": 0.00,
                    "amountAfter": 2000.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": 1,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": "1904467974331990016",
                    "accountId": null,
                    "relyAccountId": null,
                    "changeSource": null,
                    "changeAmount": null,
                    "amountBefore": null,
                    "amountAfter": null,
                    "amountTotalBefore": null,
                    "amountTotalAfter": null,
                    "changeTime": *************,
                    "userId": null,
                    "userName": null,
                    "businessId": null,
                    "createTime": null,
                    "accountInfo": null,
                    "relyAccountInfo": null,
                    "type": "date",
                    "date": "2024年01月",
                    "inAmount": 10000.00,
                    "outAmount": 0
                },
                {
                    "id": 7,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "4",
                    "changeAmount": 8000.00,
                    "amountBefore": 9000.00,
                    "amountAfter": 17000.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": 3,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 5,
                    "accountId": 26,
                    "relyAccountId": null,
                    "changeSource": "4",
                    "changeAmount": 2000.00,
                    "amountBefore": 7000.00,
                    "amountAfter": 9000.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": 6,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                }
            ],
            "total": 34
        },
        "msg": ""
    })})
}
// 单部门账户列表
export const accountListSingle = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": [
            {
                "id": 26,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_FREIGHT_PRE",
                "overAllowAmount": 0.00,
                "totalAmount": 14879.00,
                "availableAmount": 14379.00,
                "freezeAmount": 500.00,
                "createTime": *************
            },
            {
                "id": 27,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_FREIGHT_DIVIDE",
                "overAllowAmount": 0.00,
                "totalAmount": 100.00,
                "availableAmount": 100.00,
                "freezeAmount": 0.00,
                "createTime": *************
            },
            {
                "id": 28,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_RED_PACKET",
                "overAllowAmount": 0.00,
                "totalAmount": 1100.00,
                "availableAmount": 1050.00,
                "freezeAmount": 50.00,
                "createTime": *************
            },
            {
                "id": 29,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_TAX",
                "overAllowAmount": 0.00,
                "totalAmount": 9.00,
                "availableAmount": 9.00,
                "freezeAmount": 0.00,
                "createTime": *************
            },
            {
                "id": 30,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_BROKERAGE",
                "overAllowAmount": 0.00,
                "totalAmount": 736.00,
                "availableAmount": 736.00,
                "freezeAmount": 0.00,
                "createTime": *************
            },
            {
                "id": 31,
                "accountRole": "2",
                "mainId": 1,
                "mainName": null,
                "accountType": "NETWORK_DEPOSIT",
                "overAllowAmount": 0.00,
                "totalAmount": 0.00,
                "availableAmount": 0.00,
                "freezeAmount": 0.00,
                "createTime": *************
            },
            {
                "id": *************,
                "accountRole": null,
                "mainId": null,
                "mainName": null,
                "accountType": "TOTAL",
                "overAllowAmount": 0.00,
                "totalAmount": 16824.00,
                "availableAmount": 16274.00,
                "freezeAmount": 550.00,
                "createTime": null
            }
        ],
        "msg": ""
    })})
}
// 获得提现申请分页
export const getTakeCashPage = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": "1904468681080602624",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年03月",
                    "applyAmount": 1000.00,
                    "finishAmount": 0
                },
                {
                    "id": 26,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904468681068019712",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年02月",
                    "applyAmount": 15000.00,
                    "finishAmount": 11000.00
                },
                {
                    "id": 25,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 24,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 18,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/31e4054ef5a3bd0e1517962a310b13868c23acc32e94ba712dd637f53d0e0210.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "大大",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904468681034465280",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年01月",
                    "applyAmount": 4000.00,
                    "finishAmount": 4000.00
                },
                {
                    "id": 17,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/31e4054ef5a3bd0e1517962a310b13868c23acc32e94ba712dd637f53d0e0210.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "5",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "通过",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 15,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/1c58a06d529c3ea6b6227513be56099e862a08199ba94a48201af5c08af2b20e.jpeg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "76",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904468681281929216",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年03月",
                    "applyAmount": 1000.00,
                    "finishAmount": 0
                },
                {
                    "id": 26,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904468681265152000",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年02月",
                    "applyAmount": 15000.00,
                    "finishAmount": 11000.00
                },
                {
                    "id": 25,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 24,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 23,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": null,
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 19,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/31e4054ef5a3bd0e1517962a310b13868c23acc32e94ba712dd637f53d0e0210.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 18,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/31e4054ef5a3bd0e1517962a310b13868c23acc32e94ba712dd637f53d0e0210.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "大大",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904468681244180480",
                    "accountRole": null,
                    "mainId": null,
                    "mainName": null,
                    "accountId": null,
                    "takeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "accountInfo": null,
                    "type": "date",
                    "date": "2025年01月",
                    "applyAmount": 4000.00,
                    "finishAmount": 4000.00
                },
                {
                    "id": 17,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/31e4054ef5a3bd0e1517962a310b13868c23acc32e94ba712dd637f53d0e0210.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "5",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "通过",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 15,
                    "accountRole": "2",
                    "mainId": 1,
                    "mainName": "安阳网点",
                    "accountId": 26,
                    "takeAmount": 1000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/1c58a06d529c3ea6b6227513be56099e862a08199ba94a48201af5c08af2b20e.jpeg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "76",
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                }
            ],
            "total": 15
        },
        "msg": ""
    })})
}
// 获取网点/物流公司收款码
export const getMainQrCode = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "id": 1,
            "receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/7a91de64d43ec2260b77ff3792b24ec71ae569710ead8bff562203a3607d56b4.png",
            "receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/1ab2e52b0660151c925ef261a8620df351848a565b8466defbff13abd234af0f.png",
            "receiveThree": "https://dachisc.wang:9000/wljh-sanqian/bdac73eb40b5b82046788ea0974cb0500e2b421a862d40cc9800b2a245050f68.jpg",
            "receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/141e480512f20abd06a374d21e6409ccf0267257ced12fdd4925bb818d826da0.png",
            "accountPwd": null
        },
        "msg": ""
    })})
}
// 创建提现申请
export const createTakeCashApply = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 提现收款确认
export const takeCashConfirmCollect = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 获得充值申请分页
export const getRechargePage = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": "1904469255180156928",
                    "logisticsNetworkId": null,
                    "logisticsNetworkName": null,
                    "accountId": null,
                    "rechargeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "confirmUrl": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "type": "date",
                    "date": "2025年03月",
                    "applyAmount": 2000.00,
                    "finishAmount": 2000.00
                },
                {
                    "id": 13,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/bab699db953cb617e64d7f8c86810dd1dc22fe1bffd4b6562f6625deb358bc2e.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": "https://hwscm.com.cn:9000/wljh-hongwei/bab699db953cb617e64d7f8c86810dd1dc22fe1bffd4b6562f6625deb358bc2e.jpg",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904469255167574016",
                    "logisticsNetworkId": null,
                    "logisticsNetworkName": null,
                    "accountId": null,
                    "rechargeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "confirmUrl": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "type": "date",
                    "date": "2025年02月",
                    "applyAmount": 31000.00,
                    "finishAmount": 19000.00
                },
                {
                    "id": 12,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 10000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/243867523d31c3ed1b2e0492aaf18cb2e58557431c7bb3aa06b8f9ad3cdb5e56.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "confirmUrl": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 11,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/1c58a06d529c3ea6b6227513be56099e862a08199ba94a48201af5c08af2b20e.jpeg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "1",
                    "confirmUrl": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 10,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/2feb8f8f3cc65a7e5af4d4a0513d77f7fd2d61804cfe46d73efab89e7a40ca95.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": "https://hwscm.com.cn:9000/wljh-hongwei/bd1ce376125736d3d0b737f6f483fd1b9c21764837465b22cb2b6056e12b574b.jpg",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "12312",
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 9,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 5000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": "https://hwscm.com.cn:9000/wljh-hongwei/61cd684eb2564fe618f6ae38a04e80da67b5b2274b54b9ebd4513aea40405962.jpg",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "1231231",
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 6,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/76dc622c0b285c5d2a6f28257287e2f424c284b1b76bc806244e6b0d12e85948.jpeg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": null,
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 4,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 10000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "充这么多？",
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": "1904469255154991104",
                    "logisticsNetworkId": null,
                    "logisticsNetworkName": null,
                    "accountId": null,
                    "rechargeAmount": null,
                    "certUrl": null,
                    "applyUserId": null,
                    "applyUserName": null,
                    "applyTime": *************,
                    "applyStatus": null,
                    "confirmUrl": null,
                    "auditUserId": null,
                    "auditUserName": null,
                    "auditTime": null,
                    "auditRemark": null,
                    "createTime": null,
                    "type": "date",
                    "date": "2025年01月",
                    "applyAmount": 15000.00,
                    "finishAmount": 15000.00
                },
                {
                    "id": 3,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 8000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 2,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 5000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
                    "applyUserId": 140,
                    "applyUserName": "安阳",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": null,
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": null,
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                },
                {
                    "id": 1,
                    "logisticsNetworkId": 1,
                    "logisticsNetworkName": "安阳网点",
                    "accountId": 26,
                    "rechargeAmount": 2000.00,
                    "certUrl": "https://hwscm.com.cn:9000/wljh-hongwei/93d2c4cb00bc00aa6a9a830c954e7a1395ccf20787973f180b83dd66f84680cc.png",
                    "applyUserId": 1,
                    "applyUserName": "开发账户",
                    "applyTime": *************,
                    "applyStatus": "4",
                    "confirmUrl": null,
                    "auditUserId": 1,
                    "auditUserName": "开发账户",
                    "auditTime": *************,
                    "auditRemark": "",
                    "createTime": *************,
                    "type": "item",
                    "date": null,
                    "applyAmount": null,
                    "finishAmount": null
                }
            ],
            "total": 10
        },
        "msg": ""
    })})
}
// 创建充值申请
export const createRechargeApply = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 获取划转记录分页
export const getTransferPage = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": "1904469368292147200",
                    "accountId": null,
                    "relyAccountId": null,
                    "changeSource": null,
                    "changeAmount": null,
                    "amountBefore": null,
                    "amountAfter": null,
                    "amountTotalBefore": null,
                    "amountTotalAfter": null,
                    "changeTime": *************,
                    "userId": null,
                    "userName": null,
                    "businessId": null,
                    "createTime": null,
                    "accountInfo": null,
                    "relyAccountInfo": null,
                    "type": "date",
                    "date": "2025年02月",
                    "inAmount": 2086.00,
                    "outAmount": 1886.00
                },
                {
                    "id": 119,
                    "accountId": 26,
                    "relyAccountId": 30,
                    "changeSource": "3",
                    "changeAmount": -81.00,
                    "amountBefore": 18381.00,
                    "amountAfter": 18300.00,
                    "amountTotalBefore": 20155.00,
                    "amountTotalAfter": 20074.00,
                    "changeTime": *************,
                    "userId": 2,
                    "userName": null,
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 120,
                    "accountId": 30,
                    "relyAccountId": 26,
                    "changeSource": "3",
                    "changeAmount": 81.00,
                    "amountBefore": 565.00,
                    "amountAfter": 646.00,
                    "amountTotalBefore": 20074.00,
                    "amountTotalAfter": 20155.00,
                    "changeTime": *************,
                    "userId": 2,
                    "userName": null,
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 115,
                    "accountId": 28,
                    "relyAccountId": 13,
                    "changeSource": "3",
                    "changeAmount": 50.00,
                    "amountBefore": 1050.00,
                    "amountAfter": 1100.00,
                    "amountTotalBefore": 20324.00,
                    "amountTotalAfter": 20374.00,
                    "changeTime": *************,
                    "userId": 1,
                    "userName": "开发账户",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 28,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_RED_PACKET",
                        "overAllowAmount": 0.00,
                        "totalAmount": 1100.00,
                        "availableAmount": 1050.00,
                        "freezeAmount": 50.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "1",
                        "updater": "1",
                        "deleted": false,
                        "createDate": ********,
                        "id": 13,
                        "accountRole": "1",
                        "mainId": null,
                        "accountType": "PLATFORM_MARKET",
                        "overAllowAmount": 0.00,
                        "totalAmount": 600.00,
                        "availableAmount": 600.00,
                        "freezeAmount": 0.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 112,
                    "accountId": 27,
                    "relyAccountId": 26,
                    "changeSource": "3",
                    "changeAmount": -100.00,
                    "amountBefore": 200.00,
                    "amountAfter": 100.00,
                    "amountTotalBefore": 20324.00,
                    "amountTotalAfter": 20224.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 27,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_DIVIDE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 100.00,
                        "availableAmount": 100.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 113,
                    "accountId": 26,
                    "relyAccountId": 27,
                    "changeSource": "3",
                    "changeAmount": 100.00,
                    "amountBefore": 18500.00,
                    "amountAfter": 18600.00,
                    "amountTotalBefore": 20224.00,
                    "amountTotalAfter": 20324.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 27,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_DIVIDE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 100.00,
                        "availableAmount": 100.00,
                        "freezeAmount": 0.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 111,
                    "accountId": 26,
                    "relyAccountId": 28,
                    "changeSource": "3",
                    "changeAmount": 100.00,
                    "amountBefore": 18400.00,
                    "amountAfter": 18500.00,
                    "amountTotalBefore": 20224.00,
                    "amountTotalAfter": 20324.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 28,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_RED_PACKET",
                        "overAllowAmount": 0.00,
                        "totalAmount": 1100.00,
                        "availableAmount": 1050.00,
                        "freezeAmount": 50.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 110,
                    "accountId": 28,
                    "relyAccountId": 26,
                    "changeSource": "3",
                    "changeAmount": -100.00,
                    "amountBefore": 1150.00,
                    "amountAfter": 1050.00,
                    "amountTotalBefore": 20324.00,
                    "amountTotalAfter": 20224.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 28,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_RED_PACKET",
                        "overAllowAmount": 0.00,
                        "totalAmount": 1100.00,
                        "availableAmount": 1050.00,
                        "freezeAmount": 50.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 108,
                    "accountId": 26,
                    "relyAccountId": 30,
                    "changeSource": "3",
                    "changeAmount": -25.00,
                    "amountBefore": 18425.00,
                    "amountAfter": 18400.00,
                    "amountTotalBefore": 20324.00,
                    "amountTotalAfter": 20299.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 109,
                    "accountId": 30,
                    "relyAccountId": 26,
                    "changeSource": "3",
                    "changeAmount": 25.00,
                    "amountBefore": 540.00,
                    "amountAfter": 565.00,
                    "amountTotalBefore": 20299.00,
                    "amountTotalAfter": 20324.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 30,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_BROKERAGE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 736.00,
                        "availableAmount": 736.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 26,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_PRE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 14879.00,
                        "availableAmount": 14379.00,
                        "freezeAmount": 500.00
                    },
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                },
                {
                    "id": 103,
                    "accountId": 27,
                    "relyAccountId": null,
                    "changeSource": "3",
                    "changeAmount": -80.00,
                    "amountBefore": 280.00,
                    "amountAfter": 200.00,
                    "amountTotalBefore": 0.00,
                    "amountTotalAfter": 0.00,
                    "changeTime": *************,
                    "userId": 140,
                    "userName": "安阳",
                    "businessId": null,
                    "createTime": *************,
                    "accountInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "150",
                        "updater": "150",
                        "deleted": false,
                        "createDate": ********,
                        "id": 27,
                        "accountRole": "2",
                        "mainId": 1,
                        "accountType": "NETWORK_FREIGHT_DIVIDE",
                        "overAllowAmount": 0.00,
                        "totalAmount": 100.00,
                        "availableAmount": 100.00,
                        "freezeAmount": 0.00
                    },
                    "relyAccountInfo": null,
                    "type": "item",
                    "date": null,
                    "inAmount": null,
                    "outAmount": null
                }
            ],
            "total": 17
        },
        "msg": ""
    })})
}
// 操作划转
export const transferAccount = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}

// 获取当前小程序用户代理的网点列表
export const getMyBrokerNetworkPageApp = (params : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": 8,
                    "name": "New网点XX物流",
                    "address": "地球村",
                    "organizationClass": "2",
                    "organizationCode": "5455",
                    "organizationType": "CRED_ORG_REGCODE",
                    "organizationPhone": "***********",
                    "organizationEmail": null,
                    "organizationBankCardNum": null,
                    "corporationName": null,
                    "corporationPhone": null,
                    "corporationEmail": null,
                    "corporationIdentityNum": null,
                    "corporationIdentityType": null,
                    "principalName": "张二三",
                    "principalPhone": "***********",
                    "principalEmail": null,
                    "principalIdentityNum": "******************",
                    "principalIdentityType": "CRED_PSN_CH_IDCARD",
                    "networkRank": "1",
                    "startStatus": "1",
                    "certification": "1",
                    "isAccount": "1",
                    "deptId": 132,
                    "networkCode": "WD20250306006",
                    "allowBrokerage": "0",
                    "receiveOne": "https://dachisc.wang:9000/wljh-sanqian/18302c7f815b7e059ac72d9093eafea0af6564acc3d532e2c8eb6460bf7a4164.jpg",
                    "receiveTwo": "https://dachisc.wang:9000/wljh-sanqian/ea5286da6f8438da352fc9323d46eecafbf97c409a945ee36f61b7dc7e86f3ed.jpg",
                    "receiveThree": "https://dachisc.wang:9000/wljh-sanqian/cac0d676018109e971152ac52703f05bd4f694fdfed0f0e973031b3fec3e9ec4.jpg",
                    "receiveFive": "https://dachisc.wang:9000/wljh-sanqian/df1bce996826ff9184e5a50a1de269b2dfd4add2e3c31e10ad946ac79d9c5a06.jpg",
                    "receiveCodeAudit": "2",
                    "brokerId": 140,
                    "principalAuditStatus": "1",
                    "levelSort": 3,
                    "accountPwdStatus": null,
                    "createTime": *************,
                    "levelInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "1",
                        "updater": "1",
                        "deleted": false,
                        "createDate": ********,
                        "id": 3,
                        "objectType": "1",
                        "levelCode": "V3",
                        "levelSort": 3,
                        "levelImg": "https://dachisc.wang:9000/wljh-sanqian/bf8f24f916d4c282b12b55098683e9f708d221969efb1cbd7acdb8ff97e5f502.svg",
                        "levelName": "达标网点",
                        "standardMonthDayAmount": 1000.00,
                        "standardMonthTotalAmount": null,
                        "standardAddSignNum": null,
                        "rewardHonor": "V3晋级突破奖",
                        "rewardMoney": 1000.00,
                        "keepRewardHonor": "XX月度达标网点\nXX季度达标网点\nXX年度达标网点",
                        "keepRewardMaterial": "打印设备包",
                        "keepRewardMoney": null,
                        "keepRewardYear": null,
                        "keepRewardStock": null,
                        "openLimitAmount": null
                    }
                },
                {
                    "id": 7,
                    "name": "编号网点",
                    "address": "河南省洛阳市",
                    "organizationClass": null,
                    "organizationCode": "JG16677",
                    "organizationType": "CRED_ORG_USCC",
                    "organizationPhone": "***********",
                    "organizationEmail": null,
                    "organizationBankCardNum": null,
                    "corporationName": null,
                    "corporationPhone": null,
                    "corporationEmail": null,
                    "corporationIdentityNum": null,
                    "corporationIdentityType": null,
                    "principalName": "",
                    "principalPhone": "",
                    "principalEmail": null,
                    "principalIdentityNum": null,
                    "principalIdentityType": null,
                    "networkRank": "1",
                    "startStatus": "0",
                    "certification": "0",
                    "isAccount": "0",
                    "deptId": 126,
                    "networkCode": "WD20241217007",
                    "allowBrokerage": "0",
                    "receiveOne": null,
                    "receiveTwo": null,
                    "receiveThree": null,
                    "receiveFive": null,
                    "receiveCodeAudit": null,
                    "brokerId": 140,
                    "principalAuditStatus": "0",
                    "levelSort": 0,
                    "accountPwdStatus": null,
                    "createTime": *************,
                    "levelInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "1",
                        "updater": "1",
                        "deleted": false,
                        "createDate": ********,
                        "id": 13,
                        "objectType": "1",
                        "levelCode": "V0",
                        "levelSort": 0,
                        "levelImg": "https://dachisc.wang:9000/wljh-sanqian/df0269b30f9d3d51dbd1ebd2249d3ec90aa86efde8422481780de3a7f94f186e.png",
                        "levelName": "试用网点",
                        "standardMonthDayAmount": 0.00,
                        "standardMonthTotalAmount": 0.00,
                        "standardAddSignNum": null,
                        "rewardHonor": null,
                        "rewardMoney": null,
                        "keepRewardHonor": null,
                        "keepRewardMaterial": null,
                        "keepRewardMoney": null,
                        "keepRewardYear": null,
                        "keepRewardStock": null,
                        "openLimitAmount": null
                    }
                },
                {
                    "id": 4,
                    "name": "测试网点一",
                    "address": "河南省郑州市高新区1",
                    "organizationClass": null,
                    "organizationCode": "********",
                    "organizationType": "CRED_ORG_USCC",
                    "organizationPhone": "***********",
                    "organizationEmail": null,
                    "organizationBankCardNum": "",
                    "corporationName": null,
                    "corporationPhone": null,
                    "corporationEmail": null,
                    "corporationIdentityNum": null,
                    "corporationIdentityType": null,
                    "principalName": "张三",
                    "principalPhone": "***********",
                    "principalEmail": null,
                    "principalIdentityNum": "******************",
                    "principalIdentityType": "CRED_PSN_CH_IDCARD",
                    "networkRank": "1",
                    "startStatus": "1",
                    "certification": "0",
                    "isAccount": "1",
                    "deptId": 121,
                    "networkCode": "WD20241119004",
                    "allowBrokerage": "0",
                    "receiveOne": null,
                    "receiveTwo": null,
                    "receiveThree": null,
                    "receiveFive": null,
                    "receiveCodeAudit": "1",
                    "brokerId": 140,
                    "principalAuditStatus": "1",
                    "levelSort": 1,
                    "accountPwdStatus": null,
                    "createTime": *************,
                    "levelInfo": {
                        "createTime": *************,
                        "updateTime": *************,
                        "creator": "1",
                        "updater": "1",
                        "deleted": false,
                        "createDate": ********,
                        "id": 1,
                        "objectType": "1",
                        "levelCode": "V1",
                        "levelSort": 1,
                        "levelImg": "https://dachisc.wang:9000/wljh-sanqian/bcd7def54b45c7ad5e67da681b99694b590ea40d9a9adf44225ee72d824d79cf.svg",
                        "levelName": "基础网点",
                        "standardMonthDayAmount": 300.00,
                        "standardMonthTotalAmount": null,
                        "standardAddSignNum": null,
                        "rewardHonor": "V1晋级突破奖",
                        "rewardMoney": null,
                        "keepRewardHonor": null,
                        "keepRewardMaterial": null,
                        "keepRewardMoney": null,
                        "keepRewardYear": null,
                        "keepRewardStock": null,
                        "openLimitAmount": null
                    }
                }
            ],
            "total": 3
        },
        "msg": ""
    })})
}

// 获取当前小程序用户代理的网点列表
export const saveNetworkInfoApp = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 网点负责人注册
export const registerNetwork = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}

// 获取个人认证信息
export const getPrincipalApprovalInfo = (params : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "id": null,
            "logisticsNetworkId": 1,
            "logisticsNetworkName": "安阳网点",
            "networkAccountId": 2,
            "networkAccountName": "安阳",
            "approvalStatus": null,
            "principalName": "杨一一",
            "principalIdentityNum": "******************",
            "principalIdentityType": "CRED_PSN_CH_IDCARD",
            "identityPositive": "https://dachisc.wang:9000/wljh-sanqian/876d2bb3f2a80fe4ffaa73f93dace084d4a4eefe5cc7ae00e240e7971fbc49fc.jpeg",
            "identityBack": "https://dachisc.wang:9000/wljh-sanqian/acbc63bfa981cf627e6af78f77b214f5436a593c8dd99d09238745e08756986b.jpeg",
            "createTime": null
        },
        "msg": ""
    })})
}
// 获取当前经纪人代理的个人认证审核分页
export const principalApprovalPage = (params : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "list": [
                {
                    "id": 4,
                    "logisticsNetworkId": 8,
                    "logisticsNetworkName": "New网点XX物流",
                    "networkAccountId": 13,
                    "networkAccountName": "New网点",
                    "approvalStatus": "0",
                    "principalName": "张二三",
                    "principalIdentityNum": "******************",
                    "principalIdentityType": "CRED_PSN_CH_IDCARD",
                    "identityPositive": "https://dachisc.wang:9000/wljh-sanqian/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
                    "identityBack": "https://dachisc.wang:9000/wljh-sanqian/76dc622c0b285c5d2a6f28257287e2f424c284b1b76bc806244e6b0d12e85948.jpeg",
                    "createTime": *************
                },
                {
                    "id": 3,
                    "logisticsNetworkId": 8,
                    "logisticsNetworkName": "New网点XX物流",
                    "networkAccountId": 13,
                    "networkAccountName": "New网点",
                    "approvalStatus": "1",
                    "principalName": "张二三",
                    "principalIdentityNum": "******************",
                    "principalIdentityType": "CRED_PSN_CH_IDCARD",
                    "identityPositive": "https://dachisc.wang:9000/wljh-sanqian/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
                    "identityBack": "https://dachisc.wang:9000/wljh-sanqian/76dc622c0b285c5d2a6f28257287e2f424c284b1b76bc806244e6b0d12e85948.jpeg",
                    "createTime": *************
                },
                {
                    "id": 2,
                    "logisticsNetworkId": 8,
                    "logisticsNetworkName": "New网点XX物流",
                    "networkAccountId": 13,
                    "networkAccountName": "New网点",
                    "approvalStatus": "2",
                    "principalName": "张三三",
                    "principalIdentityNum": "******************",
                    "principalIdentityType": "CRED_PSN_CH_IDCARD",
                    "identityPositive": "https://dachisc.wang:9000/wljh-sanqian/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
                    "identityBack": "https://dachisc.wang:9000/wljh-sanqian/76dc622c0b285c5d2a6f28257287e2f424c284b1b76bc806244e6b0d12e85948.jpeg",
                    "createTime": *************
                }
            ],
            "total": 3
        },
        "msg": ""
    })})
}
// 保存个人认证信息
export const savePrincipalApproval = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}
// 个人认证审核
export const auditPrincipalApproval = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}

// 获取网点认证信息
export const getNetworkAuthInfo = (params : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": {
            "id": null,
            "logisticsNetworkId": 1,
            "approvalStatus": null,
            "createTime": null,
            "name": "安阳网点",
            "address": "河南省郑州市二七区",
            "identityPositive": null,
            "identityBack": null,
            "orgPositive": "https://dachisc.wang:9000/wljh-sanqian/61cd684eb2564fe618f6ae38a04e80da67b5b2274b54b9ebd4513aea40405962.jpg",
            "organizationClass": "1",
            "organizationCode": "1111226783",
            "organizationType": "CRED_ORG_USCC"
        },
        "msg": ""
    })})
}
// 保存网点认证信息
export const saveNetworkAuth = (data : any) => {
    return new Promise((resolve, reject) => {resolve({
        "code": 0,
        "data": true,
        "msg": ""
    })})
}


