<template>
	<view class="layout-container">
		<!-- 中部 -->
    <!--<view class="yj-body">
      <view class="yj-card" v-for="item in dataList" :key="item.id">
        <view class="box-dis">
          <up-text text="服务商分配账号：" color="#606266" align="left"></up-text>
          <up-text :text="item.account" align="right" lines="1"></up-text>
        </view>
        <view class="box-dis">
          <up-text text="物流服务商名称：" color="#606266" align="left"></up-text>
          <up-text :text="item.name" align="right" lines="1"></up-text>
        </view>
        <view class="box-dis">
          <up-text text="物流服务商地址：" color="#606266" align="left"></up-text>
          <up-text :text="item.address" align="right" lines="1"></up-text>
        </view>
      </view>
    </view>-->

    <view class="yj-body">
      <view class="custom-tabs-menu">
        <up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" lineWidth="78"
                 :itemStyle="tabsItemStyle" @click="handleTabsClick"></up-tabs>
      </view>
      <view style="height: calc(100% - 140rpx);overflow: auto">
        <view class="yj-card" v-for="item in dataList" :key="item.id">
          <view class="box-dis">
            <up-text text="物流服务商名称：" color="#606266" align="left"></up-text>
            <up-text :text="item.name" align="right" lines="1"></up-text>
          </view>
          <view class="box-dis">
            <up-text text="物流服务商地址：" color="#606266" align="left"></up-text>
            <up-text :text="item.address" align="right" lines="1"></up-text>
          </view>
          <up-row>
            <up-col :span="8" customStyle="color: #606266;">
              <text>物流服务商运单查询电话：</text>
            </up-col>
            <up-col :span="4" customStyle="text-align: right">
              <text>{{ item.organizationPhone ? item.organizationPhone : '' }}</text>
            </up-col>
          </up-row>
        </view>
      </view>
    </view>


    <!-- 底部 -->
<!--		<view class="yj-footer">
			<view class="butn">
				<up-button :plain="true" text="返回" shape="circle"
					customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleBlack"></up-button>
			</view>
		</view>-->
	</view>


</template>

<script setup>
import {
  onMounted,
  getCurrentInstance,
  ref, reactive
} from 'vue'
	import {
		getOuterInfo
	} from '@/api/wljh/mine'

	const dataList = ref([]) // 展示的物流服务商信息
	const dataListAll = ref([]) // 全部物流服务商信息
	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()


  const tabsSelected = ref('ed') // tab选中Key
  // tab切换栏菜单数据
  const tabsList = ref([
      {
        name: '已开通',
        key: 'ed'
      },
      {
        name: '可开通',
        key: 'no'
      }
      ])
  // tabs 切换事件
  const handleTabsClick = (item, index) => {
    tabsSelected.value = item.key
    filterShowData()
  }

	// 获取物流商信息
	const getOuterInfoApi = async (id) => {
		await getOuterInfo(id).then(res => {
			console.log(res)
			if (res.code === 0) {
        dataListAll.value = res.data
        filterShowData()
			}
		})
	}
  const filterShowData = () => {
    if (tabsSelected.value === 'ed') {
      dataList.value = dataListAll.value.filter(item => item.openFlag === '1')
    } else if (tabsSelected.value === 'no') {
      dataList.value = dataListAll.value.filter(item => item.openFlag === '0')
    }
  }
	
	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}

	onMounted(() => {
		eventChannel.on('params', function(data) {
			if (data.name) {
				getOuterInfoApi(data.name)
			}
		})
	})

// 样式
const tabsItemStyle = reactive({
  width: '160rpx',
  height: '100rpx'
})
</script>

<style scoped lang="scss">
	.layout-container {
		height: calc(100vh - var(--window-top) );

		.yj-body {
			height: calc(100% );
			overflow: auto;
			padding: 20rpx;
			background-color: #f1f1f1;

      .custom-tabs-menu {
        background-color: #fff;
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 -20rpx;
        margin-top: -20rpx;
      }

			.yj-card {
				display: flex;
				flex-direction: column;
				background-color: #fff;
				border-radius: 10rpx;
				gap: 20rpx;
				padding: 20rpx;
				margin-top: 20rpx;

				.box-dis {
					display: flex;
				}
			}
		}

		.yj-footer {
			height: 140rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #fff;
			display: flex;
			padding: 20rpx;

			.butn {
				display: flex;
				align-items: center;
				width: 100%
			}
		}
	}
</style>