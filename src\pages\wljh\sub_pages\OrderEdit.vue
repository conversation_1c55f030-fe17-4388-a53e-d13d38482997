<template>
	<view class="layout-sub-orderEdit">
		<view class="custom-body">
			<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
				<up-form-item label="寄货人" prop="sendName" borderBottom="true">
					<up-input v-model="formData.sendName" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="手机号" prop="sendPhone" borderBottom="true">
					<up-input v-model="formData.sendPhone" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="寄货地址" prop="sendAddress" borderBottom="true">
					<up-input v-model="formData.sendAddress" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="收货人" prop="collectName" borderBottom="true">
					<up-input v-model="formData.collectName" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="手机号" prop="collectPhone" borderBottom="true">
					<up-input v-model="formData.collectPhone" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="收获地址" prop="collectAddress" borderBottom="true">
					<up-input v-model="formData.collectAddress" border="none" inputAlign="right"></up-input>
				</up-form-item>
				<up-form-item label="物品类型" prop="goodsType" borderBottom="true" @click="handleGoodsTypeFn">
					<up-input v-model="formData.goodsTypeLabel" readonly border="none" inputAlign="right"></up-input>
					<template #right>
						<up-icon name="arrow-right"></up-icon>
					</template>
				</up-form-item>
				<up-form-item label="总重量" prop="totalWeight" borderBottom="true">
					<up-input v-model="formData.totalWeight" border="none" inputAlign="right"></up-input>
					<template #right><span>Kg</span></template>
				</up-form-item>
				<up-form-item label="总体积" prop="totalVolume" borderBottom="true">
					<up-input v-model="formData.totalVolume" border="none" inputAlign="right"></up-input>
					<template #right><span>m³</span></template>
				</up-form-item>
				<up-form-item label="总件数" prop="totalNum" borderBottom="true">
					<up-input v-model="formData.totalNum" border="none" inputAlign="right"></up-input>
					<template #right><span>件</span></template>
				</up-form-item>
				<up-form-item label="物流公司" prop="logisticsCompaniesId" borderBottom="true">
					<up-input v-model="formData.logisticsCompaniesId" readonly border="none"
						inputAlign="right"></up-input>
					<template #right>
						<up-icon name="arrow-right"></up-icon>
					</template>
				</up-form-item>
				<up-form-item label="付款方式" prop="payMethod" borderBottom="true" @click="payMethodFn">
					<up-input v-model="formData.payMethodLabel" readonly border="none" inputAlign="right"></up-input>
					<template #right>
						<up-icon name="arrow-right"></up-icon>
					</template>
				</up-form-item>
				<!-- <up-form-item label="应付金额" prop="shouldPayAmount" borderBottom="true">
					<up-input v-model="formData.shouldPayAmount" readonly border="none" inputAlign="right"></up-input>
					<template #right><span>元</span></template>
				</up-form-item> -->
				<up-form-item label="实付金额" prop="actualPayAmount" borderBottom="true">
					<up-input v-model="formData.actualPayAmount" border="none" inputAlign="right"></up-input>
					<template #right><span>元</span></template>
				</up-form-item>
			</up-form>


			<up-action-sheet :show="showSheet" :actions="actionsSheet" title="请选择物品类型" @close="handleActionSheetClose"
				@select="handleActionSheetSelect">
			</up-action-sheet>

			<view class="custom-btn">
				<up-button type="primary" text="提交" @click="handleSubmit"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref
	} from 'vue';
	import {
		getDictDataInfoAll,
		updateOrderInfo
	} from '@/api/wljh/task'
	import modal from '@/plugins/modal'

	const actionsSheet = ref([]) // 选择参数值
	const showSheet = ref(false) // 选择参数框是否显示
	const formData = ref({ // 参数
		id: undefined,
		sendName: undefined,
		sendPhone: undefined,
		sendAddress: undefined,
		collectName: undefined,
		collectPhone: undefined,
		goodsType: undefined,
		totalWeight: undefined,
		totalVolume: undefined,
		totalNum: undefined,
		logisticsCompaniesId: undefined,
		payMethod: undefined,
		shouldPayAmount: undefined,
		actualPayAmount: undefined,
		goodsTypeLabel: undefined,
		payMethodLabel: undefined,
	})
	// 获取单个字典标签
	const getDictLable = async (dictType, value) => {
		await getDictDataInfoAll({
			'type': dictType,
			'value': value
		}).then(res => {
			if (res.code === 0) {
				switch (dictType) {
					case 'GOODS_TYPE':
						formData.value.goodsTypeLabel = res.data[0].label
						break;
					case 'PAY_METHOD':
						formData.value.payMethodLabel = res.data[0].label
						break;
				}
			}
		})
	}
	// 获取字典所有值
	const getDictDataInfo = async (dictType) => {
		await getDictDataInfoAll({
			'type': dictType
		}).then(res => {
			if (res.code === 0) {
				actionsSheet.value = res.data.map(item => {
					let param = {
						'name': item.label,
						'text': item.value,
						'dictType': dictType
					}
					return param
				})
			}
		})
	}
	// 提交事件
	const handleSubmit = async () => {
		await updateOrderInfo(formData.value).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('订单修改成功')
				uni.navigateBack();
			}
		})
	}
	// 物品类型点击事件
	const handleGoodsTypeFn = () => {
		showSheet.value = true
		getDictDataInfo('GOODS_TYPE')
	}
	// 付款方式点击事件
	const payMethodFn = () => {
		showSheet.value = true
		getDictDataInfo('PAY_METHOD')
	}
	// 选择参数框关闭事件
	const handleActionSheetClose = () => {
		showSheet.value = false
		actionsSheet.value = []
	}
	// 选择参数框选中事件
	const handleActionSheetSelect = (item) => {
		switch (item.dictType) {
			case 'GOODS_TYPE':
				formData.value.goodsTypeLabel = item.name
				formData.value.goodsType = item.text
				break;
			case 'PAY_METHOD':
				formData.value.payMethodLabel = item.name
				formData.value.payMethod = item.text
				break;
		}
	}

	onMounted(() => {
		const instance = getCurrentInstance().proxy
		const eventChannel = instance.getOpenerEventChannel();

		eventChannel.on('orderData', function(data) {
			getDictLable('GOODS_TYPE', data.data.goodsType)
			getDictLable('PAY_METHOD', data.data.payMethod)
			formData.value = data.data
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-orderEdit {
		background-color: #f1f1f1;
		// height: 100vh;
		height: 100vh;
		overflow: auto;
		padding: 0 20rpx;

		.custom-body {
			background-color: #fff;
			padding: 0 20rpx;
			padding-bottom: 20rpx;
			margin: 20rpx 0;
			border-radius: 5px;

			.custom-btn {
				margin-top: 20rpx;
				
				:deep(.u-button--square) {
					border-radius: 20px;
				}
				
				:deep(.u-button--primary) {
					background-color: #d81e06;
					border: #d81e06;
				}
				
				:deep(.u-button) {
					height: 45px;
				}
			}

			:deep(.u-form-item__body__left) {
				margin-right: 10rpx;
			}

			:deep(.u-input) {
				margin-right: 5px;
			}
			
			:deep(.u-form-item__body__left__content__label) {
				color: #606266;
			}
		}
	}
</style>