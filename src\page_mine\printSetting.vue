<template>
	<view class="layout-container">
		<!-- 中部 -->
		<view class="yj-body">
			<view class="custom-tabs-menu">
				<up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" lineWidth="58"
					:itemStyle="tabsItemStyle" @click="handleTabsClick"></up-tabs>
			</view>
			<!-- 蓝牙设备配置 -->
			<view class="body-box" v-if="tabsSelected === 'blue'">
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>已配置</view>
					</view>
					<view class="upload-box-btn">
						<view class="yj-card" v-if="defaultBlue">
							<view class="box-dis">
								<up-text :text="defaultBlue.name + '【'+defaultBlue.brandName+'】'" color="#606266" align="left"></up-text>
                <up-icon name="close" color="#d81e06" size="18" @click="removeBlue()"></up-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>未配置</view>
					</view>
					<view class="upload-box-btn">
						<view v-for="item in blueList" :key="item.deviceId">
							<view class="yj-card-2" v-if="item.connectable && item.name!='未知设备' && item.name"
								@click="handleConnetBlue(item)">
								<view class="box-dis">
									<up-text :text="item.name" color="#606266" align="left"></up-text>
								</view>
							</view>
						</view>
						<up-loadmore v-if="lodingShow==='loading' && tabsSelected==='blue'" :status="lodingShow"
							:loading-text="lodingText" />
					</view>
				</view>
			</view>

			<!-- WiFi设备配置 -->
			<view class="body-box" v-if="tabsSelected === 'wifi'">
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>已配置</view>
					</view>
					<view class="upload-box-btn">
						<view class="yj-card" v-if="defaultWifi">
							<view class="box-dis">
								<up-text :text="defaultWifi.DEVICENAME + '【'+defaultWifi.brandName+'】'" color="#606266" align="left"></up-text>
                <up-icon name="close" color="#d81e06" size="18" @click="removeWifi()"></up-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>未配置</view>
					</view>
					<view class="upload-box-btn">
						<view v-for="item in wifiList" :key="item.ID">
							<view class="yj-card-2" @click="handleConnetWifi(item)">
								<view class="box-dis">
									<up-text :text="item.DEVICENAME" color="#606266" align="left"></up-text>
								</view>
							</view>
						</view>
						<up-loadmore v-if="lodingShow==='loading' && tabsSelected==='wifi'" :status="lodingShow"
							:loading-text="lodingText" />
					</view>
				</view>
			</view>
		</view>
		<!-- 底部 -->
		<view class="yj-footer">
			<view class="butn">
				<up-button v-if="lodingShow!='loading'" :plain="true" text="搜索" shape="circle"
					customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleQuery"></up-button>

				<up-button v-if="lodingShow==='loading'" :plain="true" text="停止搜索" shape="circle"
					customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleStopQuery"></up-button>

<!--				<up-button :plain="true" text="返回" shape="circle" customStyle="color:#000;height:45px;"
					@click="handleBlack"></up-button>-->
			</view>
		</view>

		<up-popup v-model:show="printerInfoShow">
			<view class="printer-box">
				<view class="printer-box-title">
					<up-text text="请选择打印机品牌"></up-text>
				</view>
				<up-scroll-list :indicator="false">
					<view class="custom-card-body-item-tag" v-for="(item, index) in printerInfoList" :key="index">
						<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'" :text="item.brandName"
							size="mini" :plain="!item.checked" :name="index" @click="handlePopupCardFn(index)"></up-tag>
					</view>
				</up-scroll-list>
				<view class="printer-box-title">
					<up-text type="error" text="未找到打印机？" align="right" @click="handleFindError"></up-text>
				</view>
				<view>
					<up-button :plain="true" text="确定" shape="circle"
						customStyle="color:#fff;height:45px;backgroundColor: #d81e06;"
						@click="handlePrinterClick"></up-button>
				</view>
			</view>
		</up-popup>
		<!-- 加载页 -->
		<!-- <up-loading-page :loading="lodingShow" :loading-text="lodingText" bg-color="#9093995e" loading-color="#000000"
			color="#000" loadingMode="spinner" iconSize="35"></up-loading-page> -->
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref
	} from 'vue'
	import modal from '@/plugins/modal'
	import customStorage from "@/utils/customStorage"
	import PrinterJobs from '@/utils/print/printerjobs'
	import {
		getByParentId
	} from '@/api/wljh/task'
	//import myBase64 from '../utils/base64.js'
	//const Base64Tool = myBase64.Base64Tool

	const blueList = ref([]) // 蓝牙设备列表
	const wifiList = ref([]) // wifi设备列表
	const defaultBlue = ref() // 已配置-蓝牙
	const defaultWifi = ref() // 已配置-WiFi

	const tabsSelected = ref('blue') // tab选中Key
	const tabsList = ref([{ // tab切换栏菜单数据
			name: '蓝牙',
			key: 'blue'
		},
		{
			name: 'WI-FI',
			key: 'wifi'
		}
	])

	const lodingShow = ref(false) // 加载页
	const lodingText = ref('正在搜索蓝牙设备') // 加载内容
	const isStopWifi = ref(false) // 是否停止WiFi搜索设备

	const printerInfoShow = ref(false)
	const printerInfoList = ref([])
	const checkedInfo = ref(undefined)

	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
		stopBlue()
	}

	// tabs 切换事件
	const handleTabsClick = (item, index) => {
		tabsSelected.value = item.key
		if (item.key === 'blue') {
			stopWifi()
		} else if (item.key === 'wifi') {
			stopBlue()
		}
	}

	// 搜索事件
	const handleQuery = () => {
		printerInfoShow.value = true
		getPrinterInfoApi()
		checkedInfo.value = undefined
	}
	// 停止搜索事件
	const handleStopQuery = () => {
		if (tabsSelected.value === 'blue') {
			stopBlue()
		} else if (tabsSelected.value === 'wifi') {
			stopWifi()
		}
	}
  // 移除当前配置的蓝牙
  const removeBlue = () => {
    if (customStorage.get("BlueInfo")) {
      uni.closeBLEConnection({
        deviceId: customStorage.get("BlueInfo").deviceId,
        success(res) {
          console.log(' 断开连接成功', res)
        },
        fail(err) {
          console.log(' 断开连接失败', err)
        }
      })
      defaultBlue.value = undefined
      customStorage.remove("BlueInfo")
      modal.msgSuccess('已取消配置')
    }
    uni.$emit('printerChange', {
      msg: 'removeBlue',
    });
  }
	// 连接蓝牙事件
	const handleConnetBlue = (device) => {
    // 先移除当前配置的蓝牙
    removeBlue()
		modal.msgError('正在连接蓝牙：' + device.name)
		connetBlue(device)
	}
  // 移除当前配置的wifi
  const removeWifi = () => {
    if (customStorage.get("WifiInfo")) {
      defaultWifi.value = undefined
      customStorage.remove("WifiInfo")
      modal.msgSuccess('已取消配置')
    }
    uni.$emit('printerChange', {
      msg: 'removeWifi',
    });
  }
	// 连接WiFi打印机事件
	const handleConnetWifi = (obj) => {
    // 先移除当前配置的wifi
    removeWifi()
		modal.msgError('正在连接打印机：' + obj.DEVICENAME)
		connetWifi(obj)
	}

	// 初始化
	const initFn = () => {
		// 信息初始化
		blueList.value = []
		wifiList.value = []
		tabsSelected.value = 'blue'
		lodingShow.value = false
		defaultBlue.value = customStorage.get("BlueInfo") ? customStorage.get("BlueInfo") : undefined
		defaultWifi.value = customStorage.get("WifiInfo") ? customStorage.get("WifiInfo") : undefined
	}

	// 搜索蓝牙
	const findBlue = () => {
		// 初始化蓝牙模块
		uni.openBluetoothAdapter({
			success: async (res) => {
				console.log("蓝牙初始化成功", res)
				lodingShow.value = 'loading'
				lodingText.value = '正在搜索蓝牙设备'
				await getPrinterInfoApi(checkedInfo.value.id)
				// 搜索蓝牙设备
				uni.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: true,
					interval: 0,
					success: function(res) {
						console.log('搜索蓝牙设备', res)
						// 监听寻找新设备事件
						uni.onBluetoothDeviceFound((res) => {
							if (res && res.devices.length > 0) {
                console.log('新设备', res)
                let isChongfu = false
                if (blueList.value.length > 0) {
                  blueList.value.map(item => {
                    if (item.deviceId === res.devices[0].deviceId) {
                      isChongfu = true
                    }
                  })
                }
                if (!isChongfu) {
                  //blueList.value.push(res.devices[0])
                  for (var i = 0; i < printerInfoList.value.length; i++) {
                    let indexCode = res.devices[0].name.indexOf(printerInfoList.value[i].brandCode)
                    if (printerInfoList.value[i].brandCode === '') {
                      indexCode = -1
                    }
                    let indexBluetooth = res.devices[0].name.indexOf(printerInfoList.value[i].bluetoothName)
                    if (printerInfoList.value[i].bluetoothName === '') {
                      indexBluetooth = -1
                    }
                    if(indexCode >= 0 || indexBluetooth >= 0) {
                      let data = res.devices[0]
                      data['brandName'] = printerInfoList.value[i].brandName
                      data['instructCode'] = printerInfoList.value[i].instructCode
                      data['chunkSize'] = printerInfoList.value[i].chunkSize || 20
                      blueList.value.push(data)
                      console.log('匹配到新设备', JSON.stringify(data))
                      break
                    }
                  }
                }

							}
						})
					},
					fail: function(err) {
						console.log("开始搜索失败", err)
					}
				})
			},
			fail: (err) => {
				console.log("蓝牙初始化失败", err)
				if (err.errCode === 10001) {
					modal.msgError('请检查蓝牙功能是否打开')
				}
			}
		})
	}
	// 关闭蓝牙搜索事件
	const stopBlue = () => {
		lodingShow.value = false
		uni.stopBluetoothDevicesDiscovery({
			success: function(res) {
				console.log('关闭蓝牙搜索成功');
			}
		})
	}
	// 连接蓝牙设备
	const connetBlue = (device) => {
		//console.log(device)
		uni.createBLEConnection({
			deviceId: device.deviceId, //设备id
			success(res) {
				//console.log("连接蓝牙成功", res)
				defaultBlue.value = device
				//defaultBlue.value['brandName'] = checkedInfo.value.brandName
				//defaultBlue.value['instructCode'] = checkedInfo.value.instructCode
				customStorage.set("BlueInfo", defaultBlue.value)
				modal.msgSuccess('蓝牙：' + device.name + '连接成功')
        uni.$emit('printerChange', {
          msg: 'connectBlue',
        });
				stopBlue()
			},
			fail(err) {
				//console.log("连接蓝牙失败", err)
				if (err.errCode === -1) {
					const blueInfo = customStorage.get("BlueInfo")
					if (device.deviceId === blueInfo.deviceId) {
						modal.msgSuccess('蓝牙：' + device.name + '已连接')
					} else {
						uni.closeBLEConnection({
							deviceId: device.deviceId,
							success(res) {
								connetBlue(device)
							}
						})
					}

				} else if (err.errCode === 10012) {
					modal.msgError('连接超时')
				} else if (err.errCode === 10011) {

				}
			}
		})
	}

	// 搜索当前WiFi下打印机
	const findWifi = () => {
		uni.startWifi({
			success(res) {
				uni.getConnectedWifi({
					success(res) {
						getLocalService()
					},
					fail(err) {
						modal.msgError('请先连接区域WiFi')
					}
				})
			},
			fail(err) {
				console.log("WiFi初始化失败", err)
				if (err.errCode === 12007) {
					modal.msgError('用户拒绝授权')
				} else if (err.errCode === 12001) {
					modal.msgError('当前系统不支持相关能力')
				}
			}
		})
	}
	// 获取局域网内广播打印机
	const getLocalService = () => {
		lodingShow.value = 'loading'
		lodingText.value = '正在搜索附近打印机'
		const udpSocket = wx.createUDPSocket('udp4')
		udpSocket.bind()

		udpSocket.send({
			address: '***************',
			port: 3000,
			message: 'B[]G'
		})
		udpSocket.onMessage(async (res) => {
			// const decoder = new TextDecoder('utf-8');
			// const str = decoder.decode(res.message);
			// const arrayBuffer = new Uint8Array(res.message)
			// const base64 = wx.arrayBufferToBase64(arrayBuffer)
			// let str = Base64Tool.decoder(base64)
			let unit8Arr = new Uint8Array(res.message)
			let encodedString = String.fromCharCode.apply(null, unit8Arr)
			let str = decodeURIComponent(escape((encodedString))) //没有这一步中文会乱码

			let new_str = str.split(";")
			new_str.pop()
			let json_obj = ''
			new_str.map(item => {
				let item_array = item.split(":")
				if (item_array[0] && item_array[1]) {
					json_obj += '"' + item_array[0] + '":"' + item_array[1] + '",'
				}
			})
			let new_json_obj = json_obj.substring(0, json_obj.length - 1)
			new_json_obj = '{' + new_json_obj + '}'
			//console.log(JSON.parse(new_json_obj))
			
			await getPrinterInfoApi(checkedInfo.value.id)
			for (var i = 0; i < printerInfoList.value.length; i++) {
				let indexCode = JSON.parse(new_json_obj).DEVICENAME.indexOf(printerInfoList.value[i].brandCode)
				let indexWifi = JSON.parse(new_json_obj).DEVICENAME.indexOf(printerInfoList.value[i].wifiName)
				if(indexCode >= 0 || indexWifi >= 0) {
					console.log(printerInfoList.value[i].instructCode)
					let data =JSON.parse(new_json_obj)
					data['brandName'] = printerInfoList.value[i].brandName
					data['instructCode'] = printerInfoList.value[i].instructCode
					wifiList.value.push(data)
					break
				}
			}
			udpSocket.close()
		})

		udpSocket.onClose(() => {
			//console.log("关闭广播")
			lodingShow.value = false
		})
	}
	// 关闭WiFi搜索
	const stopWifi = () => {
		lodingShow.value = false
		isStopWifi.value = true
	}
	// 绑定WiFi打印机设备
	const connetWifi = (obj) => {
		modal.msgSuccess('打印机设备：' + obj.DEVICENAME + '连接成功')
		defaultWifi.value = obj
		//defaultWifi.value['brandName'] = checkedInfo.value.brandName
		//defaultWifi.value['instructCode'] = checkedInfo.value.instructCode
		customStorage.set("WifiInfo", defaultWifi.value)
    uni.$emit('printerChange', {
      msg: 'connectWifi',
    });
	}

	//获取打印机品牌信息
	const getPrinterInfoApi = async (parentId) => {
		await getByParentId(!parentId ? 0 : parentId).then(res => {
      console.log('getPrinterInfoApi', res)
			if (res.code === 0) {
				printerInfoList.value = res.data.map(item => {
					item['checked'] = false
					return item
				})
			}
		})
	}
	// 选中打印
	const handlePopupCardFn = (index) => {
		printerInfoList.value.map((obj, inx) => {
			if (inx === index) {
				obj.checked = !obj.checked
				if (obj.checked) {
					checkedInfo.value = obj
				} else {
					checkedInfo.value = undefined
				}
			} else {
				obj.checked = false
			}
		})
	}
	// 选中打印机品牌确定事件
	const handlePrinterClick = () => {
		const info = uni.getSystemInfoSync() // 同步获取设备信息
    console.log('设备信息', info)
    console.log('选中品牌', checkedInfo.value)
		if (!checkedInfo.value) {
			modal.msgError('请选择打印机品牌信息')
		} else {
			printerInfoShow.value = false
			// if (info.uniPlatform === 'mp-weixin') {
				if (tabsSelected.value === 'blue') {
					blueList.value = []
					findBlue()
				} else if (tabsSelected.value === 'wifi') {
					isStopWifi.value = false
					wifiList.value = []
					findWifi()
				}
			// }
		}
	}
	// 未找到打印机事件
	const handleFindError = () => {
		console.log("未找到打印机事件")
		uni.navigateTo({
			url: '/page_mine/uploadPrinter',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				//res.eventChannel.emit('params', obj)
			}
		})
	}

	onMounted(() => {
		initFn()
	})

	// 样式
	const tabsItemStyle = reactive({
		width: '160rpx',
		height: '100rpx'
	})
</script>

<style scoped lang="scss">
	.layout-container {
		height: calc(100vh - var(--window-top));

		.yj-body {
			height: calc(100% - 140rpx);
			overflow: auto;
			padding: 20rpx;
			background-color: #f1f1f1;

			.custom-tabs-menu {
				background-color: #fff;
				height: 100rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 0 -20rpx;
				margin-top: -20rpx;
			}

			.body-box {
				display: flex;
				flex-direction: column;
				flex-wrap: nowrap;
				gap: 20rpx;
				margin-top: 20rpx;

				.upload-box {
					background-color: #fff;
					border-radius: 10rpx;
					padding: 20rpx;

					.upload-box-title {
						display: flex;
						align-items: center;
						font-size: 15px;
					}

					.upload-box-title-icon {
						height: 15px;
						width: 6rpx;
						background-color: #d81e06;
						margin-right: 20rpx;
					}

					.upload-box-btn {
						margin: 10px 0;

						.yj-card {
							display: flex;
							flex-direction: column;
							background-color: #fff;
							gap: 20rpx;
							padding: 20rpx;
							margin-bottom: 20rpx;
							border-top: 1px solid #f1f1f1;
							border-bottom: 1px solid #f1f1f1;

							.box-dis {
								display: flex;
							}
						}

						.yj-card-2 {
							display: flex;
							flex-direction: column;
							background-color: #fff;
							gap: 20rpx;
							padding: 20rpx;

							border-top: 1px solid #f1f1f1;
							// border-bottom: 1px solid #f1f1f1;

							.box-dis {
								display: flex;
							}
						}
					}
				}
			}


		}

		.yj-footer {
			height: 140rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #fff;
			display: flex;
			padding: 20rpx;

			.butn {
				display: flex;
				align-items: center;
				width: 100%;
				gap: 20rpx;
			}
		}

		.printer-box {
			padding: 20rpx;
			width: 100vw;

			.printer-box-title {
				margin-bottom: 20rpx;
			}

			.custom-card-body-item-tag {
				margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 15px;
					line-height: 15px;
				}

				:deep(.u-tag--mini) {
					height: 43px;
					line-height: 43px;
					padding: 0 15px;
					width: 100px;
				}

				:deep(.u-tag--primary) {
					background-color: #d81e06;
				}
			}
		}
	}
</style>