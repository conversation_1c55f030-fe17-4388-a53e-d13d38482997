
//从服务端获取到当前发布app版本信息,

import {reactive} from "vue"
import config from "@/config"
const baseUrl = config.APP_BASE_UPDATE_URL + "/"
console.log(config)
//标识是否又下载任务进行
//仅在app环境下运行此代码,检查版本更新并安装
//拿到apk最新版的下载地址
// #ifdef APP-PLUS
export function getVersion(enterBoot = false){
	plus.runtime.getProperty(plus.runtime.appid,function(widgetInfo){
		uni.request({
			url: baseUrl + 'version.json',
			success:(result)=>{
				if(result.statusCode === 200){
					//检查是不是最新版本，如果是最新版本，则什么都不做，如果不是，则下载最新版本
					const res = result.data
					console.log(widgetInfo)
					//比较versionCode
					if(res.versionCode > parseInt(widgetInfo.versionCode)){
						//检测到有新版本，是否同意更新
						//说明有新版本,使用wgt热更新
						//如果大版本更新，则下载apk更新
						uni.showModal({
							title:"更新提示",
							content:"检测到新版本，是否更新?",
							success:(resData) => {
								if(resData.confirm){
									//选择是apk更新还是wgt更新
									//从鼓舞拿到必须要使用apk升级的版本
									// res.forceUpdate
									// console.log()
									if(checkVersionName(widgetInfo.version,res.version)){
										//大版本使用apk更新
										downloadFile(baseUrl + res.apkName,"apk")
									}else{
										
										//使用wgt更新
										downloadFile(baseUrl + res.wgtName,"wgt")
									}
								}
							}
						})
						
					}else{
						//进入时不弹窗，手动点击检查时弹窗
						if(!enterBoot){
							uni.showToast({
								title:"当前版本为最新版本",
								mask:false,
								icon:"none",
								duration:2000
							})
						}
						//说明是最新版本
						// console.log("最新版本")
					}
					
				}	
			}
		})
	})
}
// #endif
// #ifndef APP-PLUS
export function getVersion(){
	// mockProgress()
	// console.log("0.0.0")
	// uni.showToast({
	// 	title:"当前版本为最新版本",
	// 	mask:false,
	// 	icon:"none",
	// 	duration:0
	// })
}
// #endif

//检查名字中的版本号，如果是大版本更新，则返回true,否则返回false
function checkVersionName(localVersion,remoteVersion){
	console.log(localVersion)
	console.log(remoteVersion)
	//获取当前版本号
	const localVersions = localVersion.split(".")
	const remoteVersions = remoteVersion.split(".")
	//如果第一个版本号加1，说明是大更新，需要apk安装
	if(parseInt(remoteVersions[0]) > parseInt(localVersions[0])){
		return true
	}else{
		return false
	}
	
}

function mockProgress(){

	// if(downloadProgressRef.value){
		// downloadProgressRef.value?.open()
	// }
	downloadProgress.end = false
    let progress = 0;
    const startTime = Date.now();
    const totalTime = 10000; // 模拟总下载时间10秒
    
    const timer = setInterval(()=>{
        const elapsedTime = Date.now() - startTime;
        progress = Math.min((elapsedTime / totalTime) * 100, 100);
        const remainingTime = Math.max((totalTime - elapsedTime) / 1000, 0);
        
        if(progress >= 100){
			// downloadProgress.downloadSpeed = "0M/s"
			// downloadProgress.remainingTime =  "0秒"
			// downloadProgress.progress = progress.toFixed(0)
            clearInterval(timer)
			downloadProgress.end = true
			progressDialog.closeDialog()
            // uni.hideToast();
			// downloadProgressRef.value?.close()
           
        }else{
			// downloadProgress.end = true
			downloadProgress.downloadSpeed = 1200
			downloadProgress.remainingTime = remainingTime 
			downloadProgress.progress = progress.toFixed(0)
			progressDialog.openDialog(downloadProgress)
			// downloadProgressRef.value?.update(downloadProgress)
            // 使用toast显示进度，设置duration为0表示不自动关闭

			//# ifdef APP-PLUS
			//使用andriod原生弹出框显示进度
			
			// uni.showToast({
			// 	title: `下载中 ${downloadProgress.progress}%\n速度: ${formatBytes(downloadProgress.downloadSpeed)}/s\n剩余时间: ${formatTime(downloadProgress.remainingTime)}`,
			// 	icon: 'none',
			// 	duration: 5000
			// 	// position: 'center'
        	// });
			//# endif
        }
    },500) // 每500ms更新一次进度
}
export const downloadProgress = reactive({
	progress:0,
	remainingTime:0,
	downloadSpeed:0,
	end:false,
	fileTempPath:"",
})

export function downloadFile(downloadUrl,type){
	// mockProgress()
	// return 
    let startTime = Date.now();
    let lastBytesWritten = 0;
	//打开下载进度条
	// downloadProgressRef.value?.open()
	downloadProgress.end = false
	progressDialog.openDialog(downloadProgress)	
    
    const downloadTask = uni.downloadFile({
        url: downloadUrl,
        success:(downloadRes)=>{
			downloadProgress.end = true
            if(downloadRes.statusCode == 200){
                // downloadProgressRef.value?.close()
				progressDialog.closeDialog()
                uni.showToast({
                    title:"下载成功",
                    mask:false,
                    icon:"none"
                })
				//将下载完成后的文件路径保存在下载进度中
				downloadProgress.fileTempPath = downloadRes.tempFilePath
                //下载成功，安装
                update(downloadRes.tempFilePath,type)
				
            }else{
                // downloadProgressRef.value?.close()
				progressDialog.closeDialog()
                uni.showToast({
                    title:"下载失败",
                    mask:false,
                    icon:"none"
                })
            }    
			
        },
        fail:(error)=>{
            console.log(error)
			downloadProgress.end = true
            // downloadProgressRef.value?.close()
			progressDialog.closeDialog()
            uni.showToast({
                title:"下载失败",
                mask:false,
                icon:"none"
            })
        }
    })
    
    downloadTask.onProgressUpdate = (res)=>{
        const currentTime = Date.now();
        const elapsedTime = (currentTime - startTime) / 1000;
        const downloadedBytes = res.totalBytesWritten - lastBytesWritten;
        const downloadSpeed = downloadedBytes / elapsedTime;
        const remainingBytes = res.totalBytesExpectedToWrite - res.totalBytesWritten;
        const remainingTime = remainingBytes / downloadSpeed;

        downloadProgress.downloadSpeed = formatBytes(downloadSpeed) + "/s"
        downloadProgress.remainingTime = formatTime(remainingTime)
        downloadProgress.progress = res.progress
		progressDialog.openDialog(downloadProgress)
		//更新下载进度
		// downloadProgressRef.value?.update(downloadProgress)
        // 使用toast显示进度，设置duration为0表示不自动关闭
        // uni.showToast({
        //     title: `下载中 ${res.progress}%\n速度: ${formatBytes(downloadSpeed)}/s\n剩余时间: ${formatTime(remainingTime)}`,
        //     icon: 'none',
        //     duration: 0,
        //     position: 'bottom'
        // });
        
        lastBytesWritten = res.totalBytesWritten;
        startTime = currentTime;
    }
}

// 格式化字节大小
export function formatBytes(bytes) {
    if (bytes < 1024) return bytes + " B";
    if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + " MB";
    return (bytes / 1073741824).toFixed(1) + " GB";
}

// 格式化时间
export function formatTime(seconds) {
    if (seconds < 60) return Math.floor(seconds) + "秒";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}分${remainingSeconds}秒`;
}

function update(filePath,type){
	if(type === "apk"){
		apkInstall(filePath)
	}else if(type === "wgt"){
		wgtInstall(filePath)
	}else{
		
	}
}
//apk安装
function apkInstall(apkFilePath){
	// uni.showLoading({
	// 	title:"安装中"
	// })
	plus.runtime.install(plus.io.convertLocalFileSystemURL(apkFilePath),{},()=>{
		// uni.hideLoading()

		uni.showToast({
			title:"更新成功",
			mask:false,
			icon:"none ",
		})	
		setTimeout(()=>{
			plus.runtime.restart();
		},2000)
	},(error)=>{
		// uni.hideLoading()
		uni.showToast({
			title:"安装失败",
			mask:false,
			icon:"none"
		})
	})
}
//wgt安装
function wgtInstall(wgtFilePath){
	uni.showLoading({
		title:"更新中"
	})
	plus.runtime.install(wgtFilePath,{force:true},()=>{
		uni.hideLoading()
		//更新完成后重启应用
		uni.showToast({
			title:"更新成功",
			mask:false,
			icon:"none ",
		})
		setTimeout(()=>{
			plus.runtime.restart();
		},2000)
		// plus.runtime.restart();
		// plus.nativeUI.toast("更新成功");
	},(error)=>{
		uni.hideLoading()
		uni.showToast({
			title:"安装失败",
			mask:false,
			icon:"none"
		})
	})
}
//定义一个进度条弹窗对象，如果为app，则使用app原生弹窗，如果为h5，则使用uni-ui的弹窗
const progressDialog = {
	openDialog,
	closeDialog
}
// #ifdef APP-PLUS
//使用andriod原生弹出框显示进度

//打开原生弹窗
function openDialog(downloadProgress){
	plus.nativeUI.showWaiting(`下载中 ${downloadProgress.progress}%\n速度: ${formatBytes(downloadProgress.downloadSpeed)}/s\n剩余时间: ${formatTime(downloadProgress.remainingTime)}`);
}
//关闭原生弹窗
function closeDialog(){
	plus.nativeUI.closeWaiting();
}

// #endif
// #ifndef APP-PLUS
//使用uni-ui的弹窗
//打开弹窗
function openDialog(downloadProgress){
	uni.showToast({
		title: `下载中 ${downloadProgress.progress}%\n速度: ${formatBytes(downloadProgress.downloadSpeed)}/s\n剩余时间: ${formatTime(downloadProgress.remainingTime)}`,
		icon: 'none',		
	})
}
//关闭弹窗
function closeDialog(){
	uni.hideToast();	
}
// #endif