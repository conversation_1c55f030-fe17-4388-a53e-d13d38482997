import upload from '@/utils/upload'

// 上传文件
export const uploadFileApi = async (tempFilePaths) => {
	// 1.1 生成文件名称
	//const filename = await generateFileName(file)
	// 1.2 获取文件预签名地址
	//const presignedInfo = await getFilePresignedUrl(filename)
	/* if (presignedInfo.code === 0) {
		return axios.put(presignedInfo.data.uploadUrl, file, {
			headers: {
				'Content-Type': file.type,
			}
		}).then(() => {
			// 1.4. 记录文件信息到后端（异步）
			//createFile(presignedInfo, fileName, options.file)
			// 通知成功，数据格式保持与后端上传的返回结果一致
			return { data: presignedInfo.data.url }
		})
	} */
	return upload({
		url: '/infra/file/minio-upload', // MinIO服务器的URL，包括端口和bucket名称
		filePath: tempFilePaths, // 上传的文件路径
		name: 'file'// 这是MinIO期待的字段名
	})
}

// 删除文件
export const delUploadFileApi = async (filePath) => {
	return upload({
		url: '/infra/file/minio-delete', // MinIO服务器的URL，包括端口和bucket名称
		filePath: filePath, // 上传的文件路径
		name: 'file',// 这是MinIO期待的字段名
		formData: {
			path: filePath
		}
	})
}

// 获取文件预签名地址
export const getFilePresignedUrl = (path : string) => {
	return request({
		url: '/infra/file/presigned-url',
		method: 'GET',
		params: { path }
	})
}

// 生成文件名称
export async function generateFileName(file : any) {
	console.log("file", file)
	let data
	const fs = uni.getFileSystemManager();
	await fs.readFile({
	  filePath: file.path,
	  success: function(res) {
	    // 成功读取文件
	    data = res.data;
	    // 此时arrayBuffer包含了文件的内容，可以进行后续操作
	  },
	  fail: function(err) {
	    console.error('读取文件失败：', err);
	  }
	});
	console.log("data", data)
	// 读取文件内容
	// const data = await file.arrayBuffer()
	const wordArray = CryptoJS.lib.WordArray.create(data)
	// 计算SHA256
	const sha256 = CryptoJS.SHA256(wordArray).toString()
	// 拼接后缀
	const ext = file.name.substring(file.name.lastIndexOf('.'))
	return `${sha256}${ext}`
}