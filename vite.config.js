import {
	defineConfig, loadEnv
} from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

// const mode = process.argv[2] === '--mode' ? process.argv[3] : 'default'; // 假设第二个参数是模式值
//从参数中读取当前模式
function getMode(){
	// console.log(process.argv);
	if(process.argv){
		for (let i = 0; i < process.argv.length; i++){
			const arg = process.argv[i]
			if(arg === "--mode"){
				// console.log(arg)
				return process.argv[i+1] ? process.argv[i+1] : "dev"
			}
		}
	}
	return "loc"
}
//从进程的参数中获取到运行模式
const mode = getMode()
// console.log(mode)
const env = loadEnv(mode,".")
export default defineConfig(() => {
	return {
		// 设置scss的api类型为modern-compiler
		css: {
			preprocessorOptions: {
			  scss: {
				api: 'modern-compiler'
			  }
			}
		},
		   renderer: {
		    css: {
		      preprocessorOptions: {
		        scss: {
		          silenceDeprecations: ['legacy-js-api']
		        }
		      }
		    }
			},
		base: './',
		build: {
			minify: true,
			outDir: 'dist',
		},
		server: {
			port: '80'
		},
		plugins: [
			uni()
		],
		define:{
			'process.env': env
		},
		exclude: [
			/\/README\.md$/,
		]
	}
})
