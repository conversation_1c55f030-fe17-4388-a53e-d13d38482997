<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">

					<up-form-item label="姓名" prop="principalName" borderBottom="true">
            <up-input v-model="formData.principalName" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item v-if="!(userInfo.logisticsNetworkInfo.principalAuditStatus==='1' && formType==='edit')" label="身份证号" prop="principalIdentityNum" >
            <up-input v-model="formData.principalIdentityNum" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item v-if="userInfo.logisticsNetworkInfo.principalAuditStatus==='1' && formType==='edit'" label="身份证号" prop="principalIdentityNum" borderBottom="true">
            <up-input v-model="formData.principalIdentityNum" border="none" inputAlign="right"></up-input>
					</up-form-item>
<!--					<up-form-item label="身份证类型" prop="principalIdentityType" borderBottom="true">
            <up-input v-model="formData.principalIdentityTypeLabel" border="none" inputAlign="right"
                      readonly></up-input>
            <template #right>
              <up-icon v-if="formType==='edit'" name="arrow-right" @click="handleShowTypeSelect" size="20"></up-icon>
            </template>
					</up-form-item>-->
					<up-form-item v-if="userInfo.logisticsNetworkInfo.principalAuditStatus==='1' && formType==='edit'" label="" prop="identityBack">
            <view style="text-align: center; color: #606266;">
              <view>温馨提示</view>
              <view>个人认证审核期间无法使用系统功能，请谨慎操作！</view>
            </view>
					</up-form-item>
				</up-form>
			</view>

      <view class="upload-box">
        <view class="upload-box-title">
          <view class="upload-box-title-icon"></view>
          <view>身份证上传</view>
        </view>
        <view class="upload-box-btn">
          <up-upload :fileList="identityData.identityPositive" width="300" height="150" :maxCount="1"
                     uploadText="身份证正面" @afterRead="afterReadIdentityPositive" @delete="deleteIdentityPositive"
                     :deletable="formType==='edit'">
          </up-upload>
        </view>
        <view class="upload-box-btn">
          <up-upload :fileList="identityData.identityBack" width="300" height="150" :maxCount="1"
                     uploadText="身份证反面" @afterRead="afterReadIdentityBack" @delete="deleteIdentityBack"
                     :deletable="formType==='edit'">
          </up-upload>
        </view>
      </view>
		</view>


		<view class="custom-footer" v-if="formType==='edit'" >
      <view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>
		<view class="custom-footer" v-if="formType==='audit' && formData.approvalStatus==='0'" >
			<view style="width: 85%;">
        <up-row>
          <up-col span="5.5">
            <up-button type="info" shape="circle" text="不通过" @click="handleAudit('2')"></up-button>
          </up-col>
          <up-col span="1"></up-col>
          <up-col span="5.5">
            <up-button type="primary" shape="circle" text="通过" @click="handleAudit('1')"></up-button>

          </up-col>
        </up-row>
			</view>
		</view>
	</view>

  <up-action-sheet :show="showTypeSelect" :actions="radiosTypeList" title="请选择身份证类型" @close="showTypeSelect=false"
                   @select="handleTypeSelect">
  </up-action-sheet>
</template>

<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import {getPrincipalApprovalInfo, savePrincipalApproval, auditPrincipalApproval} from "@/page_mine/api/sub_mine";
import modal from '@/plugins/modal'
import {getDictDataInfoAll} from "@/api/wljh/task";
import {uploadFileApi} from "@/page_mine/api/minioUpload";
import customStorage from "@/utils/customStorage";

const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    logisticsNetworkId: undefined,
    networkAccountId: undefined,
    principalName: undefined,
    principalIdentityNum: undefined,
    principalIdentityType: undefined,
    principalIdentityTypeLabel: undefined,
    identityPositive: undefined,
    identityBack: undefined,
  })

const userInfo = ref({
  levelInfo: {}
})


  // 类型列表
  const radiosTypeList = ref([])
  const showTypeSelect = ref(false) // 显示类型选择
  // 显示类型选择
  const handleShowTypeSelect = () => {
    if(formType.value==='edit') {
      showTypeSelect.value=true
    }
  }
  // 选中类型
  const handleTypeSelect = (item) => {
    formData.value.principalIdentityType = item.value
    formData.value.principalIdentityTypeLabel = item.name
    showTypeSelect.value = false
  }

  // 身份证照片
  const identityData = ref({
    identityPositive: [],
    identityBack: [],
  })
  // 读取后事件
  const afterReadIdentityPositive = (res) => {
    identityData.value.identityPositive.push(res.file)
    uploadFileApi(res.file.url).then(res => {
      if (res.code === 0) {
        formData.value.identityPositive = res.data.filePath
      }
    })
  }
  const afterReadIdentityBack = (res) => {
    identityData.value.identityBack.push(res.file)
    uploadFileApi(res.file.url).then(res => {
      if (res.code === 0) {
        formData.value.identityBack = res.data.filePath
      }
    })
  }
  // 删除事件
  const deleteIdentityPositive = (res) => {
    identityData.value.identityPositive.splice(res.index, 1)
    formData.value.identityPositive = ""
  }
  const deleteIdentityBack = (res) => {
    identityData.value.identityBack.splice(res.index, 1)
    formData.value.identityBack = ""
  }

	// 提交信息
	const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }
    let data = {...formData.value}
		await savePrincipalApproval(data).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFnPrincipal', true);
        uni.$emit('childFnPrincipal', {msg: true,})
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}
	// 提交审核
	const handleAudit = async (result) => {

		await auditPrincipalApproval({
      id: formData.value.id,
      result: result,
    }).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFn', true);
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}

  // 校验表单
  const validateForm = () => {
    if (!formData.value.principalName) {
      modal.msgError('请输入姓名')
      return false
    }
    if (!formData.value.principalIdentityNum) {
      modal.msgError('请输入身份证号')
      return false
    }
    // 校验身份证号
    const regex = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/ //正则表达式
    if (!regex.test(formData.value.principalIdentityNum)) {
      modal.msgError('请输入正确的身份证号')
      return false
    }
    if (!formData.value.principalIdentityType) {
      modal.msgError('请选择身份证类型')
      return false
    }
    if (!formData.value.identityPositive) {
      modal.msgError('请上传身份证正面图片')
      return false
    }
    if (!formData.value.identityBack) {
      modal.msgError('请上传身份证反面图片')
      return false
    }
    return true
  }

	// 初始化页面
	const initPage = async (obj) => {
    // console.log(obj)
    // 动态改变顶部标题
    await uni.setNavigationBarTitle({
      title: obj.type === 'edit' ? '个人认证' : '审核认证'
    });
    formType.value = obj.type
		if (obj.data) {
      if (formType.value === 'edit') {
        // 编辑
        await getPrincipalApprovalInfo({...obj.data}).then(res => {
          if (res.code === 0) {
            formData.value = {...res.data}
            formData.value.id = undefined
          }
        })
      } else {
        // 审核
        formData.value = {...obj.data}
      }
      if (!formData.value.principalIdentityType) {
        // 默认中国大陆居民身份证
        formData.value.principalIdentityType = 'CRED_PSN_CH_IDCARD'
      }
      formData.value.principalIdentityTypeLabel = radiosTypeList.value.find(item => item.value === formData.value.principalIdentityType)?.name
      if(formData.value.identityPositive){
        identityData.value.identityPositive.push({url: formData.value.identityPositive})
      }
      if(formData.value.identityBack){
        identityData.value.identityBack.push({url: formData.value.identityBack})
      }
    }
	}

  onMounted(() => {
    getDictDataInfoAll({type: 'IDENTITY_TYPE'}).then(res => {
      if (res.code === 0) {
        radiosTypeList.value = res.data.map(item => {
          return {
            name: item.label,
            value: item.value,
          }
        })
        if (formData.value.principalIdentityType) {
          formData.value.principalIdentityTypeLabel = radiosTypeList.value.find(item => item.value === formData.value.principalIdentityType)?.name
        }
      }
    })
    userInfo.value = customStorage.get("UserInfo")
		eventChannel.on('params', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}
  .upload-box {
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .upload-box-title {
      display: flex;
      align-items: center;
      font-size: 15px;
    }

    .upload-box-title-icon {
      height: 15px;
      width: 6rpx;
      background-color: #d81e06;
      margin-right: 20rpx;
    }

    .upload-box-btn {
      margin: 10px 0;

      :deep(.u-upload) {
        align-items: center;
      }
    }
  }
</style>