<template>
  <view class="layout-sub-form">
    <view class="custom-body">
      <view class="custom-body-form">
        <up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">

          <up-form-item label="机构类型" prop="organizationClass" borderBottom="true">
<!--            <up-input v-model="formData.organizationClassLabel" border="none" inputAlign="right"
                      readonly @focus="handleShowClassSelect"></up-input>-->
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowClassSelect" >
                <text>{{ formData.organizationClassLabel }}</text>
                <up-icon v-if="formType==='edit'" name="arrow-right" size="20"></up-icon>
              </view>
            </template>
          </up-form-item>
          <up-form-item label="机构代码" prop="organizationCode" >
            <up-input v-model="formData.organizationCode" border="none" inputAlign="right"></up-input>
          </up-form-item>
<!--          <up-form-item label="机构代码类型" prop="organizationType" borderBottom="true">
            <up-input v-model="formData.organizationTypeLabel" border="none" inputAlign="right"
                      readonly></up-input>
            <template #right>
              <up-icon v-if="formType==='edit'" name="arrow-right" @click="handleShowTypeSelect" size="20"></up-icon>
            </template>
          </up-form-item>-->
        </up-form>
      </view>

      <view class="upload-box">
        <view class="upload-box-title">
          <view class="upload-box-title-icon"></view>
          <view>营业执照上传</view>
        </view>
        <view class="upload-box-btn">
          <up-upload :fileList="identityData.orgPositive" width="300" height="150" :maxCount="1"
                     uploadText="营业执照" @afterRead="afterReadOrgPositive" @delete="deleteOrgPositive"
                     :deletable="formType==='edit'">
          </up-upload>
        </view>
      </view>
    </view>


    <view class="custom-footer" v-if="formType==='edit'" >
      <view style="width: 100%;padding: 20rpx;">
        <up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
      </view>
    </view>

  </view>

  <up-action-sheet :show="showClassSelect" :actions="radiosClassList" title="请选择机构类型" @close="showClassSelect=false"
                   @select="handleClassSelect">
  </up-action-sheet>
  <up-action-sheet :show="showTypeSelect" :actions="radiosTypeList" title="请选择机构代码类型" @close="showTypeSelect=false"
                   @select="handleTypeSelect">
  </up-action-sheet>
</template>

<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import {getNetworkAuthInfo, saveNetworkAuth} from "@/page_mine/api/sub_mine";
import modal from '@/plugins/modal'
import {getDictDataInfoAll} from "@/api/wljh/task";
import {uploadFileApi} from "@/page_mine/api/minioUpload";
import customStorage from "@/utils/customStorage";

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const formType = ref() // 表单类型
const formData = ref({
  logisticsNetworkId: undefined,
  organizationClass: undefined,
  organizationClassLabel: undefined,
  organizationCode: undefined,
  organizationType: undefined,
  organizationTypeLabel: undefined,
  orgPositive: undefined,
})


// 类型列表
const radiosClassList = ref([
    {name: '企业', value: '1'},
    {name: '个体户', value: '2'},
])
const showClassSelect = ref(false) // 显示类型选择
// 显示类型选择
const handleShowClassSelect = () => {
  if(formType.value==='edit') {
    showClassSelect.value=true
  }
}
// 选中类型
const handleClassSelect = (item) => {
  formData.value.organizationClass = item.value
  formData.value.organizationClassLabel = item.name
  showClassSelect.value = false
}

// 类型列表
const radiosTypeList = ref([])
const showTypeSelect = ref(false) // 显示类型选择
// 显示类型选择
const handleShowTypeSelect = () => {
  if(formType.value==='edit') {
    showTypeSelect.value=true
  }
}
// 选中类型
const handleTypeSelect = (item) => {
  formData.value.organizationType = item.value
  formData.value.organizationTypeLabel = item.name
  showTypeSelect.value = false
}

// 身份证照片
const identityData = ref({
  orgPositive: [],
})
// 读取后事件
const afterReadOrgPositive = (res) => {
  identityData.value.orgPositive.push(res.file)
  uploadFileApi(res.file.url).then(res => {
    if (res.code === 0) {
      formData.value.orgPositive = res.data.filePath
    }
  })
}
// 删除事件
const deleteOrgPositive = (res) => {
  identityData.value.orgPositive.splice(res.index, 1)
  formData.value.orgPositive = ""
}

// 提交信息
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  let data = {...formData.value}
  await saveNetworkAuth(data).then(res => {
    if (res.code === 0) {
      modal.msgSuccess('操作成功')
      // 父页面发送数据
      eventChannel.emit('updateAuthApprovalStatus', true);
      uni.navigateBack()
    }
  }).catch(error => {

  })
}

// 校验表单
const validateForm = () => {
  if (!formData.value.organizationClass) {
    modal.msgError('请选择机构类型')
    return false
  }
  if (!formData.value.organizationCode) {
    modal.msgError('请输入机构代码')
    return false
  }
  if (!formData.value.organizationType) {
    modal.msgError('请选择机构代码类型')
    return false
  }
  if (!formData.value.orgPositive) {
    modal.msgError('请上传营业执照图片')
    return false
  }
  return true
}

// 初始化页面
const initPage = async (obj) => {
  // console.log(obj)
  // 动态改变顶部标题
  await uni.setNavigationBarTitle({
    title: obj.type === 'edit' ? '网点认证' : '审核认证'
  });
  formType.value = obj.type
  if (obj.data) {
    if (formType.value === 'edit') {
      // 编辑
      await getNetworkAuthInfo({...obj.data}).then(res => {
        if (res.code === 0) {
          formData.value = {...res.data}
          formData.value.id = undefined
        }
      })
    } else {
      // 审核
      formData.value = {...obj.data}
    }
    formData.value.organizationClassLabel = radiosClassList.value.find(item => item.value === formData.value.organizationClass)?.name

    if (!formData.value.organizationType) {
      // 默认 统一社会信用代码
      formData.value.organizationType = 'CRED_ORG_USCC'
    }
    formData.value.organizationTypeLabel = radiosTypeList.value.find(item => item.value === formData.value.organizationType)?.name
    if(formData.value.orgPositive){
      identityData.value.orgPositive.push({url: formData.value.orgPositive})
    }
  }
}

onMounted(() => {
  getDictDataInfoAll({type: 'ORG_TYPE'}).then(res => {
    if (res.code === 0) {
      radiosTypeList.value = res.data.map(item => {
        return {
          name: item.label,
          value: item.value,
        }
      })
      if (formData.value.organizationType) {
        formData.value.organizationTypeLabel = radiosTypeList.value.find(item => item.value === formData.value.organizationType)?.name
      }
    }
  })
  eventChannel.on('params', function(data) {
    initPage(data)
  })
})
</script>

<style scoped lang="scss">
.layout-sub-form {
  background-color: #f1f1f1;
  height: calc(100vh - var(--window-top));
  overflow: hidden;
  padding: 0 20rpx;

  .custom-body {
    height: calc(100% - 100rpx);
    overflow: auto;

    .custom-body-form {
      background-color: #fff;
      margin: 20rpx 0;
      padding: 0 20rpx;
      border-radius: 10rpx;
      //padding-bottom: 30rpx;

      :deep(.u-form-item__body__left__content__label){
        color: #606266;
      }
      :deep(.u-form-item__body__right__content__slot) {
        display: block;
      }
    }
  }

  .custom-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;

    :deep(.u-button--circle){
      border-radius: 20px;
    }

    :deep(.u-button--primary){
      background-color: #d81e06;
      border-color: #d81e06;
    }
  }
}
.upload-box {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .upload-box-title {
    display: flex;
    align-items: center;
    font-size: 15px;
  }

  .upload-box-title-icon {
    height: 15px;
    width: 6rpx;
    background-color: #d81e06;
    margin-right: 20rpx;
  }

  .upload-box-btn {
    margin: 10px 0;

    :deep(.u-upload) {
      align-items: center;
    }
  }
}
</style>