export const regExp = (value : string) => {
	let result = undefined
	if (value) {
		value = value.trim()
		//console.log(value)
		/*
		边框离纸张顶端10px(px是绝对值长度，等于1/96英寸,下同)距左边55px、宽360px、高220px、
		框为实线(0-实线 1-破折线 2-点线 3-点划线 4-双点划线)、线宽为1px
		第一个参数：距顶端10px
		第二个参数：距左边55px
		第三个参数：宽360px
		第四个参数：高220px
		第五个参数：框线类型：0-实线 1-破折线 2-点线 3-点划线 4-双点划线
		第六个参数：线宽为1px
		ADD_PRINT_RECT(10,55,360,220,0,1);
		图形
		*/
		const ADD_PRINT_RECT_PATTERN_TEST = /ADD_PRINT_RECT/;
		const ADD_PRINT_RECT_PATTERN_MATCH = /\d+/g;
		/*
		ADD_PRINT_TEXT(intTop,intLeft,intWidth,intHeight,strContent);
		增加纯文本项
		*/
		const ADD_PRINT_TEXT_PATTERN_TEST = /ADD_PRINT_TEXT/
		const ADD_PRINT_TEXT_PATTERN_MATCH = /\((.*?)\)/

		const SET_PRINT_STYLEA_PATTERN_TEST = /SET_PRINT_STYLEA/
		const SET_PRINT_STYLEA_PATTERN_MATCH = /\((.*?)\)/

		/*
		intShapeType：该图形的类型，数字型参数。0--仰角直线 1--俯角直线 2--矩形框线 3--椭圆线 4--实心矩形 5--实心椭圆
		Top：
		该图形在纸张内的上边距，整数或字符型，整数时缺省长度单位为px。
		字符型时可包含单位名：in(英寸)、cm(厘米) 、mm(毫米) 、pt(磅)、px(1/96英寸) 、%(百分比)，如“10mm”表示10毫米。
		当上边距超过纸张高度时，打印项被输出在下一页(或更下页)。
		Left：
		该图形在纸张内的左边距，整数或字符型，整数时缺省长度单位为px。
		字符型时可包含单位名：in(英寸)、cm(厘米) 、mm(毫米) 、pt(磅)、px(1/96英寸) 、%(百分比)，如“10mm”表示10毫米。
		Width：
		该图形(直线的外缘矩形或椭圆的外缘矩形)的宽度，整数或字符型，整数时缺省长度单位为px。
		字符型时可包含单位名：in(英寸)、cm(厘米) 、mm(毫米) 、pt(磅)、px(1/96英寸) 、%(百分比)，如“10mm”表示10毫米。
		Height：
		该图形(直线的外缘矩形或椭圆的外缘矩形)的高度，整数或字符型，整数时缺省长度单位为px。
		字符型时可包含单位名：in(英寸)、cm(厘米) 、mm(毫米) 、pt(磅)、px(1/96英寸) 、%(百分比)，如“10mm”表示10毫米。
		intLineStyle：
		线条类型，数字型，0--实线 1--破折线 2--点线 3--点划线 4--双点划线,缺省线条是实线。
		intLineWidth：
		线条宽，整数型，单位是（打印）像素，缺省值是1，非实线的线条宽也是0。
		varColor：
		图形的颜色，整数或字符型，整数时是颜色的十进制RGB值；字符时是超文本颜色值，可以是“#”加三色16进制值组合，也可以是英文颜色名；
		ADD_PRINT_SHAPE(intShapeType, Top, Left,Width,Height,intLineStyle,intLineWidth, varColor);
		*/
		const ADD_PRINT_SHAPE_PATTERN_TEST = /ADD_PRINT_SHAPE/
		const ADD_PRINT_SHAPE_PATTERN_MATCH = /\((.*?)\)/


		if (ADD_PRINT_RECT_PATTERN_TEST.test(value) && value.match(ADD_PRINT_RECT_PATTERN_MATCH)) {
			let match = value.match(ADD_PRINT_RECT_PATTERN_MATCH)
			result = {
				key: "rect",
				data: match
			}
		} else if (ADD_PRINT_TEXT_PATTERN_TEST.test(value) && value.match(ADD_PRINT_TEXT_PATTERN_MATCH)) {
			let match = value.match(ADD_PRINT_TEXT_PATTERN_MATCH)
			result = {
				key: "text",
				data: match ? match[1].split(",") : undefined
			}
		} else if (SET_PRINT_STYLEA_PATTERN_TEST.test(value) && value.match(SET_PRINT_STYLEA_PATTERN_MATCH)) {
			let match = value.match(SET_PRINT_STYLEA_PATTERN_MATCH)
			result = {
				key: "stylea",
				data: match ? match[1].split(",") : undefined
			}
		} else if (ADD_PRINT_SHAPE_PATTERN_TEST.test(value) && value.match(ADD_PRINT_SHAPE_PATTERN_MATCH)) {
			let match = value.match(ADD_PRINT_SHAPE_PATTERN_MATCH)
			result = {
				key: "shape",
				data: match ? match[1].split(",") : undefined
			}
		}
	}
	return result
}

export function wrapText(context, text, x, y, maxWidth, lineHeight) {
	    var words = text.split(' ');
	    var line = '';
	    
	    for(var n = 0; n < words.length; n++) {
	        var testLine = line + words[n] + ' ';
	        var metrics = context.measureText(testLine);
	        var testWidth = metrics.width;
	        if (testWidth > maxWidth && n > 0) {
	            context.fillText(line, x, y);
	            line = words[n] + ' ';
	            y += lineHeight;
	        } else {
	            line = testLine;
	        }
	    }
	    context.fillText(line, x, y);
	}