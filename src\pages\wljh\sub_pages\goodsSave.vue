<template>
	<view class="layout-sub-goodsSave">
		<view class="custom-body">
			<up-form labelPosition="left" ref="formRef" :model="goodsInfo" labelWidth="150rpx">
				<up-form-item label="货品名称" prop="goodsType" borderBottom="true">
					<up-input v-model="goodsInfo.goodsTypeLabel" border="none" inputAlign="right"></up-input>
					<template #right>
						<up-icon name="arrow-right" @click="handleGoodsTypeFn" size="20"></up-icon>
					</template>
				</up-form-item>
				<up-form-item label="总重量" prop="totalWeight" borderBottom="true">
					<!-- <up-number-box v-model="goodsInfo.totalWeight" min="0"></up-number-box> -->
					<up-input v-model="goodsInfo.totalWeight" inputAlign="right" placeholder="总重量" type="digit">
						<template #suffix>
							<span>Kg</span>
						</template>
					</up-input>
					<!-- <template #right><span>Kg</span></template> -->
				</up-form-item>
				<view class="custom-body-form-row">
					<up-form-item label="总体积" prop="totalVolume" borderBottom="true">
						<view class="custom-body-form-item">
							<view class="custom-body-form-item-line ">
								<up-input type="digit" v-model="goodsInfo.totalVolume" inputAlign="right"
									placeholder="总体积">
									<template #suffix>
										<span>m³</span>
									</template>
								</up-input>
							</view>
							<view class="custom-body-form-item-line custom-body-form-item-input">
								<up-input v-model="goodsInfo.goodsLong" inputAlign="right" placeholder="长" type="digit">
									<template #suffix>
										<span>cm</span>
									</template>
								</up-input>
								<up-input v-model="goodsInfo.goodsWidth" inputAlign="right" placeholder="宽"
									type="digit">
									<template #suffix>
										<span>cm</span>
									</template>
								</up-input>
								<up-input v-model="goodsInfo.goodsHeight" inputAlign="right" placeholder="高"
									type="digit">
									<template #suffix>
										<span>cm</span>
									</template>
								</up-input>
							</view>
						</view>
					</up-form-item>
				</view>
				<up-form-item label="总件数" prop="totalNum" borderBottom="true">
					<!-- <up-number-box v-model="goodsInfo.totalNum" min="0"></up-number-box> -->
					<up-input v-model="goodsInfo.totalNum" inputAlign="right" placeholder="总件数" type="number">
						<template #suffix>
							<span>件</span>
						</template>
					</up-input>
					<!-- <template #right><span>件</span></template> -->
				</up-form-item>
				<!-- <up-form-item  label="包装方式" borderBottom="true">
					
				</up-form-item>
				<up-scroll-list  :indicator="false">
						<view class="custom-card-body-item-tag" v-for="(item, index) in packMethodList"
							:key="index">
							<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'" :text="item.name"
								size="mini" :plain="!item.checked" :name="index"
								@click="handlePopupCardFn(item, index)"></up-tag>
						</view>
					</up-scroll-list> -->
				<!-- 包装方式 -->
				<view class="custom-card-body-item border-bottom-line custom_line">
					<view class="custom-card-body-item-lable">
						<text >包装方式</text>
					</view>

					<up-scroll-list :indicator="false">
						<view class="custom-card-body-item-tag" v-for="(item, index) in packMethodList" :key="index">
							<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'" :text="item.name"
								size="mini" :plain="!item.checked" :name="index"
								@click="handlePopupCardFn(item, index)">
							</up-tag>
						</view>
					</up-scroll-list>
				</view>

        <up-line color="#d6d7d9"></up-line>
			</up-form>


			<view class="custom-btn">
				<up-button type="primary" text="确定" @click="handleSubmit"></up-button>
			</view>

			<up-action-sheet :show="showSheet" :actions="actionsSheet" title="请选择货品名称" @close="handleActionSheetClose"
				@select="handleActionSheetSelect">
			</up-action-sheet>
		</view>
	</view>
</template>

<script setup>
import {
	reactive,
	getCurrentInstance,
	ref,
	onMounted
} from 'vue'
import {
	getDictDataInfoAll
} from '@/api/wljh/task'
import modal from '@/plugins/modal'

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const goodsInfo = ref({
	goodsType: undefined,
	goodsTypeLabel: undefined,
	totalWeight: undefined,
	totalVolume: undefined,
	totalNum: undefined,
	goodsLong: undefined,
	goodsWidth: undefined,
	goodsHeight: undefined,
})
const actionsSheet = ref([]) // 选择参数值
const showSheet = ref(false) // 选择参数框是否显示
const packMethodList = ref([{ // 包装方式
	name: '箱',
	value: '1',
	dictType: 'Pack',
	checked: false
},
{
	name: '纸',
	value: '2',
	dictType: 'Pack',
	checked: false
},
{
	name: '袋',
	value: '3',
	dictType: 'Pack',
	checked: false
},
{
	name: '桶',
	value: '4',
	dictType: 'Pack',
	checked: false
},
{
	name: '木',
	value: '5',
	dictType: 'Pack',
	checked: false
},
{
	name: '膜',
	value: '6',
	dictType: 'Pack',
	checked: false
},
{
	name: '皮',
	value: '7',
	dictType: 'Pack',
	checked: false
},
{
	name: '布',
	value: '8',
	dictType: 'Pack',
	checked: false
}

])


// 获取单个字典标签
const getDictLable = async (dictType, value) => {
	await getDictDataInfoAll({
		'type': dictType,
		'value': value
	}).then(res => {
		if (res.code === 0) {
			switch (dictType) {
				case 'GOODS_TYPE':
					goodsInfo.value.goodsTypeLabel = res.data[0].label
					break;
			}
		}
	})
}
// 获取字典所有值
const getDictDataInfo = async (dictType) => {
	await getDictDataInfoAll({
		'type': dictType
	}).then(res => {
		if (res.code === 0) {
			actionsSheet.value = res.data.map(item => {
				let param = {
					'name': item.label,
					'text': item.value,
					'dictType': dictType
				}
				return param
			})
		}
	})
}
// 物品类型点击事件
const handleGoodsTypeFn = () => {
	showSheet.value = true
	getDictDataInfo('GOODS_TYPE')
}
// 选择参数框关闭事件
const handleActionSheetClose = () => {
	showSheet.value = false
	actionsSheet.value = []
}
// 选择参数框选中事件
const handleActionSheetSelect = (item) => {
	switch (item.dictType) {
		case 'GOODS_TYPE':
			goodsInfo.value.goodsTypeLabel = item.name
			goodsInfo.value.goodsType = item.text
			break;
	}
}
const handlePopupCardFn = (item, index) => {
	item.checked = !item.checked
	packMethodList.value.map((obj, inx) => {
		obj.checked = index != inx ? false : item.checked
	})
	goodsInfo.value.packMethod = item.checked ? item.value : undefined
}
// 确定事件
const handleSubmit = () => {
	if (!goodsInfo.value.goodsTypeLabel) {
		setTimeout(() => {
			modal.msgError("请输入或者选择物品类型")
		}, 500)
		return false
	} else if (goodsInfo.value.totalWeight == undefined || goodsInfo.value.totalWeight == 0) {
		setTimeout(() => {
			modal.msgError("请输入总重量")
		}, 500)
		return false
	} else if (goodsInfo.value.totalVolume == undefined || goodsInfo.value.totalVolume == 0) {
		setTimeout(() => {
			modal.msgError("请输入总体积")
		}, 500)
		return false
	} else if (goodsInfo.value.totalNum == undefined || goodsInfo.value.totalNum == 0) {
		setTimeout(() => {
			modal.msgError("请输入总件数")
		}, 500)
		return false
	} else if (goodsInfo.value.packMethod == undefined) {
		setTimeout(() => {
			modal.msgError("请选择包装方式")
		}, 500)
		return false
	} else {
		// 父页面发送数据
		eventChannel.emit('goodsInfoChild', goodsInfo.value);
		uni.navigateBack()
	}
}

onMounted(() => {
	// 接收数据
	eventChannel.on('goodsInfoParent', function (data) {
		console.log(data)
		if (data.goodsType) {
			getDictLable('GOODS_TYPE', data.goodsType)
		}
		goodsInfo.value.goodsType = data.goodsType
		goodsInfo.value.goodsTypeLabel = data.goodsTypeLabel
		goodsInfo.value.totalWeight = data.totalWeight
		goodsInfo.value.totalVolume = data.totalVolume
		goodsInfo.value.totalNum = data.totalNum
		goodsInfo.value.goodsLong = data.goodsLong
		goodsInfo.value.goodsWidth = data.goodsWidth
		goodsInfo.value.goodsHeight = data.goodsHeight
	})
})
</script>

<style scoped lang="scss">
.layout-sub-goodsSave {
	background-color: #f1f1f1;
	height: 100vh;
	overflow: auto;
	padding: 0 20rpx;

	.custom-body {
		background-color: #fff;
		padding: 0 20rpx;
		padding-bottom: 20rpx;
		margin: 20rpx 0;
		border-radius: 5px;

		:deep(.u-form-item__body__right__content__slot) {
			padding-right: 50rpx;
		}

		:deep(.u-number-box) {
			padding: 0 60rpx;
		}

		:deep(.u-number-box__input) {
			width: auto !important;
		}

		:deep(.u-form-item__body__left__content__label) {
			color: #606266;
		}

		.custom-body-form-row {
			.custom-body-form-item {
				display: flex;
				width: 100%;
				flex-direction: column;
				gap: 20rpx;

				.total-volume-item-input {
					width: 89%;
					height: 60rpx;
					margin-left: auto;
				}

				.custom-body-form-item-line {
					display: flex;

				}

				.custom-body-form-item-input {
					display: flex;
					flex-wrap: nowrap;
					gap: 5px;
					// margin-left: 30px;
					height: 30px;
				}
			}
		}

		.custom-btn {
			margin-top: 20rpx;

			:deep(.u-button--square) {
				border-radius: 20px;
			}

			:deep(.u-button--primary) {
				background-color: #d81e06;
				border: #d81e06;
			}

			:deep(.u-button) {
				height: 45px;
			}
		}

		// :deep(.u-scroll-list) {
		// 	width: 100%;
		// }

		.custom_line {
			display: flex;
			height: 60px;
			align-items: center;
			.custom-card-body-item-lable {
				font-size: 16px;
				color: #606266;
				width: 150rpx;
				// margin-right: 6rpx;
			}

			.custom-card-body-item-tag {
				// padding-left: 120rpx;
				width: 50px;
				height: 100%;
				display: flex;
				align-items: center;
        margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 18px;
				}
			}
		}


	}


	:deep(.u-scroll-list) {
		width: calc(100% - 220rpx);
		padding-bottom: 0;
	}


  :deep(.u-tag--mini) {
    height: 43px;
    line-height: 43px;
    padding: 0 15px;
    min-width: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.u-tag--primary) {
    background-color: #d81e06;
  }
}
</style>