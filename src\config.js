/**
 * 应用全局配置
 * appType: 小程序类别(hongwei/dachi)(使用不同的隐私政策、服务协议)
 * baseUrl: api地址
 */
const config = {

  baseUrl: process.env.VITE_BASE_URL,
  APP_BASE_UPDATE_URL: process.env.VITE_APP_UPDATE_URL + process.env.VITE_PROJECT_NAME,
  // 应用信息
  appInfo: {
    // 应用名称
    name: "nodal-wx",
    // 应用版本
    version: "1.0.0",
    // 应用logo
    // logo: "/static/logo.png",
    logo: "https://dachisc.wang:9000/wljh/442d24f655b71efd4e76c8d4a7c3d70ed8eeb388ffb7dbb8146dfd2a4cf43a8c.jpg",
    // 官方网站
    site_url: "http://127.0.0.1:48080",
    // 政策协议
    agreements: [{
      title: "隐私政策",
      url: "http://127.0.0.1:48080/protocol.html"
    },
    {
      title: "用户服务协议",
      url: "http://127.0.0.1:48080/protocol.html"
    }
    ]
  }
}

export default config