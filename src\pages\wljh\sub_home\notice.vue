<template>
	<view class="layout-container">
		<!-- 中部 -->
		<view class="yj-body">
			<view class="body-box" v-for="item in noticeList" :key="item.id">
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>{{item.title}}</view>
					</view>
					<view class="upload-box-content">
						<up-parse :content="item.content"></up-parse>
					</view>
					<view class="upload-box-date">
						<up-text mode="date" :text="item.createTime" align="right" color="#606266"></up-text>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部 -->
		<view class="yj-footer">
			<view class="butn">
				<up-button :plain="true" text="返回" shape="circle"
					customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleBlack"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref
	} from 'vue'
	import {
		getNoticePage
	} from '@/api/wljh/home'
	
	const noticeList = ref([]) // 公告数据

	const content = ref('<p>公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容</p>')

	// 获取通知公告
	const getNoticePageApi = async ()=>{
		getNoticePage().then(res=>{
			console.log(res)
			noticeList.value = res.data.list
		})
	}
	
	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}
	
	onMounted(()=>{
		getNoticePageApi()
	})
</script>

<style scoped lang="scss">
	.layout-container {
		height: calc(100vh - var(--window-top));

		.yj-body {
			height: calc(100% - 140rpx);
			overflow: auto;
			padding: 20rpx;
			background-color: #f1f1f1;

			.body-box {
				display: flex;
				flex-direction: column;
				flex-wrap: nowrap;
				gap: 20rpx;
				margin-bottom: 20rpx;

				.upload-box {
					background-color: #fff;
					border-radius: 10rpx;
					padding: 20rpx;

					.upload-box-title {
						display: flex;
						align-items: center;
						font-size: 15px;
						color: #000;
					}

					.upload-box-title-icon {
						height: 15px;
						width: 6rpx;
						background-color: #d81e06;
						margin-right: 20rpx;
					}

					.upload-box-content {
						margin: 20rpx 0;
						font-size: 15px;
						color: #000;

						:deep(p) {
							text-indent: 1em
						}
					}

					.upload-box-date {
						border-top: 1px solid #eee;
						padding-top: 10px;
					}
				}
			}
		}

		.yj-footer {
			height: 140rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #fff;
			display: flex;
			padding: 20rpx;

			.butn {
				display: flex;
				align-items: center;
				width: 100%;
				gap: 20rpx;
			}
		}
	}
</style>