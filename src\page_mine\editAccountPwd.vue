<template>
	<view class="layout-container">
		<!-- 中部 -->
    <view class="yj-body">
      <view class="form-box">
        <up-form labelPosition="left" :model="formData" ref="formRef" labelWidth="190rpx">
          <view style="padding: 10px;margin-bottom:20px;height: 45px;">
            <uni-easyinput type="password" v-model="formData.oldPassword" placeholder="原密码" clearable
                           @change="validatorPasswordNew('oldPassword', $event)" :inputBorder="false"
            ></uni-easyinput>
            <view ><up-line color="#e7e7e7" :hairline="false"></up-line></view>
            <view class="tip-view" v-if="showOldPwdTip">{{ tip.oldPassword }}</view>
          </view>

          <view style="padding: 0 10px 0 10px;margin-top: 10px;height: 45px;">
            <uni-easyinput type="password" v-model="formData.password" placeholder="新密码"
                           @change="validatorPasswordNew('password', $event)" :inputBorder="false"
            ></uni-easyinput>
<!--            <up-line-progress :percentage="pwdStrength" :activeColor="pwdColor" :showText="false" height="8"/>-->

            <view ><up-line color="#e7e7e7" :hairline="false"></up-line></view>
            <view class="tip-view" v-if="showPwdTip">{{ tip.password }}</view>
          </view>

          <view style="padding: 0 10px 0 10px;margin-top: 10px;height: 45px;">
            <uni-easyinput type="password" v-model="formData.enterPassword" placeholder="确认密码" clearable
                           @change="validatorEnterPasswordNew" :inputBorder="false"
            ></uni-easyinput>
            <view ><up-line color="#e7e7e7" :hairline="false"></up-line></view>
            <view class="tip-view" v-if="showEnterPwdTip">{{ tip.enterPassword }}</view>
          </view>

        </up-form>
        <view style="padding: 0 10px 10px 10px;margin-top: 20px;">
          <up-button :plain="true" text="确认修改" shape="circle" color="#fff" hairline="true"
                     customStyle="color:#fff;height:35px;backgroundColor: #d81e06;border-radius: 10rpx;" @click="handleSubmitNew"></up-button>
<!--          <up-button :plain="true" text="返回" shape="circle" hairline="true"
                     customStyle="color:#000;height:35px;margin-top:10px;border-radius: 10rpx;" @click="handleBlack"></up-button>-->
        </view>
        <Verify @success="reqUpdateAccountPwd" :mode="'pop'" :captchaType="'blockPuzzle'" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>

      </view>
    </view>

	</view>
</template>

<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import Verify from "./components/verifition/Verify.vue"
import {updateNetworkAccountPwd} from "@/page_mine/api/sub_mine";
import modal from "@/plugins/modal";

const verify = ref(null)

const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

  // 密码强度
  const pwdStrength = ref(0)
  const pwdColor = ref('red')

	const formRef = ref(null)
	const formData = ref({ // 表单数据
		id: undefined,
		nickname: undefined,
		account: undefined,
    oldPassword: undefined,
		password: undefined,
		enterPassword: undefined
	})
  const showOldPwdTip = ref(false)
  const showPwdTip = ref(false)
  const showEnterPwdTip = ref(false)
  const tip = ref({
    oldPassword: undefined,
    password: undefined,
    enterPassword: undefined,
  })
	/*const rules = ref({ // 自定义校验
    oldPassword: {
			validator: validatorPassword,
			trigger: ['blur', 'change']
		},
		password: {
			validator: validatorPassword,
			trigger: ['blur', 'change']
		},
		enterPassword: {
			validator: validatorEnterPassword,
			trigger: ['blur', 'change']
		}
	})*/

	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}

  // 密码强度校验
  const checkPasswordStrength = (password) => {
    // 这里简单示例密码强度的计算，实际情况需要更复杂的逻辑
    const minLength = 6;
    if (!password) {
      pwdStrength.value = 0;
    } else if (password.length < minLength) {
      pwdColor.value='red'
      pwdStrength.value = 20
    } else if (password.length === minLength) {
      pwdColor.value='red'
      pwdStrength.value = 40
    } else if (password.length <= minLength + 3) {
      pwdColor.value='orange'
      pwdStrength.value = 60
    } else if (password.length <= minLength + 5) {
      pwdColor.value='green'
      pwdStrength.value = 80
    } else {
      pwdColor.value='green'
      pwdStrength.value = 100
    }
  }

  // 确定事件New
  const handleSubmitNew = () => {
    let validOldPassword = validatorPasswordNew('oldPassword', formData.value.oldPassword)
    let validPassword = validatorPasswordNew('password', formData.value.password)
    let validEnterPassword = validatorEnterPasswordNew(formData.value.enterPassword)
    if (validOldPassword && validPassword && validEnterPassword) {
      // 唤起验证码
      if (verify.value) {
        // verify.value.refresh()
        verify.value.show()
      }
    } else {
      console.log('校验失败 ')
    }
  }
	// 检验密码New
	function validatorPasswordNew(type, value) {
		const regex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/
    if (type === 'oldPassword') {
      if (!value) {
        tip.value.oldPassword = '请输入原密码'
        showOldPwdTip.value = true
      } else if (!regex.test(value)){
        tip.value.oldPassword = '密码中必须包含字母数字至少6~20个字符'
        showOldPwdTip.value = true
      } else {
        tip.value.oldPassword = ''
        showOldPwdTip.value = false
        return true
      }
    } else if (type === 'password') {
      // checkPasswordStrength(value)
      if (!value) {
        tip.value.password = '请输入新密码'
        showPwdTip.value = true
      } else if (!regex.test(value)){
        tip.value.password = '密码中必须包含字母数字至少6~20个字符'
        showPwdTip.value = true
      } else {
        tip.value.password = ''
        showPwdTip.value = false
        return true
      }
    }
	}
	// 检验二次密码New
	function validatorEnterPasswordNew(value) {
		if (!value) {
      tip.value.enterPassword = '请输入确认密码'
      showEnterPwdTip.value = true
      return false
		} else if (value != formData.value.password) {
      tip.value.enterPassword = '两次密码不一致'
      showEnterPwdTip.value = true
      return false
		} else {
      tip.value.enterPassword = ''
      showEnterPwdTip.value = false
      return true
		}
	}
  // 发送请求修改交易密码
  const reqUpdateAccountPwd = () => {
    updateNetworkAccountPwd(formData.value).then(res => {
      if (res.code === 0) {
        modal.msgSuccess('操作成功！')
        uni.navigateBack()
      }
    }).finally(() => {
      verify.value.hide()
    })
  }


	onMounted(() => {
		eventChannel.on('params', function(data) {
			if (data) {
				// formRef.value.setRules(rules)
				formData.value = data
        formData.value.oldPassword = undefined
        formData.value.password = undefined
        formData.value.enterPassword = undefined
			}
		})
	})
</script>

<style scoped lang="scss">
.tip-view{
  color: #e50d0d;
  text-align: right;
  font-size: 25rpx;
}
	.layout-container {
		height: calc(100vh - var(--window-top));

		.yj-body {
			//height: calc(100% - 140rpx);
			height: calc(100%);
			overflow: auto;
			padding: 20rpx;
			background-color: #f1f1f1;

			.form-box {
				background-color: #fff;
				padding: 20rpx;
				border-radius: 10rpx;

				:deep(.u-form-item__body__right__message) {
					font-size: 14px;
					line-height: 14px;
					text-align: right;
					color: #d81e06;
					margin-bottom: 20rpx;
				}

				:deep(.u-form-item) {
					border-bottom: 1px solid #f1f1f2;
					margin-bottom: 36rpx;
				}

				:deep(.u-form-item__body__left__content__label) {
					color: #606266;
				}

        :deep(.uni-easyinput__content-input) {
          padding-left: 5px!important;
          //border-bottom: 1px solid #e7e7e7;
        }
			}
		}

		.yj-footer {
			height: 140rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #fff;
			display: flex;
			padding: 20rpx;

			.butn {
				display: flex;
				align-items: center;
				width: 100%;
				gap: 20rpx;
			}
		}
	}
</style>