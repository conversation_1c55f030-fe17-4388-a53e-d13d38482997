import {
	TextEncoder
} from 'text-decoding'

/**
 * 客户单cpcl打印模板
 */
export const orderCpclTemplateData = '' +
	'! 0 200 200 781 1\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 235 12 客户联\n' +
	'SETBOLD 0\n' +
	'LINE 402 75 402 75 2\n' +
	'LINE 150 56 150 56 2\n' +
	'LINE 0 0 572 0 2\n' +
	'LINE 0 45 572 45 2\n' +
	'LINE 0 133 572 133 2\n' +
	'LINE 0 181 572 181 2\n' +
	'LINE 0 87 572 87 2\n' +
	'LINE 100 49 100 735 2\n' +
	'LINE 366 49 366 135 2\n' +
	'LINE 235 47 235 133 2\n' +
	'LINE 0 225 572 225 2\n' +
	'LINE 0 266 572 266 2\n' +
	'LINE 366 183 366 268 2\n' +
	'LINE 235 181 235 266 2\n' +
	'LINE 210 291 212 291 2\n' +
	'LINE 0 375 572 375 2\n' +
	'LINE 0 416 572 416 2\n' +
	'LINE 460 418 460 504 2\n' +
	'LINE 235 462 235 504 2\n' +
	'LINE 572 2 572 735 2\n' +
	'LINE 0 2 0 735 2\n' +
	'LINE 0 733 572 733 2\n' +
	'LINE 0 504 572 504 2\n' +
	'LINE 366 506 366 735 2\n' +
	'LINE 0 562 366 562 2\n' +
	'LINE 0 625 366 625 2\n' +
	'LINE 0 462 572 462 2\n' +
	'LINE 0 329 572 329 2\n' +
	'LINE 366 331 366 462 2\n' +
	'LINE 235 331 235 416 2\n' +
	'TEXT 7 2 3 58 单号\n' +
	// 'SETBOLD 2\n' +
	'TEXT 55 2 110 56 *orderCode*\n' +
	// 'SETBOLD 0\n' +
	'TEXT 7 2 272 58 时间\n' +
	'CENTER\n' +
	'TEXT 7 2 375 56 *dateTime02*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 97 发站\n' +
	'TEXT 7 2 112 102 *logisticsNetworkId*\n' +
	'TEXT 7 2 272 102 到站\n' +
	'CENTER\n' +
	'TEXT 7 2 375 102 *receiveNetworkName*\n' +
	'LEFT\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 3 150 目的地\n' +
	'CENTER\n' +
	'TEXT 7 2 112 150 *destination*\n' +
	'LEFT\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 3 189 发货人\n' +
	'TEXT 7 2 112 193 *sendName*\n' +
	'TEXT 7 2 272 193 电话\n' +
	'CENTER\n' +
	'TEXT 7 2 375 193 *sendPhone*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 235 收货人\n' +
	'TEXT 7 2 112 235 *collectName*\n' +
	'TEXT 7 2 272 235 电话\n' +
	'CENTER\n' +
	'TEXT 7 2 375 239 *collectPhone*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 287 收货地址\n' +
	'CENTER\n' +
	'TEXT 7 2 112 289 *collectAddress*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 339 货名\n' +
	'TEXT 7 2 112 339 *goodsType*\n' +
	'TEXT 7 2 243 339 包装/件数\n' +
	'CENTER\n' +
	'TEXT 7 2 375 339 *packMethodAndNum*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 381 送货方式\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 112 383 *deliveryMethod*\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 243 383 重量/体积\n' +
	'CENTER\n' +
	'TEXT 7 2 375 385 *weightAndVolume*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 429 等通知\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 152 429 *releaseMethod*\n' +
	'TEXT 7 2 383 429 回单\n' +
	'SETBOLD 0\n' +
	'CENTER\n' +
	'TEXT 7 2 475 429 *isReceipt*\n' +
	'LEFT\n' +
	'TEXT 7 2 3 475 费用合计\n' +
	'TEXT 7 2 112 475 *shouldPayAmount*\n' +
	'TEXT 7 2 303 475 付款方式\n' +
	'CENTER\n' +
	'TEXT 7 2 470 475 *payMethod*\n' +
	'LEFT\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 3 525 代收货款\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 112 525 *deliveryAmount*\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 3 585 到付合计\n' +
	'TEXT 7 2 112 583 *payTotal*\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 3 668 备注\n' +
	'TEXT 7 2 112 668 *remark*\n' +
	'B QR 385 531 M 2 U 8\n' +
	'MA,*qrcode*\n' +
	'ENDQR\n' +
	'TEXT 7 2 30 743 *logisticsNetworkAddress*\n' +
	'TEXT 7 2 363 743 *logisticsNetworkPhone*'

/**
 * 标签cpcl打印模板
 */
export const wayBillCpclTemplate = '' +
	'! 0 200 200 781 1\n' +
	'N-DOTS\n' +
	'LINE 199 281 202 281 2\n' +
	'LINE 0 0 562 0 2\n' +//上边框
	'LINE 0 0 0 775 2\n' +
	'LINE 562 0 562 775 2\n' +
	'LINE 0 775 564 775 2\n' +
	'LINE 0 58 562 58 2\n' +
	'LINE 0 204 562 204 2\n' +
	'LINE 0 268 564 268 2\n' +
	'LINE 177 204 177 270 2\n' +
	'LINE 285 58 285 206 2\n' +
	'LINE 287 131 562 131 2\n' +
	'LINE 0 475 562 475 2\n' +
	'LINE 0 533 562 533 2\n' +
	'LINE 177 270 177 477 2\n' +
	'LINE 0 418 177 418 2\n' +
	'LINE 0 360 358 360 2\n' +
	'LINE 0 314 177 314 2\n' +
	'LINE 358 268 358 533 2\n' +
	'LINE 177 477 177 533 2\n' +
	'LINE 0 675 564 675 2\n' +
	'TEXT 7 2 6 16 多多物流超市 & *logisticsName*\n' +
	'TEXT 7 2 430 4 *nowDate*\n' +
	'TEXT 7 2 430 31 *nowTime*\n' +
	'SETBOLD 2\n' +
	'SETMAG 2 2\n' +
	'TEXT 7 0 22 75 A\n' +
	'TEXT 7 0 16 133 01\n' +
	'INVERSE-LINE 2 60 2 206 75\n' +
	'SETMAG 1 2\n' +
	'TEXT 0 30 130 106 *arrivedBranch*\n' +
	'TEXT 7 0 390 75 货号\n' +
	'TEXT 7 0 380 141 *itemNo*\n' +
	'SETBOLD 2\n' +
	'SETMAG 1 1\n' +
	'TEXT 7 1 22 227 *sendStation*\n' +
	'SETMAG 0 0\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 262 227 *sendBasicInfo*\n' +
	'TEXT 7 2 37 283 *goodsNameAndPackMethod*\n' +
	'TEXT 7 2 33 327 *totalWeightAndTotalVolume*\n' +
	'SETBOLD 2\n' +
	'SETMAG 2 2\n' +
	'TEXT 7 2 214 287 *deliveryMethod*\n' +
	'SETMAG 0 0\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 43 375 *isReceipt*\n' +
	'TEXT 7 2 27 431 *releaseMethod*\n' +
	'SETBOLD 2\n' +
	'SETMAG 2 2\n' +
	'TEXT 7 2 208 397 *collectionDelivery*\n' +
	'SETMAG 0 0\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 22 489 *sendStation*\n' +
	'TEXT 7 2 210 489 郑州分拨\n' +
	'TEXT 7 2 427 489 *arrivedBranch*\n' +
	'SETBOLD 2\n' +
	'TEXT 7 2 10 552 *collectBasicInfo*\n' +
	'SETBOLD 0\n' +
	'TEXT 7 2 8 593 *collectAddress*\n' +
	'TEXT 7 2 6 637 *notice*\n' +
	// 'TEXT 7 2 12 751 *waybillCode*\n' +
	'B QR 371 285 M 2 U 8\n' +
	'MA,*qrcode*\n' +
	'ENDQR\n' +
	'CENTER\n' +
	'BARCODE-TEXT 7 0 5\n' +
	'BARCODE 128 2 1 50 12 681 *barCode*\n'+
	'BARCODE - TEXT ON'
	// 'BARCODE 128 2 1 70 12 681 *barCode*'

/**
 * 将cpcl指令转换为字节流
 * @param dataArr cpcl指令字符串列表
 * @returns {ArrayBufferLike}
 */
export const getCpclDataBuffer = (dataArr) => {
	const encode = new TextEncoder('gb18030', {
		NONSTANDARD_allowLegacyEncoding: true,
	})
	let result = []

	for (let i = 0; i < dataArr.length; i++) {
		// console.log(dataArr[i])
		result.push(encode.encode(dataArr[i] + '\r\n'))
	}
	return arrayBufferCat(result).buffer
}

export const cpclGraphics = (bitmap) => {
	const encode = new TextEncoder('gb18030', {
		NONSTANDARD_allowLegacyEncoding: true,
	})
	let result = []
	const commandsEnd = 'PRINT \r\n'
	// 像素转换位图
	const w = bitmap.width
	const width = parseInt((bitmap.width + 7) / 8 * 8 / 8)
	const height = bitmap.height
	const bits = new Uint8Array(height * width)
	for (let y = 0; y < height; y++) {
		for (let x = 0; x < w; x++) {
			const color = bitmap.data[(y * w + x) * 4 + 1]
			if (color > 128) {
				bits[parseInt(y * width + x / 8)] |= (0x80 >> (x % 8))
			}
		}
	}
	let r = []
	for (let i = 0; i < bits.length; i++) {
		//result.push((~bits[i]) & 0xFF)
		r.push((~bits[i]) & 0xFF);
	}
	// 指令拼接
	result.push(encode.encode(`! 0 200 200 ${height} 1 \r\n`))
	result.push(encode.encode(`CG ${width} ${height} 0 0 \r\n`))
	result.push(new Uint8Array(r))
	result.push(encode.encode(`\r\n`))
	result.push(encode.encode(commandsEnd))
	return arrayBufferCat(result).buffer
}

/* export const escGraphics = (bitmap) => {
	const encode = new TextEncoder('gb2312', {
      NONSTANDARD_allowLegacyEncoding: true,
    })
	const w = bitmap.width
	const h = bitmap.height
	const bitw = parseInt(String((w + 7) / 8)) * 8
	const bith = h
	const pitch = parseInt(String(bitw / 8))
	const bits = new Uint8Array(bith * pitch)
	
} */

const arrayBufferCat = (arrays) => {
	let totalLength = 0
	for (let arr of arrays) {
		totalLength += arr.length
	}
	let result = new Uint8Array(totalLength)
	let offset = 0;
	for (let arr of arrays) {
		result.set(arr, offset)
		offset += arr.length
	}
	return result
}