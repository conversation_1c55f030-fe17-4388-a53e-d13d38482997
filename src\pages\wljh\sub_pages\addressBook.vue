<template>
	<view class="layout-sub-addressBook">
		<view class="custom-search">
			<up-search v-model="searchData.searchParam" placeholder="请输入姓名/手机号/公司名称" shape="round" :showAction="false"
				bgColor="#fff" borderColor="#f4f4f5" @clickIcon="getListInfo" @search="getListInfo"></up-search>
		</view>

		<view class="custom-body">
			<view class="custom-body-header-btn" v-if="isShowChecked">
				<up-text text="一键删除" type="error" align="right" @click="handleDeleteAll"></up-text>
			</view>

			<up-checkbox-group v-model="checkboxValue" placement="column">
				<view class="custom-panel" v-for="item in listInfo">
					<view class="custom-panel-checked" v-if="isShowChecked">
						<up-checkbox :name="item.id"></up-checkbox>
					</view>

					<view class="custom-panel-main">
						<up-row gutter="10">
              <up-col span="8">
                <up-input border="none" v-model="item.mobile" readonly>
                  <template #prefix>
                    <up-text text="手机号" margin="0 3px 0 0" type="tips"></up-text>
                  </template>
                </up-input>
              </up-col>
							<up-col span="4">
								<up-input border="none" v-model="item.name" readonly>
									<template #prefix>
										<up-text text="姓名" margin="0 3px 0 0" type="tips"></up-text>
									</template>
								</up-input>
							</up-col>
						</up-row>
            <!--<up-row>
              <up-col span="12">
                <up-input border="none" v-model="item.idCard" readonly>
                  <template #prefix>
                    <up-text text="身份证号" margin="0 3px 0 0" type="tips"></up-text>
                  </template>
                </up-input>
              </up-col>
            </up-row>-->
            <up-row>
							<up-col span="12">
								<up-input border="none" v-model="item.showAddress" readonly>
									<template #prefix>
										<up-text text="地址" margin="0 3px 0 0" type="tips"></up-text>
									</template>
								</up-input>
							</up-col>
						</up-row>
						<up-row customStyle="justify-content: space-between;column-gap: 10px;">
							<view>
								<up-radio-group v-model="radioValue" iconPlacement="left" @change="handleRadioChange">
									<up-radio activeColor="red" :name="item.id" label="设置地址"></up-radio>
								</up-radio-group>
							</view>
							<view style="display: flex; gap: 10px;">
								<view>
									<up-button :plain="true" text="编辑" @click="handleClick('update', item)"></up-button>
								</view>
								<view style="padding-right:10rpx;">
									<up-button :plain="true" text="删除" @click="handleDelete(item.id)"></up-button>
								</view>
							</view>
						</up-row>
					</view>
				</view>
			</up-checkbox-group>
		</view>


		<view class="custom-footer">
			<view style="width: 200rpx;">
				<up-button shape="circle" :plain="true" text="管 理" @click="handleGuanli"></up-button>
			</view>
			<view style="width: 200rpx;">
				<up-button shape="circle" type="error" text="新增地址" @click="handleClick('create')"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted,
		getCurrentInstance
	} from 'vue'
	import modal from '@/plugins/modal'

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const checkboxValue = ref([]) // 多选框数据
	const isShowChecked = ref(false) // 是否显示多选框
	const radioValue = ref() // 默认地址选中
	const listInfo = ref([]) // 地址信息
	const searchData = ref({ // 检索条件
		searchParam: undefined
	})

	// 管理点击事件
	const handleGuanli = () => {
		isShowChecked.value = !isShowChecked.value
		if (!isShowChecked.value) {
			checkboxValue.value = []
		}
	}
	// 默认地址选中事件
	const handleRadioChange = (val) => {
		let param = {}
		listInfo.value.map(item => {
			if (item.id === val) {
				param = item
			}
		})
		eventChannel.emit('addressBookChild', param);
	}
	// 获取列表
	const getListInfo = async () => {
    listInfo.value = [
      {
        "pageNo": 1,
        "pageSize": 10,
        "id": 16,
        "name": "演示线索",
        "followUpStatus": true,
        "contactLastTime": [
          1708760561000
        ],
        "contactLastContent": "111",
        "contactNextTime": [
          1708272000000
        ],
        "ownerUserId": 1,
        "ownerTime": [
          1708694174000
        ],
        "lockStatus": false,
        "dealStatus": false,
        "mobile": "13577788446",
        "telephone": null,
        "idCard": null,
        "qq": null,
        "wechat": null,
        "email": null,
        "areaId": null,
        "detailAddress": "幸福小区6栋501",
        "industryId": null,
        "level": null,
        "source": null,
        "remark": null,
        "createTime": [
          1708694174000
        ],
        "addressOne": "河南省",
        "addressTwo": "郑州市",
        "addressThree": "金水区",
        "addressFour": "大石桥街道办事处",
        "addressFive": "金沙社区居民委员会"
      },
      {
        "pageNo": 1,
        "pageSize": 10,
        "id": 17,
        "name": "演示客户",
        "followUpStatus": false,
        "contactLastTime": null,
        "contactLastContent": null,
        "contactNextTime": null,
        "ownerUserId": null,
        "ownerTime": [
          1708441517000
        ],
        "lockStatus": false,
        "dealStatus": false,
        "mobile": "18818260203",
        "telephone": "18818260223",
        "idCard": "******************",
        "qq": null,
        "wechat": null,
        "email": "<EMAIL>",
        "areaId": null,
        "detailAddress": "河南省郑州市",
        "industryId": null,
        "level": null,
        "source": 1,
        "remark": null,
        "createTime": [
          1708697093000
        ],
        "addressOne": null,
        "addressTwo": null,
        "addressThree": null,
        "addressFour": null,
        "addressFive": null
      }
    ]
    listInfo.value.forEach(item => {
      item.showAddress = applyAddress(item)
    })
	}
  const applyAddress = (data) => {
    let address = ''
    if (data.addressOne) {
      address += data.addressOne
    }
    if (data.addressTwo) {
      address += data.addressTwo
    }
    if (data.addressThree) {
      address += data.addressThree
    }
    if (data.addressFour) {
      address += data.addressFour
    }
    if (data.addressFive) {
      address += data.addressFive
    }
    if (data.detailAddress) {
      address += data.detailAddress
    }
    return address
  }
	// 新增、修改页面
	const handleClick = (type, data) => {
		uni.navigateTo({
			url: '/pages/wljh/sub_pages/addressBookForm',
			events: {
				childFn: function(data) {
					if (data) {
            data.showAddress = applyAddress(data)
            listInfo.value.push(data)
					}
				}
			},
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('parentParam', {
					type: type,
					data: data
				})
			}
		});
	}
	// 删除
	const handleDelete = (id) => {
    modal.msgSuccess('删除成功')
    listInfo.value = listInfo.value.filter(item => item.id !== id)
	}
	// 删除全部
	const handleDeleteAll = () => {
		if (checkboxValue.value.length > 0) {
      modal.msgSuccess('删除成功')
      listInfo.value = []
      checkboxValue.value = []
    }
	}
	// 初始化
	onMounted(() => {
		getListInfo()
	})
</script>

<style scoped lang="scss">
	.layout-sub-addressBook {
		background-color: #f1f1f1;
		// height: 100vh;
		height: calc(100vh - 100rpx);
		overflow: auto;
		padding: 0 20rpx;

		.custom-search {
			background-color: #fff;
			padding: 20rpx;
			border-top: 1px solid #f4f4f5;
			margin: 0 -20rpx;
			height: 110rpx;
		}

		.custom-body {
			height: calc(100% - 110rpx);
			overflow: auto;
      padding-bottom: 110rpx;

			:deep(.u-text__value--tips) {
				color: #606266;
			}

			.custom-body-header-btn {
				background-color: #fff;
				border-radius: 5px;
				padding: 20rpx;
				margin-top: 20rpx;
			}

			.custom-panel {
				//padding: 20rpx;
				//background-color: #fff;
				margin: 20rpx 0;
				display: flex;
				align-items: center;
				//border-radius: 5px;

				.custom-panel-checked {
					margin-right: 20rpx;
				}

				.custom-panel-main {
					background-color: #fff;
					border-radius: 5px;
					padding: 20rpx;
				}

				:deep(.u-button--plain.u-button--info) {
					height: 50rpx;
					border-radius: 30rpx;
				}

				:deep(.u-button__text) {
					font-size: 12px !important;
				}
				
				:deep(.u-button--plain.u-button--info){
					color:#606266;
				}
			}
		}


		.custom-footer {
			height: 100rpx;
			background-color: #fff;
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			gap: 60rpx;
			justify-content: center;
			align-items: center;

			:deep(.u-button) {
				height: 70rpx;
			}
			
			:deep(.u-button--error){
				background-color: #d81e06;
			}
			
			:deep(.u-button--plain.u-button--info){
				color:#000;
			}
		}
	}
</style>