<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
					<up-form-item label="操作账户" prop="accountId" borderBottom="true">
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowAccountSelect" >
                <text>{{ formData.accountName }}</text>
                <up-icon v-if="formType==='create'" name="arrow-right" size="20"></up-icon>
              </view>
            </template>
					</up-form-item>
          <up-form-item v-if="selectedAccount && formType==='create'" label="" prop="amountInfo" borderBottom="true">

              <up-row>
                <up-col span="12">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="总金额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.totalAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
              </up-row>
              <up-row>
                <up-col span="6">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="可用余额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.availableAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
                <up-col span="6">
                  <view style="color: #606266;text-align: center;display: flex;justify-content: center;">
                    <up-text text="冻结金额：" size="13" align="right" color="#606266"></up-text>
                    <up-text :text="selectedAccount.freezeAmount" mode="price" size="12" align="left" color="#606266"></up-text>
                  </view>
                </up-col>
              </up-row>
          </up-form-item>
          <up-form-item label="本次划转金额" prop="dealAmount" borderBottom="true">
            <up-input v-model="formData.dealAmount" mode="price" fontSize="15px" color="#000"
                      border="none" inputAlign="right" :adjustPosition="true" safe-area
                      cursor-spacing="0" type="digit"></up-input>
          </up-form-item>
          <up-form-item v-if="formType==='create'" label="目标账户" prop="toAccountId" >
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowToAccountSelect" >
                <text>{{ formData.toAccountName }}</text>
                <up-icon name="arrow-right" @click="handleShowToAccountSelect" size="20"></up-icon>
              </view>
              </template>
          </up-form-item>

          <up-form-item v-if="formType!=='create' && formData.changeAmount < 0" label="目标账户" prop="toAccountId" borderBottom="true">
            <up-input v-model="formData.toAccountName" border="none" inputAlign="right"
                      readonly></up-input>
          </up-form-item>
          <up-form-item v-if="formType!=='create' && formData.changeAmount >= 0" label="来源账户" prop="toAccountId" borderBottom="true">
            <up-input v-model="formData.toAccountName" border="none" inputAlign="right"
                      readonly></up-input>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="划转时间" prop="changeTimeLabel" >
            <up-input v-model="formData.changeTimeLabel" border="none" inputAlign="right" readonly></up-input>
          </up-form-item>
				</up-form>
			</view>
		</view>
		<view class="custom-footer" v-if="formType==='create'">
			<view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>

    <up-action-sheet :show="showAccountSelect" :actions="radiosAccountIdList" title="请选择操作账户" @close="showAccountSelect=false"
                     @select="handleAccountSelect">
    </up-action-sheet>
    <up-action-sheet :show="showToAccountSelect" :actions="radiosToAccountIdList" title="请选择目标账户" @close="showToAccountSelect=false"
                     @select="handleToAccountSelect">
    </up-action-sheet>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
  import {
    accountListSingle, getAccountTypeLabel,getAccountRoleLabel, transferAccount
  } from "@/page_mine/api/sub_mine";
	import modal from '@/plugins/modal'
  import {formatDate} from "@/api/common";

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    id: undefined,
    accountRole: undefined,
    mainId: undefined,
    accountType: undefined,
    accountId: undefined,
    accountName: undefined,
    accountTypeLabel: undefined,
    overAllowAmount: undefined,
    totalAmount: undefined,
    availableAmount: undefined,
    freezeAmount: undefined,
    dealAmount: undefined,
    toAccountRole: undefined,
    toMainId: undefined,
    toAccountId: undefined,
    toAccountName: undefined,
  })

  const radiosAccountIdListAll = ref([]) // 总账户列表

  const radiosAccountIdList = ref([]) // 账户列表
  const selectedAccount = ref(undefined) // 选中的账户
  const showAccountSelect = ref(false) // 显示账户选择
  // 显示账户选择
  const handleShowAccountSelect = () => {
    if(formType.value==='create') {
      showAccountSelect.value=true
      radiosAccountIdList.value = []
      radiosAccountIdList.value.push(...radiosAccountIdListAll.value.filter(item => item.id !== formData.value.toAccountId))
    }
  }
  // 选中账户
  const handleAccountSelect = (item) => {
    formData.value.accountId = item.value
    formData.value.accountName = item.name
    selectedAccount.value = item
    showAccountSelect.value = false
  }

  const radiosToAccountIdList = ref([]) // 目标账户列表
  const selectedToAccount = ref(undefined) // 选中的目标账户
  const showToAccountSelect = ref(false) // 显示目标账户选择
  // 显示目标账户选择
  const handleShowToAccountSelect = () => {
    if(formType.value==='create') {
      showToAccountSelect.value=true
      radiosToAccountIdList.value = []
      radiosToAccountIdList.value.push(...radiosAccountIdListAll.value.filter(item => item.id !== formData.value.accountId))
    }
  }
  // 选中目标账户
  const handleToAccountSelect = (item) => {
    formData.value.toAccountId = item.value
    formData.value.toAccountName = item.name
    selectedToAccount.value = item
    showToAccountSelect.value = false
  }

	// 提交信息
	const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }
    let data = {...formData.value}
		await transferAccount(data).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFn', true);
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}

  // 校验表单
  const validateForm = () => {
    if (!formData.value.accountId) {
      modal.msgError('请选择操作账户')
      return false
    }
    if (!formData.value.dealAmount) {
      modal.msgError('请输入划转金额')
      return false
    }
    if (!formData.value.toAccountId) {
      modal.msgError('请选择目标账户')
      return false
    }
    return true
  }

	// 初始化页面
	const initPage = (obj) => {
    // console.log(obj)
		if (obj.data) {
			formType.value = obj.type
			formData.value = {...obj.data}
      formData.value.changeTimeLabel = formatDate(formData.value.changeTime, 'yyyy-MM-dd HH:mm:ss')

      accountListSingle({accountRole: '2', mainId: formData.value.mainId}).then(res => {
        if (res && res.data) {
          for (let item of res.data) {
            if (['NETWORK_FREIGHT_PRE','NETWORK_FREIGHT_DIVIDE','NETWORK_RED_PACKET','NETWORK_BROKERAGE','NETWORK_DEPOSIT'].indexOf(item.accountType) < 0) {
              continue
            }
            item.name = getAccountTypeLabel(item.accountType)
            item.checked = false
            item.value = item.id
            radiosAccountIdListAll.value.push(item)
          }
          if (!formData.value.accountId) {
            formData.value.accountId = res.data[0].id
          }
          selectedAccount.value = radiosAccountIdListAll.value.find(item => item.value === formData.value.accountId)
          formData.value.accountName = radiosAccountIdListAll.value.find(item => item.value === formData.value.accountId)?.name

          radiosAccountIdList.value.push(...radiosAccountIdListAll.value.filter(item => item.id !== formData.value.toAccountId))
          radiosToAccountIdList.value.push(...radiosAccountIdListAll.value.filter(item => item.id !== formData.value.accountId))
          if (formData.value.relyAccountInfo) {
            formData.value.toAccountName = getAccountRoleLabel(formData.value.relyAccountInfo.accountRole) + '-' + getAccountTypeLabel(formData.value.relyAccountInfo.accountType)
          }
        }
      })

		}
	}

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

</style>