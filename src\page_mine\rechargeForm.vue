<template>
	<view class="layout-sub-form">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
          <up-form-item label="申请金额" prop="rechargeAmount" borderBottom="true">
            <template #right>
              <view style="min-width:400rpx; display: flex; align-items: center; justify-content: flex-end;"
                    @click="handleShowAmountSelect" >
                <text>{{ formData.rechargeAmountLabel }}</text>
                <up-icon v-if="formType==='create'" name="arrow-right" size="20"></up-icon>
              </view>
              </template>
          </up-form-item>
					<up-form-item v-if="formType==='create'" label="付款码" prop="payQrCode" borderBottom="true">
            <up-album :urls="formData.payQrCode"></up-album>
					</up-form-item>
					<up-form-item label="支付凭证" prop="certUrl" >
            <up-upload v-if="formType==='create'" :fileList="certUrlList" width="150" height="150" mode="scaleToFill" :maxCount="1"
                       uploadText="请上传支付凭证" @afterRead="uploadAfterRead" @delete="uploadDelete"
                       >
            </up-upload>
            <up-album v-if="formType!=='create'" :urls="certUrlList"></up-album>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="申请时间" prop="applyTimeLabel" borderBottom="true">
            <up-input v-model="formData.applyTimeLabel" border="none" inputAlign="right" readonly></up-input>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="申请状态" prop="applyStatus" borderBottom="true">
            <view style="text-align: right">{{ getRechargeApplyStatus(formData.applyStatus) }}</view>
          </up-form-item>
          <up-form-item v-if="formType!=='create'" label="到账凭证" prop="confirmUrl">
            <up-album :urls="confirmUrlList"></up-album>
          </up-form-item>
				</up-form>
			</view>
		</view>
		<view class="custom-footer" v-if="formType==='create'">
			<view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>

    <up-action-sheet :show="showAmountSelect" :actions="radiosAmountList" title="请选择申请金额" @close="showAmountSelect=false"
                     @select="handleAmountSelect">
    </up-action-sheet>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
  import {
    accountListSingle, getPlatformReceiveQrCode, createRechargeApply, getRechargeApplyStatus
  } from "@/page_mine/api/sub_mine";
	import modal from '@/plugins/modal'
  import {delUploadFileApi, uploadFileApi} from "@/page_mine/api/minioUpload";
  import {formatDate} from "@/api/common";

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({
    id: undefined,
    logisticsNetworkId: undefined,
    accountId: undefined,
    rechargeAmount: '1000',
    rechargeAmountLabel: '1,000元',
    certUrl: undefined,
    confirmUrl: undefined,
    applyUserId: undefined,
    applyTime: undefined,
    applyTimeLabel: undefined,
    applyStatus: undefined,
    auditUserId: undefined,
    auditRemark: undefined,
    payQrCode: getPlatformReceiveQrCode('1000')
  })

  // 到账凭证图片
  const confirmUrlList = ref([])

  // 金额列表
  const radiosAmountList = ref([
    {
      value: '1000',
      name: '1,000元'
    },
    {
      value: '2000',
      name: '2,000元'
    },
    {
      value: '3000',
      name: '3,000元'
    },
    {
      value: '5000',
      name: '5,000元'
    },
    // {
    //   value: '8000',
    //   name: '8,000元'
    // }, {
    //   value: '10000',
    //   name: '10,000元'
    // },
  ])
  const showAmountSelect = ref(false) // 显示金额选择
  // 显示金额选择
  const handleShowAmountSelect = () => {
    if(formType.value==='create') {
      showAmountSelect.value=true
    }
  }
  // 选中金额
  const handleAmountSelect = (item) => {
    formData.value.rechargeAmount = item.value
    formData.value.rechargeAmountLabel = item.name
    formData.value.payQrCode = [item.url]
  }

  // 支付凭证图片
  const certUrlList = ref([])
  // 上传前事件
  const uploadAfterRead = (res) => {
    console.log(res)
    certUrlList.value.push(res.file)
    console.log(certUrlList.value)
    addUploadImage(res.file.url)
  }
  // 上传图片到后台
  const addUploadImage = async (filePath) => {
    await uni.showLoading({
      title: '正在上传...',
      mask: true
    })
    try {
      const resData = await uploadFileApi(filePath)
      uni.hideLoading()
      if (resData.code !== 0) {
        modal.msgError("上传失败，请稍后重试")
      } else {
        formData.value.certUrl = resData.data.filePath
      }
    } catch (e) {
      console.log(e)
      try {
        uni.hideLoading()
      } catch (e) {
        console.log('弹窗已关闭')
      }
    }
  }

  // 删除事件
  const uploadDelete = (res) => {
    console.log(res)
    certUrlList.value.splice(res.index, 1)
    // delUploadImage(res.file.url)
  }
  // 移除图片
  const delUploadImage = async (filePath) => {
    await delUploadFileApi(formData.value.certUrl)
  }

	// 提交信息
	const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }
    let data = {...formData.value}
		await createRechargeApply(data).then(res => {
			if (res.code === 0) {
				modal.msgSuccess('操作成功')
				// 父页面发送数据
				eventChannel.emit('childFn', true);
				uni.navigateBack()
			}
		}).catch(error => {

		})
	}

  // 校验表单
  const validateForm = () => {
    if (!formData.value.rechargeAmount) {
      modal.msgError('请选择申请金额')
      return false
    }
    if (!formData.value.certUrl) {
      modal.msgError('请上传支付凭证')
      return false
    }
    return true
  }

	// 初始化页面
	const initPage = (obj) => {
    // console.log(obj)
		if (obj.data) {
			formType.value = obj.type
			formData.value = {...obj.data}
      if (formType.value === 'create') {
        formData.value.rechargeAmount = '1000'
      }
      formData.value.applyTimeLabel = formatDate(formData.value.applyTime, 'yyyy-MM-dd HH:mm:ss')

      if (!formData.value.accountId) {
        accountListSingle({accountRole: '2', mainId: formData.value.logisticsNetworkId}).then(res => {
          if (res && res.data) {
            for (let item of res.data) {
              if (['NETWORK_FREIGHT_PRE'].indexOf(item.accountType) < 0) {
                continue
              }
              formData.value.accountId = item.id
            }
          }
        })
      }
      for (let item of radiosAmountList.value) {
        item.url = getPlatformReceiveQrCode(item.value)
        if (item.value === formData.value.rechargeAmount || item.value === formData.value.rechargeAmount + '') {
          formData.value.payQrCode = [item.url]
          formData.value.rechargeAmountLabel = item.name
        }
      }
      if (formData.value.certUrl) {
        certUrlList.value=[formData.value.certUrl]
      }
      if (formData.value.confirmUrl) {
        confirmUrlList.value=[formData.value.confirmUrl]
      }

		}
	}

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})
	})
</script>

<style scoped lang="scss">
	.layout-sub-form {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

</style>