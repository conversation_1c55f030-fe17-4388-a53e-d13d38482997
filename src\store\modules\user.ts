import { login, logout } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import customStorage from "@/utils/customStorage";
import { defineStore } from "pinia";

export interface LoginForm {
	account : string;
	password : string;
	socialCode : string;
}

const useUserStore = defineStore("user", {
	state: () => ({
		token: getToken(),
		name: "",
		avatar: "",
		roles: Array(),
		permissions: [],
	}),
	actions: {
		// 登录
		login(userInfo : LoginForm) {
			const account = userInfo.account.trim();
			const password = userInfo.password;
			const socialCode = userInfo.socialCode;
			return new Promise((resolve, reject) => {
				login(account, password, socialCode)
					.then((res : any) => {
						setToken(res.data.accessToken);
						this.token = res.data.accessToken;
						customStorage.set("AccountInfo", res.data)
						resolve(null);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		// 退出系统
		logOut() {
			return new Promise<null>((resolve, reject) => {
				logout()
					.then(() => {
						this.token = "";
						this.roles = [];
						this.permissions = [];
						this.name = "";
						this.avatar = "";
						removeToken();
						// customStorage.clear()
						customStorage.clearWithoutLoginForm()
						resolve(null);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
	},
});

export default useUserStore;