<template>
	<view class="layout-container">
		<!-- 中部 -->
		<view class="yj-body">
			<view class="body-box">
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>示例</view>
					</view>
					<view class="upload-box-btn">
						<!-- <up-upload :fileList="certificationData.orgPositive" width="300" height="150" :maxCount="1"
							uploadText="营业执照正面">
	
						</up-upload> -->
						<up-image :show-loading="true" :src="shiliImag" width="300" height="150" mode="scaleToFill" @click="click"></up-image>
					</view>
				</view>
				<view class="upload-box">
					<view class="upload-box-title">
						<view class="upload-box-title-icon"></view>
						<view>打印机品牌型号信息</view>
					</view>
					<view class="upload-box-btn">
						<up-upload :fileList="printImageList" width="300" height="150" mode="scaleToFill"  :maxCount="1"
							uploadText="打印机品牌型号信息" @afterRead="uploadAfterRead" @delete="uploadDelete">

						</up-upload>
					</view>
				</view>
			</view>
		</view>
		<!-- 底部 -->
<!--		<view class="yj-footer">
			<view class="butn">
				<up-button :plain="true" text="返回" shape="circle"
					customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handleBlack"></up-button>
			</view>
		</view>-->
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref
	} from 'vue'
	import modal from '@/plugins/modal'
	import {uploadFileApi, delUploadFileApi} from '@/page_mine/api/minioUpload'
	import {createPrintFeedback, deletePrintFeedback} from './api/sub_mine.ts'
	const shiliImag = ref('https://dachisc.wang:9000/wljh/afc16a23a1af4ea160f4e16a25dff2ec1347e2a2695c31f4a03ee2f734d2f308.jpg')
	const printImageList = ref([])
  const feedbackDataId = ref()
  const feedbackImagePath = ref()

	// 上传前事件
	const uploadAfterRead = (res) => {
		console.log(res)
		printImageList.value.push(res.file)
    addUploadImage(res.file.url)
	}
	// 删除事件
	const uploadDelete = (res) => {
		console.log(res)
		printImageList.value.splice(res.index, 1)
    delUploadImage(res.file.url)
	}
	
	// 上传图片到后台并添加反馈记录
	const addUploadImage = async (filePath) => {
    await uni.showLoading({
      title: '正在上传...',
      mask: true
    })
    let savedPath = ''
    let uploadFlag = false
    try {
      const resData = await uploadFileApi(filePath)
      if (resData.code !== 0) {
        uni.hideLoading()
        modal.msgError("上传失败，请稍后重试")
      }
      savedPath = resData.data.filePath
      uploadFlag = true
    } catch (e) {
      console.log(e)
      try {
        uni.hideLoading()
      } catch (e) {
        console.log('弹窗已关闭')
      }
    }

    if (uploadFlag) {
      try {
        // 其他字段在后端设置
        const createRes = await createPrintFeedback({
          photoUrl: savedPath
        })
        uni.hideLoading()
        if (createRes.code === 0) {
          modal.msgSuccess('反馈成功')
          feedbackDataId.value = createRes.data
          feedbackImagePath.value = savedPath
        } else {
          modal.msgError(createRes.msg)
        }
      }catch (e) {
        console.log(e)
        try {
          uni.hideLoading()
        } catch (e) {
          console.log('弹窗已关闭')
        }
      }
    }

	}

	// 删除反馈记录并移除图片
	const delUploadImage = async (filePath) => {
    if (feedbackDataId.value) {
      const deleteRes = await deletePrintFeedback(feedbackDataId.value)
      if (deleteRes.code === 0 && feedbackImagePath.value) {
        const resData = await delUploadFileApi(feedbackImagePath.value)
        feedbackDataId.value = undefined
        feedbackImagePath.value = undefined
      }
    }
	}

	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}
</script>

<style scoped lang="scss">
	.layout-container {
		height: calc(100vh - var(--window-top));

		.yj-body {
			height: calc(100% - 140rpx);
			overflow: auto;
			padding: 20rpx;
			background-color: #f1f1f1;

			.body-box {
				display: flex;
				flex-direction: column;
				flex-wrap: nowrap;
				gap: 20rpx;

				.upload-box {
					background-color: #fff;
					border-radius: 10rpx;
					padding: 20rpx;

					.upload-box-title {
						display: flex;
						align-items: center;
						font-size: 15px;
					}

					.upload-box-title-icon {
						height: 15px;
						width: 6rpx;
						background-color: #d81e06;
						margin-right: 20rpx;
					}

					.upload-box-btn {
						margin: 10px 0;
						display: flex;
						justify-content: center;

						:deep(.u-upload) {
							align-items: center;
						}
					}
				}
			}
		}

		.yj-footer {
			height: 140rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #fff;
			display: flex;
			padding: 20rpx;

			.butn {
				display: flex;
				align-items: center;
				width: 100%;
				gap: 20rpx;
			}
		}
	}
</style>