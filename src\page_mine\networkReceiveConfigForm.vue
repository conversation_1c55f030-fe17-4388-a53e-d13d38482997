<template>
  <view class="layout-sub-form">
    <view class="custom-body">

      <view style="display: flex; align-items: center;padding-top: 20rpx;">
        <view class="upload-box">
          <view class="upload-box-title">
            <view class="upload-box-title-icon"></view>
            <view>收款码 ¥1,000</view>
          </view>
          <view class="upload-box-btn">
            <up-upload :fileList="receivePic.receiveOne" imageMode="scaleToFill" width="150" height="200" :maxCount="1"
                       uploadText="收款码 ¥1,000" @afterRead="afterReadReceiveOne" @delete="deleteReceiveOne">
            </up-upload>
          </view>
        </view>
        <view style="width: 20rpx"></view>
        <view class="upload-box">
          <view class="upload-box-title">
            <view class="upload-box-title-icon"></view>
            <view>收款码 ¥2,000</view>
          </view>
          <view class="upload-box-btn">
            <up-upload :fileList="receivePic.receiveTwo" imageMode="scaleToFill" width="150" height="200" :maxCount="1"
                       uploadText="收款码 ¥2,000" @afterRead="afterReadReceiveTwo" @delete="deleteReceiveTwo">
            </up-upload>
          </view>
        </view>
      </view>
      <view v-show="false" style="display: flex; align-items: center;">
        <view class="upload-box">
          <view class="upload-box-title">
            <view class="upload-box-title-icon"></view>
            <view>收款码 ¥3,000</view>
          </view>
          <view class="upload-box-btn">
            <up-upload :fileList="receivePic.receiveThree" imageMode="scaleToFill" width="150" height="200" :maxCount="1"
                       uploadText="收款码 ¥3,000" @afterRead="afterReadReceiveThree" @delete="deleteReceiveThree">
            </up-upload>
          </view>
        </view>
        <view style="width: 20rpx"></view>
        <view class="upload-box">
          <view class="upload-box-title">
            <view class="upload-box-title-icon"></view>
            <view>收款码 ¥5,000</view>
          </view>
          <view class="upload-box-btn">
            <up-upload :fileList="receivePic.receiveFive" imageMode="scaleToFill" width="150" height="200" :maxCount="1"
                       uploadText="收款码 ¥5,000" @afterRead="afterReadReceiveFive" @delete="deleteReceiveFive">
            </up-upload>
          </view>
        </view>
      </view>


    </view>


    <view class="custom-footer" >
      <view style="width: 100%;padding: 20rpx">
        <up-button type="primary" shape="circle" text="保存" @click="handleSubmit"></up-button>
      </view>
    </view>

  </view>
  <!-- 交易密码弹窗 -->
  <up-modal :show="showAccountPwd" title="请输入交易密码" class="pwdModal"
            showConfirmButton @confirm="handleSubmitFinal"
            showCancelButton @cancel="showAccountPwd=false">
    <uni-easyinput type="password" v-model="formData.accountPwd" placeholder=""></uni-easyinput>
  </up-modal>

</template>

<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import {saveNetworkReceiveConfig} from "@/page_mine/api/sub_mine";
import modal from '@/plugins/modal'
import {getDictDataInfoAll} from "@/api/wljh/task";
import {uploadFileApi} from "@/page_mine/api/minioUpload";
import customStorage from "@/utils/customStorage";

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const formData = ref({
  id: undefined,
  receiveOne: undefined,
  receiveTwo: undefined,
  receiveThree: undefined,
  receiveFive: undefined,
  accountPwd: undefined,
})


// 身份证照片
const receivePic = ref({
  receiveOne: [],
  receiveTwo: [],
  receiveThree: [],
  receiveFive: [],
})
// 读取后事件
const afterReadReceiveOne = (res) => {
  receivePic.value.receiveOne.push(res.file)
  uploadFileApi(res.file.url).then(res => {
    if (res.code === 0) {
      formData.value.receiveOne = res.data.filePath
    }
  })
}
const afterReadReceiveTwo = (res) => {
  receivePic.value.receiveTwo.push(res.file)
  uploadFileApi(res.file.url).then(res => {
    if (res.code === 0) {
      formData.value.receiveTwo = res.data.filePath
    }
  })
}
const afterReadReceiveThree = (res) => {
  receivePic.value.receiveThree.push(res.file)
  uploadFileApi(res.file.url).then(res => {
    if (res.code === 0) {
      formData.value.receiveThree = res.data.filePath
    }
  })
}
const afterReadReceiveFive = (res) => {
  receivePic.value.receiveFive.push(res.file)
  uploadFileApi(res.file.url).then(res => {
    if (res.code === 0) {
      formData.value.receiveFive = res.data.filePath
    }
  })
}
// 删除事件
const deleteReceiveOne = (res) => {
  receivePic.value.receiveOne.splice(res.index, 1)
  formData.value.receiveOne = ""
}
const deleteReceiveTwo = (res) => {
  receivePic.value.receiveTwo.splice(res.index, 1)
  formData.value.receiveTwo = ""
}
const deleteReceiveThree = (res) => {
  receivePic.value.receiveThree.splice(res.index, 1)
  formData.value.receiveThree = ""
}
const deleteReceiveFive = (res) => {
  receivePic.value.receiveFive.splice(res.index, 1)
  formData.value.receiveFive = ""
}

// 提交信息
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  handleShowAccountPwd()
}
// 输入交易密码
const showAccountPwd = ref(false)
const handleShowAccountPwd = () => {
  showAccountPwd.value = true
}
const handleSubmitFinal = async () => {
  if (!validateFormFinal()) {
    return
  }
  let data = {...formData.value}
  await saveNetworkReceiveConfig(data).then(res => {
    if (res.code === 0) {
      modal.msgSuccess('操作成功')
      // 父页面发送数据
      eventChannel.emit('childFn', true);
      uni.navigateBack()
    }
  }).catch(error => {

  })
}


// 校验表单
const validateForm = () => {
  if (!formData.value.receiveOne) {
    modal.msgError('请上传收款码 ¥1,000')
    return false
  }
  if (!formData.value.receiveTwo) {
    modal.msgError('请上传收款码 ¥2,000')
    return false
  }
  /*if (!formData.value.receiveThree) {
    modal.msgError('请上传收款码 ¥3,000')
    return false
  }
  if (!formData.value.receiveFive) {
    modal.msgError('请上传收款码 ¥5,000')
    return false
  }*/
  return true
}
// 校验表单
const validateFormFinal = () => {
  if (!formData.value.accountPwd) {
    modal.msgError('请输入交易密码')
    return false
  }
  return validateForm()
}

// 初始化页面
const initPage = async (obj) => {
  // console.log(obj)
  if (obj) {
    // 审核
    formData.value = {...obj}

    if(formData.value.receiveOne){
      receivePic.value.receiveOne.push({url: formData.value.receiveOne})
    }
    if(formData.value.receiveTwo){
      receivePic.value.receiveTwo.push({url: formData.value.receiveTwo})
    }
    if(formData.value.receiveThree){
      receivePic.value.receiveThree.push({url: formData.value.receiveThree})
    }
    if(formData.value.receiveFive){
      receivePic.value.receiveFive.push({url: formData.value.receiveFive})
    }
  }
}

onMounted(() => {
  eventChannel.on('params', function(data) {
    initPage(data)
  })
})
</script>

<style scoped lang="scss">
.layout-sub-form {
  background-color: #f1f1f1;
  height: calc(100vh - var(--window-top));
  overflow: hidden;
  padding: 0 20rpx;

  .custom-body {
    height: calc(100% - 100rpx);
    overflow: auto;

    .custom-body-form {
      background-color: #fff;
      margin: 20rpx 0;
      padding: 0 20rpx;
      border-radius: 10rpx;
      padding-bottom: 30rpx;

      :deep(.u-form-item__body__left__content__label){
        color: #606266;
      }
      :deep(.u-form-item__body__right__content__slot) {
        display: block;
      }
    }
  }

  .custom-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;

    :deep(.u-button--circle){
      border-radius: 20px;
    }

    :deep(.u-button--primary){
      background-color: #d81e06;
      border-color: #d81e06;
    }
  }
}
.upload-box {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  width: calc(50% - 10rpx);

  .upload-box-title {
    display: flex;
    align-items: center;
    font-size: 15px;
  }

  .upload-box-title-icon {
    height: 15px;
    width: 6rpx;
    background-color: #d81e06;
    margin-right: 20rpx;
  }

  .upload-box-btn {
    margin: 10px 0;

    :deep(.u-upload) {
      align-items: center;
    }

    :deep(.u-upload__button) {
      margin: unset!important;
    }
  }
}
</style>