<template>
	<view class="layout-data">
		<!-- 头部 -->
		<view class="layout-data-header">
			<!-- tab切换栏 -->
			<view class="custom-tabs-menu">
				<up-tabs :list="tabsList" lineColor="#d9001b" :itemStyle="tabsItemStyle" lineWidth="58"
					@click="handleTabsClick"></up-tabs>
			</view>
			<view class="custom-tabs-btn">
				<up-button :plain="true" text="筛选" @click="handleScreenFn"></up-button>
			</view>
		</view>
		<!-- 内容 -->
		<view class="layout-data-body">
			<!-- 揽收统计 -->
			<view v-if="tabsSelected==='collectionStatistics'" class="custom-card"
				v-for="item in collectionStatisticsList">
				<view class="custom-card-header">
					<up-text color="#fff" size="15" :text="item.title"></up-text>
				</view>
				<!-- 开单统计 -->
				<view class="custom-card-order-stat" style="align-items: flex-start;">
					<view class="custom-card-order-stat-right">
						<up-row customStyle="">
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">开单</view>
								<view>{{item.orderCount}} 单</view>
							</up-col>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">处理</view>
								<view>{{item.orderProcessedCount}} 单</view>
							</up-col>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">剩余</view>
								<view>{{item.orderPendCount}} 单</view>
							</up-col>
						</up-row>
          </view>
        </view>
        <view class="custom-card-order-stat">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title">承运</view>
                <view>{{item.waybillCount}} 单</view>
              </up-col>
              <up-col span="4">
                <view class="custom-card-order-stat-right-title">在途</view>
                <view>{{item.waybillPendCount}} 单</view>
              </up-col>
              <up-col span="4">
                <view class="custom-card-order-stat-right-title">签收</view>
                <view>{{item.waybillProcessedCount}} 单</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
						<up-row>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">应收</view>
								<view>{{item.orderCountAmount}} 元</view>
							</up-col>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">实收</view>
								<view>{{item.orderActualAmount}} 元</view>
							</up-col>
						</up-row>
					</view>
				</view>
        <view class="custom-card-order-stat" style="align-items: flex-start;border-bottom: none!important;">
          <view class="custom-card-order-stat-right">
						<up-row>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">应付</view>
								<view>{{item.orderCountAmount}} 元</view>
							</up-col>
							<up-col span="4">
								<view class="custom-card-order-stat-right-title">实付</view>
								<view>{{item.orderActualAmount}} 元</view>
							</up-col>
						</up-row>
					</view>
				</view>

<!--				<view class="custom-card-order-stat"></view>-->
			</view>

			<!-- 揽收明细 -->
			<view v-if="tabsSelected==='collectionDetails'" class="custom-table">
				<view class="custom-table-search">
					<up-search placeholder="请输入姓名/手机号" v-model="searchParam.searchData" shape="round"
						:showAction="false" bgColor="#fff" @clickIcon="collectingMingXiApi"
						@search="collectingMingXiApi"></up-search>
				</view>
				<view class="custom-table-item">
					<up-scroll-list :indicator="false" indicatorWidth="700">
						<view class="custom-table-item-line">
							<view class="custom-table-item-cell">
								<up-text text="日期" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="订单编号" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="运单编号" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="发货人" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="收货人" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="运输状态" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
							<view class="custom-table-item-cell">
								<up-text text="运费情况" lines="1" size="15" color="#000" align="center"></up-text>
							</view>
						</view>
            <up-line color="#f4f4f5" :hairline="false"></up-line>
						<view v-for="(item, index) in collectionDetailsList">
              <view class="custom-table-item-line">
                <view class="custom-table-item-cell">
                  <up-text :text="item.waybillCreateTime" mode="date" format="mm-dd" lines="1" size="15"
                    color="#606266" align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.orderCode" lines="1" size="15" color="#606266"
                    align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.waybillCode" lines="1" size="15" color="#555555"
                    align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.sendName" lines="1" size="15" color="#606266"
                    align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.collectName" lines="1" size="15" color="#606266"
                    align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.logisticsStatusDesc" lines="1" size="15" color="#606266"
                    align="center"></up-text>
                </view>
                <view class="custom-table-item-cell">
                  <up-text :text="item.totalAmount" lines="1" size="15" color="#606266"
                    align="center"></up-text>
                </view>
              </view>
              <up-line v-if="index!==collectionDetailsList.length-1" color="#f4f4f5" :hairline="false"></up-line>
            </view>
					</up-scroll-list>
				</view>
			</view>

      <!-- 结算统计 -->
      <view v-if="tabsSelected==='settlementStatistics'" class="custom-card">
        <view class="custom-card-header">
          <up-text color="#fff" size="15" text="待结算"></up-text>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">待结算运单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleWaybillCount}} 单</view>
              </up-col>
              <up-col span="3">
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">待结算分成</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleDivideCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleDivideAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">到付授信额度</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleOverAllowCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleOverAllowAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">待结算佣金</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleBrokerageCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleBrokerageAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;border-bottom: none!important;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">待结算税费</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleTaxCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.waitSettleTaxAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>

<!--        <view class="custom-card-order-stat"></view>-->
      </view>
      <view v-if="tabsSelected==='settlementStatistics'" class="custom-card">
        <view class="custom-card-header">
          <up-text color="#fff" size="15" text="已结算"></up-text>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">已结算运单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledWaybillCount}} 单</view>
              </up-col>
              <up-col span="3">
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">已结算分成</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledDivideCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledDivideAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">到付授信额度</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledOverAllowCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledOverAllowAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">已结算佣金</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledBrokerageCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledBrokerageAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>
        <view class="custom-card-order-stat" style="align-items: flex-start;border-bottom: none!important;">
          <view class="custom-card-order-stat-right">
            <up-row customStyle="">
              <up-col span="4">
                <view class="custom-card-order-stat-right-title-3">已结算税费</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledTaxCount}} 单</view>
              </up-col>
              <up-col span="3">
                <view>{{settlementStatisticsInfo.settledTaxAmount}} 元</view>
              </up-col>
            </up-row>
          </view>
        </view>

<!--        <view class="custom-card-order-stat"></view>-->
      </view>
		</view>

		<!-- 筛选弹窗 -->
		<view class="custom-popup" v-if="customPopupShow" @click="handlePopupClick">
			<view class="custom-popup-card" @click.stop="customPopupShow=true">
				<up-text text="时间范围" lineHeight="30"></up-text>
				<view class="custom-popup-card-tag" style="margin-bottom: 20rpx;">
					<up-tag v-for="(item, index) in radiosDateList" :key="index" borderColor="#f4f4f5"
						:color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
						:name="index" @click="handlePopupCardFn(item, index)"></up-tag>
				</view>
				<view>
					<up-calendar :defaultDate="defaultDateMultiple" :show="calendarShow" mode="range" title="自定义时间"
						:minDate="minDate" :maxDate="maxDate" monthNum="25" color="#d81e06" @close="handleCalendar"
						@confirm="handleCalendarEnter" allowSameDay></up-calendar>
				</view>
				<view class="custom-card-foot">
					<up-row :gutter="60">
						<up-col :offset="1" :span="5">
							<up-button type="error" shape="circle" text="重置" customStyle="height: 30px;"
                         @tap.stop="handleRest"></up-button>
						</up-col>
						<up-col :span="5">
							<up-button shape="circle" text="确定" customStyle="height: 30px;"
                         @tap.stop="handleEnter"></up-button>
						</up-col>
					</up-row>
				</view>
			</view>
		</view>

	</view>

  <!-- 跳转认证弹窗 -->
  <up-modal
      :content="principal.principalAuditContent"
      :show="principal.showPrincipalAudit" showCancelButton
      @confirm="handlePrincipalAuditApplyForm" :confirmText="principal.principalAuditConfirmText"
      @cancel="handleLogout" cancelText="退出登录"
  ></up-modal>
</template>

<script setup>
	import {
		reactive,
		ref,
		onMounted
	} from 'vue'
	import {
		collectingTongJiApp, collectingMingXiApp, settlementTongjiApp
	} from '@/api/wljh/data'
  import {formatDate} from "@/api/common";
  import customStorage from "@/utils/customStorage";
  import {
    getPrincipalAuditCheckResult,
    handlePrincipalAuditApplyForm,
    getUserInfo,
    handleLogout
  } from "@/api/wljh/mine";
  import {onLoad, onUnload} from "@dcloudio/uni-app";

	const customPopupShow = ref(false) // 筛选弹窗是否显示
	const calendarShow = ref(false) // 筛选弹窗自定义日期是否显示
	const maxDate = ref(formatDate(new Date(), 'yyyy-MM-dd')) // 最大日期
	const minDate = ref(formatDate(new Date(), 'yyyy-MM-dd')) // 最小日期
	const defaultDateMultiple = ref([]) // 默认日期

	const lableParam = ref('') // 数据字典返回值
	const collectionStatisticsList = ref([]) // 揽收统计数据集合
	const collectionDetailsList = ref([]) // 揽收明细数据集合
	const settlementStatisticsInfo = ref({}) // 结算统计数据对象
	const tabsSelected = ref('collectionStatistics') // tab选中Key
	const radiosDateList = ref([{ // 筛选条件（时间范围）
			'name': '今日',
			'value': '1',
			'dictType': 'DATE_TIME',
			'checked': false
		},
		{
			'name': '本周',
			'value': '2',
			'dictType': 'DATE_TIME',
			'checked': false
		},
		{
			'name': '本月',
			'value': '3',
			'dictType': 'DATE_TIME',
			'checked': false
		},
		{
			'name': '自定义时间',
			'value': '4',
			'dictType': 'DATE_TIME',
			'checked': false
		}
	])
	const tabsList = reactive([{ // tab切换栏菜单数据
			name: '揽收统计',
			key: 'collectionStatistics'
		},
		{
			name: '揽收明细',
			key: 'collectionDetails'
		},
		{
			name: '结算统计',
			key: 'settlementStatistics'
		}
	])

	const queryParam = ref({ //揽收统计-查询参数
		dateType: undefined,
		dateList: undefined
	})
  const searchParam = ref({ //揽收明细-查询参数
    searchData: undefined,
    dateType: undefined,
    dateList: undefined
  })
	const queryParam3 = ref({ //结算统计-查询参数
		dateType: undefined,
		dateList: undefined
	})

	// tabs 切换事件
	const handleTabsClick = (item, index) => {
		tabsSelected.value = item.key
		handleRest()
		/* if (item.key === 'collectionStatistics') {
			collectingTongJiApi()
		} else if (item.key === 'collectionDetails') {
			collectingMingXiApi()
		} */
	}

	// 获取揽收统计信息
	const collectingTongJiApi = async () => {
		await collectingTongJiApp(queryParam.value).then(res => {
			if (res.code === 0) {
				collectionStatisticsList.value = res.data
			}
		})
    customPopupShow.value = false
	}

	// 获取揽收明细信息
	const collectingMingXiApi = async () => {
		await collectingMingXiApp(searchParam.value).then(res => {
			if (res.code === 0) {
				collectionDetailsList.value = res.data
			}
		})
    customPopupShow.value = false
	}
	// 获取结算统计信息
	const settlementTongjiApi = async () => {
		await settlementTongjiApp(queryParam3.value).then(res => {
			if (res.code === 0) {
        settlementStatisticsInfo.value = res.data
			}
		})
    customPopupShow.value = false
	}

	// 筛选遮罩层点击事件
	const handlePopupClick = () => {
		customPopupShow.value = false
	}
	// 筛选事件
	const handleScreenFn = () => {
		customPopupShow.value = !customPopupShow.value
	}
	// 筛选选中
	const handlePopupCardFn = (item, index) => {
		item.checked = item.value === '4' ? true : !item.checked
		if(tabsSelected.value === 'collectionStatistics') {
			queryParam.value.dateType = item.checked ? item.value : undefined
		} else if (tabsSelected.value === 'collectionDetails') {
			searchParam.value.dateType = item.checked ? item.value : undefined
		} else if (tabsSelected.value === 'settlementStatistics') {
      queryParam3.value.dateType = item.checked ? item.value : undefined
		}
		
		radiosDateList.value.map((obj, inx) => {
			obj.checked = index != inx ? false : item.checked
		})
		calendarShow.value = item.value != '4' ? false : true
		if (calendarShow.value) {
			getTwoYearsBeforeDate()
		} else {
			if(tabsSelected.value === 'collectionStatistics') {
				queryParam.value.dateList = undefined
				defaultDateMultiple.value = []
			} else if (tabsSelected.value === 'collectionDetails') {
				searchParam.value.dateList = undefined
				defaultDateMultiple.value = []
			} else if (tabsSelected.value === 'settlementStatistics') {
				queryParam3.value.dateList = undefined
				defaultDateMultiple.value = []
			}
		}
	}
	// 获取当前日期两年前的日期
	function getTwoYearsBeforeDate() {
		const today = new Date();
		const twoYearsAgo = new Date(today);
		twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
		minDate.value = formatDate(twoYearsAgo, 'yyyy-MM-dd')
		return minDate.value
	}
	// 日历关闭时触发
	const handleCalendar = () => {
		calendarShow.value = false
		if (!queryParam.value.dateList) {
			radiosDateList.value.map(item => {
				item.checked = false
			})
		}
	}
	// 日历确定时触发
	const handleCalendarEnter = (obj) => {
		calendarShow.value = false
		if (obj) {
			if(tabsSelected.value === 'collectionStatistics') {
				queryParam.value.dateList = obj
				defaultDateMultiple.value = obj
			} else if (tabsSelected.value === 'collectionDetails') {
				searchParam.value.dateList = obj
				defaultDateMultiple.value = obj
			} else if (tabsSelected.value === 'settlementStatistics') {
        queryParam3.value.dateList = obj
				defaultDateMultiple.value = obj
			}
		} else {
			if(tabsSelected.value === 'collectionStatistics') {
				queryParam.value.dateList = undefined
				defaultDateMultiple.value = []
			} else if (tabsSelected.value === 'collectionDetails') {
				searchParam.value.dateList = undefined
				defaultDateMultiple.value = []
			} else if (tabsSelected.value === 'settlementStatistics') {
        queryParam3.value.dateList = undefined
				defaultDateMultiple.value = []
			}
		}
	}

	// 样式
	const tabsItemStyle = reactive({
		width: '200rpx',
		height: '100rpx'
	})

	// 筛选窗确定按钮
	const handleEnter = async () => {
		handlePopupClick()
		if (tabsSelected.value === 'collectionStatistics') {
			await collectingTongJiApi()
		} else if (tabsSelected.value === 'collectionDetails') {
			await collectingMingXiApi()
		} else if (tabsSelected.value === 'settlementStatistics') {
			await settlementTongjiApi()
		}
	}
	// 重置
	const handleRest = async () => {
		handlePopupClick()
		radiosDateList.value.map(item => {
			item.checked = false
		})
		queryParam.value = {
			dateType: undefined,
			dateList: undefined
		}
		searchParam.value.dateType = undefined
		searchParam.value.dateList = undefined
    queryParam3.value = {
      dateType: undefined,
      dateList: undefined
    }
		defaultDateMultiple.value = []
		if (tabsSelected.value === 'collectionStatistics') {
			await collectingTongJiApi()
		} else if (tabsSelected.value === 'collectionDetails'){
			await collectingMingXiApi()
		} else if (tabsSelected.value === 'settlementStatistics'){
			await settlementTongjiApi()
		}
	}


  // 负责人认证判断内容
  const principal = ref({
    showPrincipalAudit: false,
    principalAuditContent: '',
    principalAuditConfirmText: ''
  })
  // 判断是否需要进行个人认证
  const checkPrincipalAudit = async () => {
    principal.value = await getPrincipalAuditCheckResult()
  }

  onLoad(() => {
    // 监听个人认证返回
    uni.$on('childFnPrincipal',d=>{
      checkPrincipalAudit()
    })
  })
  onUnload(() => {
    // 移除监听事件
    uni.$off('childFnPrincipal');
  });

	// 初始化
	onMounted(() => {
    checkPrincipalAudit()
		collectingTongJiApi()
	})
</script>

<style scoped lang="scss">
	.layout-data {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-bottom) - var(--window-top));
		overflow: auto;

		.layout-data-header {
			background-color: #fff;
			height: 100rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.custom-tabs-menu {
				width: 500rpx;
			}

			.custom-tabs-btn {
				padding: 0 20rpx;

				:deep(.u-button--plain.u-button--info) {
					height: 60rpx;
					border-radius: 20px;
					padding: 0 40rpx;
					color: #606266;
				}
			}
		}

		.layout-data-body {
			height: calc(100% - 100rpx);
			padding: 20rpx;
			overflow: auto;

			.custom-card {
				padding: 0 20rpx;
				background-color: #fff;
				border-radius: 5px;
				margin-bottom: 20rpx;

				.custom-card-header {
					background-color: #d81e06;
					height: 60rpx;
					margin: 0 -20rpx;
					border-top-left-radius: 5px;
					border-top-right-radius: 5px;
					display: flex;
					align-items: center;
					padding: 0 20rpx;
				}

				.custom-card-order-stat {
					display: flex;
					font-size: 15px;
					color: #606266;
					padding: 20rpx 0;
					border-bottom: 1px solid #f4f4f5;
					align-items: center;

					.custom-card-order-stat-left {
						color: #000;
						font-size: 15px;
						font-weight: 600;
						padding-right: 10rpx;
						width: 140rpx;
					}

					.custom-card-order-stat-right {
						width: 100%;
						//padding-bottom: 10rpx;
						//border-bottom: 1px solid #f4f4f5;

						:deep(.u-col) {
							display: flex;
							align-items: center !important;
						}

						.custom-card-order-stat-right-title {
							width: 60rpx;
							margin-right: 10rpx;
						}

						.custom-card-order-stat-right-title-3 {

						}
					}
				}
			}

			.custom-card:last-child {
				margin-bottom: 0;
			}

			.custom-table {
				.custom-table-search {}

				.custom-table-item {
					margin-top: 20rpx;
					background-color: #fff;
					padding: 30rpx 20rpx 0 20rpx;
					border-radius: 5px;

					:deep(.u-scroll-list) {
						padding-bottom: 0;
						// border-bottom: 1px solid #f4f4f5;
					}

					:deep(.u-scroll-list__scroll-view__content) {
						flex-direction: column;
					}

					:deep(.u-scroll-list__scroll-view__content) {
						gap: 30rpx;
					}

					.custom-table-item-line {
						display: flex;
						align-items: center;
						height: 50rpx;
						line-height: 50rpx;
						//border-bottom: 1px solid #f4f4f5;

						.custom-table-item-cell {
							max-width: 500rpx;
							padding-right: 10rpx;
							min-width: 120rpx;
						}

						.custom-table-item-cell:nth-child(2) {
							min-width: 200rpx;
						}

						.custom-table-item-cell:nth-child(3) {
							min-width: 200rpx;
						}
					}
				}
			}
		}

		.custom-popup {
			position: absolute;
			top: 100rpx;
			bottom: 0;
			left: 0;
			right: 0;
			background-color: #9093998c;
			z-index: 10000;

			.custom-popup-card {
				background-color: #fff;
				padding: 20rpx;

				.custom-popup-card-tag {
					padding: 20rpx;
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					column-gap: 20rpx;
					row-gap: 20rpx;

					:deep(.u-tag) {
						justify-content: center;
						min-width: 200rpx;
					}
				}

				.custom-card-foot {
					padding-bottom: 26rpx;

					:deep(.u-button--circle) {
						border-radius: 20px;
					}

					:deep(.u-button--error) {
						background-color: #d81e06;
					}
				}
			}
		}

		:deep(.u-tag--primary) {
			background-color: #d81e06;
		}
	}
</style>