<template>
	<view class="layout-mine">
		<view class="layout-mine-essential">
			<up-cell-group>
				<up-cell title="头像" isLink @click="handleHeadPortrait">
					<template #value>
						<up-image :src="dataParam.headPortrait" width="80rpx" height="80rpx"
							></up-image>
					</template>
				</up-cell>
				<up-cell title="昵称">
					<template #value>
						<up-input border="none" v-model="dataParam.nickname" inputAlign="right" color="#606266"
							suffix-icon="edit-pen-fill" @change="handleNickChange"></up-input>
					</template>
				</up-cell>
        <up-cell title="手机号"  >
          <template #value>
            {{ dataParam.account }}
          </template>
        </up-cell>
      </up-cell-group>
		</view>
		<view class="layout-mine-essential">
			<up-cell-group>
        <up-cell title="账号">
          <template #value>
            <up-input border="none" v-model="dataParam.account" inputAlign="right" readonly
                      color="#606266"></up-input>
          </template>
        </up-cell>
        <up-cell title="账号认证" isLink @click="handlePrincipalApproval"></up-cell>
      </up-cell-group>
		</view>
		<view class="layout-mine-essential">
			<up-cell-group>
        <up-cell title="登录密码" isLink @click="handleEditPassword(dataParam)"></up-cell>
      </up-cell-group>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from "vue"
	import customStorage from "@/utils/customStorage"
	import {
		getUserInfo,
		updateUserInfo
	} from '@/api/wljh/mine'
	import {
		uploadFileApi
	} from '@/page_mine/api/minioUpload.ts'

  const defaultHeader = ref('https://dachisc.wang:9000/wljh/4e29406c6510fb74ce5ce3538304a07b1dd7fa6333937e7da6b5e9384af75fb7.svg')

	const dataParam = ref({
		id: undefined,
		headPortrait: defaultHeader,
		nickname: undefined,
		account: undefined,
		logisticsNetworkInfo: {
			id: undefined,
			certification: '0',
			name: undefined,
			address: undefined,
      networkCode: undefined
		}
	})
  const printConfigStatus = ref()
	const defaultNickname = ref() //默认昵称

	// 上传头像
	const handleHeadPortrait = async () => {
		uni.chooseImage({
			count: 1, // 默认9
			sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
			sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
			success: async function(res) {
				// 成功选择图片后，获取图片的临时文件路径
				const tempFilePaths = res.tempFilePaths
				const resData = await uploadFileApi(tempFilePaths[0])
				if (resData.code === 0) {
					dataParam.value.headPortrait = resData.data.filePath
					await updateUserInfo({
						id: dataParam.value.id,
						headPortrait: resData.data.filePath
					}).then(res => {
						console.log(res)
					})
				}
			}
		})
	}
  // 个人认证事件
  const handlePrincipalApproval = () => {
		uni.navigateTo({
			url: '/page_mine/principalApprovalForm',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('params', {
          data: {
            networkId: dataParam.value.logisticsNetworkId,
            accountId: dataParam.value.id,
          },
          type: 'edit'
        })
			}
		})
	}
	// 修改密码事件
	const handleEditPassword = (obj) => {
		uni.navigateTo({
			// url: '/page_mine/logoutOrEditPwd',
			url: '/page_mine/editPassword',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('params', obj)
			}
		})
	}
	// 输入框监听事件
	const handleNickChange = async (value) => {
		let str = JSON.parse(JSON.stringify(value))
		if (!value) {
			str = defaultNickname.value
		}
		await updateUserInfo({
			id: dataParam.value.id,
			nickname: str
		}).then(res => {
			console.log(res)
		})
	}
	// 获取个人详细信息
	const getUserInfoApi = async (param) => {
		await getUserInfo(param).then(res => {
			if (res.code === 0) {
				dataParam.value = res.data
				// 设置头像
				if (!res.data.headPortrait) {
					dataParam.value.headPortrait = defaultHeader
				}
				defaultNickname.value = JSON.parse(JSON.stringify(res.data)).nickname

			}
		})
	}
	// 初始化页面
	const initPage = () => {
		const userInfo = customStorage.get("AccountInfo")
		if (userInfo) {
		}
			getUserInfoApi(userInfo)
	}
	onMounted(() => {
		initPage()
	});

</script>

<style scope lang="scss">
	.layout-mine {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-bottom) - var(--window-top));
		overflow: auto;
		padding: 20rpx;

		.layout-mine-essential {
			background-color: #fff;
			border-radius: 5px;
			margin-bottom: 10rpx;

			:deep(.u-cell__body) {
				height: 120rpx;
			}

			.custom-cell-title {
				display: flex;

				.custom-cell-title-item {
					margin-left: 26rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
				}
			}
		}
	}
</style>