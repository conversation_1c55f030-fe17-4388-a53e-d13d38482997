<template>
	<view class="layout-sub-goodsSave">
		<view class="custom-body">
			<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="150rpx">
				<up-form-item label="代收款" prop="collectionDelivery" borderBottom="true">
          <up-input v-model="formData.collectionDelivery" mode="price" fontSize="15px" color="#000"
                    border="none" inputAlign="right" :adjustPosition="true" safe-area
                    cursor-spacing="0" type="digit">
            <template #suffix>
              <span>元</span>
            </template>
          </up-input>
				</up-form-item>
        <up-form-item label="收款人" prop="collectionName" borderBottom="true">
          <up-input v-model="formData.collectionName" border="none" inputAlign="right"></up-input>
        </up-form-item>
        <up-form-item label="收款行" prop="collectionBankName" borderBottom="true">
          <up-input v-model="formData.collectionBankName" border="none" inputAlign="right"></up-input>
        </up-form-item>
        <up-form-item label="卡号" prop="collectionCardNum" borderBottom="true">
          <up-input v-model="formData.collectionCardNum" border="none" inputAlign="right"></up-input>
        </up-form-item>

        <up-line color="#d6d7d9"></up-line>
			</up-form>


			<view class="custom-btn">
				<up-button type="primary" text="确定" @click="handleSubmit"></up-button>
			</view>

		</view>
	</view>
</template>

<script setup>
import {
	reactive,
	getCurrentInstance,
	ref,
	onMounted
} from 'vue'
import modal from '@/plugins/modal'

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const formData = ref({
  collectionDelivery: undefined,
  collectionName: undefined,
  collectionBankName: undefined,
  collectionCardNum: undefined,
})

// 确定事件
const handleSubmit = () => {
  if (formData.value.collectionDelivery && formData.value.collectionDelivery > 0) {
    // 填写了代收货款，则需填写收款人信息
    if (!formData.value.collectionName) {
      setTimeout(() => {
        modal.msgError("请输入收款人")
      }, 500)
      return false
    } else if (!formData.value.collectionBankName) {
      setTimeout(() => {
        modal.msgError("请输入收款行")
      }, 500)
      return false
    } else if (!formData.value.collectionCardNum) {
      setTimeout(() => {
        modal.msgError("请输入卡号")
      }, 500)
      return false
    }
  }
  // 父页面发送数据
  eventChannel.emit('collectionDeliveryChild', formData.value)
  uni.navigateBack()
}

onMounted(() => {
	// 接收数据
	eventChannel.on('collectionDeliveryParent', function (data) {
		console.log(data)
		formData.value.collectionDelivery = data.collectionDelivery
		formData.value.collectionName = data.collectionName
    formData.value.collectionBankName = data.collectionBankName
    formData.value.collectionCardNum = data.collectionCardNum
	})
})
</script>

<style scoped lang="scss">
.layout-sub-goodsSave {
	background-color: #f1f1f1;
	height: 100vh;
	overflow: auto;
	padding: 0 20rpx;

	.custom-body {
		background-color: #fff;
		padding: 0 20rpx;
		padding-bottom: 20rpx;
		margin: 20rpx 0;
		border-radius: 5px;

		:deep(.u-form-item__body__right__content__slot) {
			padding-right: 50rpx;
		}

		:deep(.u-number-box) {
			padding: 0 60rpx;
		}

		:deep(.u-number-box__input) {
			width: auto !important;
		}

		:deep(.u-form-item__body__left__content__label) {
			color: #606266;
		}

		.custom-body-form-row {
			.custom-body-form-item {
				display: flex;
				width: 100%;
				flex-direction: column;
				gap: 20rpx;

				.total-volume-item-input {
					width: 89%;
					height: 60rpx;
					margin-left: auto;
				}

				.custom-body-form-item-line {
					display: flex;

				}

				.custom-body-form-item-input {
					display: flex;
					flex-wrap: nowrap;
					gap: 5px;
					// margin-left: 30px;
					height: 30px;
				}
			}
		}

		.custom-btn {
			margin-top: 20rpx;

			:deep(.u-button--square) {
				border-radius: 20px;
			}

			:deep(.u-button--primary) {
				background-color: #d81e06;
				border: #d81e06;
			}

			:deep(.u-button) {
				height: 45px;
			}
		}

		// :deep(.u-scroll-list) {
		// 	width: 100%;
		// }

		.custom_line {
			display: flex;
			height: 60px;
			align-items: center;
			.custom-card-body-item-lable {
				font-size: 16px;
				color: #606266;
				width: 150rpx;
				// margin-right: 6rpx;
			}

			.custom-card-body-item-tag {
				// padding-left: 120rpx;
				width: 50px;
				height: 100%;
				display: flex;
				align-items: center;
        margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 18px;
				}
			}
		}


	}


	:deep(.u-scroll-list) {
		width: calc(100% - 220rpx);
		padding-bottom: 0;
	}


  :deep(.u-tag--mini) {
    height: 43px;
    line-height: 43px;
    padding: 0 15px;
    min-width: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.u-tag--primary) {
    background-color: #d81e06;
  }
}
</style>