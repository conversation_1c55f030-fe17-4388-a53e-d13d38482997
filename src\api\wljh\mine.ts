import request from '@/utils/request'
import {reactive} from "vue";
import customStorage from "@/utils/customStorage"
import useUserStore from "@/store/modules/user";


// 判断是否需要进行个人认证
export const getPrincipalAuditCheckResult = async () => {
	const accountInfo = customStorage.get("AccountInfo")
	console.log('accountInfo', accountInfo)
	let result = {
		showPrincipalAudit: false,
		principalAuditContent: '',
		principalAuditConfirmText: ''
	}
	// 网点个人认证状态
	const principalAuditStatus = '4'
	// 0-未提交审核 1-已审核 2-待审核 3-审核不通过
	if (!principalAuditStatus || principalAuditStatus === '0') {
		result.principalAuditContent = '请先进行个人认证，审核通过后可试用系统功能'
		result.principalAuditConfirmText = '去认证'
		result.showPrincipalAudit = true
	} else if (principalAuditStatus === '2') {
		result.principalAuditContent = '个人认证暂未审核通过，请联系经纪人或系统管理员进行审核'
		result.principalAuditConfirmText = '查看认证信息'
		result.showPrincipalAudit = true
	} else if (principalAuditStatus === '3') {
		result.principalAuditContent = '个人认证审核失败，请修改认证信息再次提交审核'
		result.principalAuditConfirmText = '修改认证信息'
		result.showPrincipalAudit = true
	} else {
		result.showPrincipalAudit = false
	}
	return result
}
// 跳转个人认证页面
export const handlePrincipalAuditApplyForm = () => {
	const userInfo = customStorage.get("AccountInfo")
	uni.navigateTo({
		url: '/page_mine/principalApprovalForm',
		success: function(res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('params', {
				data: {
					networkId: userInfo.logisticsNetworkId,
					accountId: userInfo.accountId,
				},
				type: 'edit'
			})
		}
	})
}
// 退出登录
export const handleLogout = () => {

	const userStore = useUserStore()
	userStore.logOut().then(res => {
		uni.reLaunch({
			url: '/page_mine/login'
		});
	})
}

// 查询个人信息
export const getUserInfo = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"account": "***********",
			"id": 2,
			"headPortrait": "https://hwscm.com.cn:9000/wljh-hongwei/7e3f14869fa65889b7c70dba4f0440b35028ff390fc22f2829de2599a61dea9b.jpg",
			"nickname": "安阳",
			"logisticsNetworkId": "1",
			"logisticsNetworkInfo": {
				"id": 1,
				"name": "安阳网点",
				"address": "河南省郑州市二七区",
				"organizationClass": "1",
				"organizationCode": "**********",
				"organizationType": "CRED_ORG_USCC",
				"organizationPhone": "***********",
				"organizationEmail": null,
				"organizationBankCardNum": null,
				"corporationName": null,
				"corporationPhone": null,
				"corporationEmail": null,
				"corporationIdentityNum": null,
				"corporationIdentityType": null,
				"principalName": "杨一一",
				"principalPhone": "***********",
				"principalEmail": null,
				"principalIdentityNum": "******************",
				"principalIdentityType": "CRED_PSN_CH_IDCARD",
				"networkRank": "1",
				"startStatus": "1",
				"certification": "1",
				"isAccount": null,
				"deptId": 121,
				"networkCode": "WD20240918001",
				"allowBrokerage": "1",
				"receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/7a91de64d43ec2260b77ff3792b24ec71ae569710ead8bff562203a3607d56b4.png",
				"receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/1ab2e52b0660151c925ef261a8620df351848a565b8466defbff13abd234af0f.png",
				"receiveThree": "https://dachisc.wang:9000/wljh-sanqian/bdac73eb40b5b82046788ea0974cb0500e2b421a862d40cc9800b2a245050f68.jpg",
				"receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/141e480512f20abd06a374d21e6409ccf0267257ced12fdd4925bb818d826da0.png",
				"receiveCodeAudit": "1",
				"brokerId": null,
				"principalAuditStatus": "1",
				"levelSort": 3,
				"accountPwdStatus": "1",
				"createTime": *************,
				"levelInfo": null
			},
			"levelInfo": {
				"id": 3,
				"objectType": "1",
				"levelCode": "V3",
				"levelSort": 3,
				"levelImg": "https://dachisc.wang:9000/wljh-sanqian/bf8f24f916d4c282b12b55098683e9f708d221969efb1cbd7acdb8ff97e5f502.svg",
				"levelName": "达标网点",
				"standardMonthDayAmount": 1000.00,
				"standardMonthTotalAmount": 0,
				"standardAddSignNum": 0,
				"rewardHonor": "V3晋级突破奖",
				"rewardMoney": 1000.00,
				"keepRewardHonor": "XX月度达标网点\nXX季度达标网点\nXX年度达标网点",
				"keepRewardMaterial": "打印设备包",
				"keepRewardMoney": 0,
				"keepRewardYear": null,
				"keepRewardStock": null,
				"openLimitAmount": 0,
				"createTime": *************
			},
			"accountList": [
				{
					"id": 26,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_FREIGHT_PRE",
					"overAllowAmount": 0.00,
					"totalAmount": 14879.00,
					"availableAmount": 14379.00,
					"freezeAmount": 500.00,
					"createTime": *************
				},
				{
					"id": 27,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_FREIGHT_DIVIDE",
					"overAllowAmount": 0.00,
					"totalAmount": 100.00,
					"availableAmount": 100.00,
					"freezeAmount": 0.00,
					"createTime": *************
				},
				{
					"id": 28,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_RED_PACKET",
					"overAllowAmount": 0.00,
					"totalAmount": 1100.00,
					"availableAmount": 1050.00,
					"freezeAmount": 50.00,
					"createTime": *************
				},
				{
					"id": 29,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_TAX",
					"overAllowAmount": 0.00,
					"totalAmount": 9.00,
					"availableAmount": 9.00,
					"freezeAmount": 0.00,
					"createTime": *************
				},
				{
					"id": 30,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_BROKERAGE",
					"overAllowAmount": 0.00,
					"totalAmount": 736.00,
					"availableAmount": 736.00,
					"freezeAmount": 0.00,
					"createTime": *************
				},
				{
					"id": 31,
					"accountRole": "2",
					"mainId": 1,
					"mainName": null,
					"accountType": "NETWORK_DEPOSIT",
					"overAllowAmount": 0.00,
					"totalAmount": 0.00,
					"availableAmount": 0.00,
					"freezeAmount": 0.00,
					"createTime": *************
				},
				{
					"id": *************,
					"accountRole": null,
					"mainId": null,
					"mainName": null,
					"accountType": "TOTAL",
					"overAllowAmount": 0.00,
					"totalAmount": 16824.00,
					"availableAmount": 16274.00,
					"freezeAmount": 550.00,
					"createTime": null
				}
			],
			"roleCodeSet": [
				"network_admin"
			],
			"openOuterNum": 6
		},
		"msg": ""
	})})
}

// 查询当前用户绑定的物流账号
export const getOuterInfo = (id : number) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [
			{
				"id": 14,
				"name": "百达物流",
				"address": null,
				"account": "admin",
				"organizationPhone": null,
				"openFlag": "1"
			},
			{
				"id": 1,
				"name": "远通物流",
				"address": "河南省郑州市新郑市",
				"account": "蚌埠",
				"organizationPhone": "***********",
				"openFlag": "1"
			},
			{
				"id": 4,
				"name": "浩运物流",
				"address": "河南省郑州市",
				"account": "杨进才",
				"organizationPhone": "***********",
				"openFlag": "1"
			},
			{
				"id": 9,
				"name": "晋南物流",
				"address": null,
				"account": "多多超市",
				"organizationPhone": null,
				"openFlag": "1"
			},
			{
				"id": 12,
				"name": "超达物流",
				"address": "河南省郑州市分拨中心",
				"account": "杨进才",
				"organizationPhone": null,
				"openFlag": "1"
			},
			{
				"id": 15,
				"name": "创新物流",
				"address": null,
				"account": "371557",
				"organizationPhone": null,
				"openFlag": "1"
			}
		],
		"msg": ""
	})})
}

// 更新个人信息
export const updateUserInfo = (data : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}
// 修改密码
export const updateUserPwd = (data : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 获得我的站内信分页
export const getMyNotifyMessagePage = (params : any) => {
	return request({
		url: '/system/notify-message/my-page',
		method: 'GET',
		params: params
	})
}
// 标记站内信为已读
export const updateNotifyMessageRead = (params : any) => {
	return request({
		url: '/system/notify-message/update-read',
		method: 'PUT',
		params: params
	})
}

