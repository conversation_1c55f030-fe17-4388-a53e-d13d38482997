<template>
  <!-- 输入联系方式后四位 -->
  <view v-show="!showDetail" class="phone-verify">
    <view class="phone-verify-box">
      <view class="phone-verify-box-header">
        <text style="color: #666aa1">{{ formData.logisticsCompaniesName }}</text>
        <text style="color: #818180; padding-left: 20rpx">{{ formData.orderCode }}</text>
        <view class="phone-verify-box-header-copy">
          <up-icon name="file-text" size="20" color="#606266" @click="copyContent(formData.orderCode)"></up-icon>
        </view>

      </view>
      <view class="phone-verify-box-content">
        <view class="phone-verify-box-content-txt">
          请输入寄件人或收件人联系方式后四位验证身份
        </view>
        <view class="phone-verify-box-content-input">
          <up-code-input v-model="verifyPhoneValue" mode="line" hairline size="45" :maxlength="4"
                         borderColor="#d81e06" @finish="verifyInputFinish"
          ></up-code-input>
        </view>
      </view>
    </view>
  </view>
  <!-- 订单详情 - 内有画布，不可隐藏 -->
  <view class="layout-task">
    <!--<view>
      <button ref="getPhoneRef" open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">授权并获取手机号</button>
    </view>-->
    <!-- 头部 -->
    <view class="layout-task-header">
      <!-- tab切换栏 -->
      <view class="custom-tabs-menu">
        <up-tabs :list="tabsList" lineColor="#d81e06" activeStyle="color:#000" :itemStyle="tabsItemStyle"
                 lineWidth="78" @click="handleTabsClick" scrollable></up-tabs>
      </view>
    </view>
    <!-- 内容 -->
    <view class="layout-task-body">
      <!-- 电子客户联 -->
      <view v-show="tabsSelected === 'info'" >
        <view class="custom-card" style="padding: 40rpx;display: flex;justify-content: center;">
          <canvas id="imgCanvas" type="2d" style="width:284px;height:378px;" />
          <view class="disapleBox">
            <w-qrcode :options="qrcodeOptions" ref="qrcodeRef" @generate="handleGenerateQr"></w-qrcode>
          </view>
        </view>
      </view>
      <!-- 运单状态-顶部 -->
      <view v-if="tabsSelected === 'track'" class="custom-card">
        <view class="custom-card-header">
          <up-row>
            <up-col :span="6" customStyle="text-align: center; border-right: 1px solid #f1f1f1;">
              <text>{{ statusLabel }}</text>
              <up-line color="#f1f1f1"></up-line>
              <text style="color: #606266; font-size: 13px">{{ statusTimeLabel }}时间：</text>
              <text style="font-size: 13px">{{ statusTimeStr }}</text>
            </up-col>
            <up-col :span="6" customStyle="text-align: center; ">
              <up-row customStyle="font-size:13px;line-height: normal;">
                <up-col :span="4.5" customStyle="text-align:center;color:#000000;font-weight: 500;">{{ formData.billDeptName }}</up-col>
                <up-col :span="3" customStyle="text-align:center;color:#ec818e">》》》</up-col>
                <up-col :span="4.5" customStyle="text-align:center;color:#000000;font-weight: 500;">{{ formData.discDeptName }}</up-col>
              </up-row>
            </up-col>
          </up-row>
        </view>
      </view>

      <!-- 运单状态-list -->
      <view v-if="tabsSelected === 'track'" class="custom-card trackView" style="padding: 20rpx;">
        <up-steps v-if="waybillTrackList.length > 0" :current="0" direction="column" activeColor="red">
          <up-steps-item v-for="(item, index) in waybillTrackList" :title="item.showTitle ? item.nodeName : ''" >
            <template #icon>
              <view v-if="item.showTitle" style="margin-top: 25rpx">
                <!-- 激活-已签收 -->
                <up-image v-if="index === 0 && item.nodeType === 'sign'" :src="signIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-运送中、派送中 -->
                <up-image v-if="index === 0 && (item.nodeType === 'transit' || item.nodeType === 'delivery')" :src="transitIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-已揽收 -->
                <up-image v-if="index === 0 && item.nodeType === 'collect'" :src="collectIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 激活-其他 -->
                <up-image v-if="index === 0 && !item.nodeType" :src="collectIconRed" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>

                <!-- 未激活-已签收 -->
                <up-image v-if="index !== 0 && item.nodeType === 'sign'" :src="signIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-运送中、派送中 -->
                <up-image v-if="index !== 0 && (item.nodeType === 'transit' || item.nodeType === 'delivery')" :src="transitIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-已揽收 -->
                <up-image v-if="index !== 0 && item.nodeType === 'collect'" :src="collectIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
                <!-- 未激活-其他 -->
                <up-image v-if="index !== 0 && !item.nodeType" :src="collectIconGray" bgColor="#ffffff" shape="circle" :fade="false" width="45rpx" height="45rpx"></up-image>
              </view>
              <view v-if="!item.showTitle">
                <span class="step-icon-dot">·</span>
              </view>
            </template>
            <template #desc>
              <view>
                <view :class="index===0?'step-content':'step-content step-content-text'">{{ formatDate(item.nodeTime, 'yyyy-MM-dd HH:mm:ss') }}</view>
                <span v-for="(item2, index2) in item.contentList"
                      :class="index===0&&item2.type==='text'?'step-content':'step-content step-content-'+item2.type"
                      @click=makeCall(item2)>
                  {{ item2.value }}
                </span>
              </view>
            </template>
          </up-steps-item>
        </up-steps>
        <view v-if="waybillTrackList.length <= 0" style="text-align: center;color: #606266;">暂无数据</view>
      </view>

    </view>
  </view>
</template>

<script setup>
import {onMounted, ref} from 'vue';
import {
  getDictDataInfoAll,
  getOrderPrintInfoApp, getWaybillDetailInfoApp,
  getWaybillTrackInfo,
  reachWaybillInfoApp
} from '@/api/wljh/task'
import {formatDate} from "@/api/common";
import modal from '@/plugins/modal'
import {onLoad} from "@dcloudio/uni-app";
import {regExp} from '@/utils/print/printTool'

  const showDetail = ref(true)

  const verifyPhoneValue = ref('')

  const qrcodeRef = ref(null)
  const qrcodeTemplatePath = ref()
  const qrcodeOptions = ref({ // 生成二维码的值
    code: '241127205',
    text: { //二维码绘制文字 非必传
      opacity: 1, //文字透明度 默认不透明1  0~1 非必传
      font: 'bold 20px system-ui', //文字是否加粗 默认normal 20px system-ui 非必传
      color: ["#000000"], // 文字颜色 多个颜色支持渐变色 默认黑色 非必传
      content: "" //文字内容
    },
    size: 110, //生成的二维码的宽高
    padding: 8,
    type: 'none',
    color: ['#000']
  })

  const canvasRef = ref(null)

  const sendIcon = ref('https://dachisc.wang:9000/wljh/7e266d83d806e0731c88466b136012484123919fbbb4f166200ad8de12cef6e8.png')
  const collIcon = ref('https://dachisc.wang:9000/wljh/f455372bdd7e2cc155cf0dc4c316e1aea78f8c2803e2462a2a75e7dc7bec32b8.png')
  const signIconRed = ref('https://dachisc.wang:9000/wljh/f455372bdd7e2cc155cf0dc4c316e1aea78f8c2803e2462a2a75e7dc7bec32b8.png')
  const signIconGray = ref('https://dachisc.wang:9000/wljh/82ed88c0a82ffd3456061626c3c267f26138e0f94ce90f45b190305c0dbf4ec7.png')
  const transitIconRed = ref('https://dachisc.wang:9000/wljh/a91062bd812e9122bbf26001c3bedc19847f312cbb58d4ac5364bbd6ff14b13a.png')
  const transitIconGray = ref('https://dachisc.wang:9000/wljh/d86c6d5e6bee09bb222130efd1ae73ebceb851c3de09e14b26cde943c90c07ee.png')
  const collectIconRed = ref('https://dachisc.wang:9000/wljh/2ec6dbeb56533074f7f97a9efe86179972660f02a3932d348f8a7744229bf391.png')
  const collectIconGray = ref('https://dachisc.wang:9000/wljh/41fc277a0b0392dedd4cb7609aeab69fd36460819defc010acaf55dc52d17ba3.png')

  const tabsSelected = ref('info') // tab选中Key
  const tabsList = ref([{ // tab切换栏菜单数据
    name: '电子客户联',
    badge: {
      value: 0
    },
    key: 'info'
  },
    {
      name: '运输状态',
      key: 'track'
    },
    // {
    //   name: '单据图片',
    //   key: 'pic'
    // }
  ])

	const formData = ref({ // 参数
		id: undefined,
		sendName: undefined,
		sendPhone: undefined,
		sendAddress: undefined,
		collectName: undefined,
		collectPhone: undefined,
		goodsType: undefined,
		totalWeight: undefined,
		totalVolume: undefined,
		totalNum: undefined,
		logisticsCompaniesId: undefined,
		payMethod: undefined,
		shouldPayAmount: undefined,
		actualPayAmount: undefined,
    amountFreight: undefined,
    totalAmount: 0,
		goodsTypeLabel: undefined,
		payMethodLabel: undefined,
		orderCode: undefined,
		waybillCode: undefined,
    billDeptName: undefined,
    discDeptName: undefined,
	})

  // 运单轨迹数据
  const waybillTrackList = ref({})
  const firstTrackType = ref('')
  const statusLabel = ref('')
  const statusTimeLabel = ref('')
  const statusTimeStr = ref('')

  // tabs 切换事件
  const handleTabsClick = async (item, index) => {
    tabsSelected.value = item.key
    if (item.key === 'info') { // 电子存根

    } else if (item.key === 'track') { // 运单状态

    } else if (item.key === 'pic') { // 单据图片
      modal.msgError('开发中')
    }
  }

	// 返回
	const handleBlack = () => {
		uni.navigateBack();
	}
	// 获取单个字典标签
	const getDictLable = async (dictType, value) => {
		await getDictDataInfoAll({
			'dictType': dictType,
			'value': value
		}).then(res => {
			if (res.code === 0) {
				switch (dictType) {
					case 'GOODS_TYPE':
						formData.value.goodsTypeLabel = res.data[0].label
						break;
					case 'PAY_METHOD':
						formData.value.payMethodLabel = res.data[0].label
						break;
				}
			}
		})
	}

// 复制到粘贴板
const copyContent = (value) => {
  uni.setClipboardData({
    data: value,
    success: function () {
      console.log('复制成功', value);
      modal.msgSuccess("已复制")
    }
  })
}

  const initPageData = (orderCode) => {
    // 获取订单详情数据
    getOrderPrintInfoApp(orderCode).then(res => {
      if (res.code === 0) {
        // 订单数据
        fillFormData(res.data)
        // 获取运单状态数据
        getWaybillTrackInfoFn(formData.value.waybillCode)
        // 在画布上绘制电子客户联
        handlePrintOrderFn1()

        if (formData.value.logisticsStatus !== '5') {
          console.log('未到签收状态-先同步状态')
          reachWaybillInfoApp({
            'waybillCode': res.data.waybillCode,
            'logisticsCompaniesId': res.data.logisticsCompaniesId
          })
          setTimeout(() => {
            // 5s后获取最新状态
            getOrderPrintInfoApp(orderCode).then(res => {
              if (res.code === 0) {
                // 刷新运单数据
                fillFormData(res.data)
                // 再次获取运单状态数据
                getWaybillTrackInfoFn(formData.value.waybillCode)
              }
            })
          }, 5000)
        }
      }
    })
  }

  // 填充表单数据
  const fillFormData = (data) => {
    formData.value = data
    formData.value.waybillCode = data.waybillCode
    formData.value.billDeptName = data.billDeptName
    formData.value.discDeptName = data.discDeptName
  }
  /**
   * 获取运单轨迹信息
   * @param waybillCode 运单号
   * @returns {Promise<void>}
   */
  const getWaybillTrackInfoFn = (waybillCode) => {
    getWaybillTrackInfo(waybillCode).then(res => {
      if (res.code === 0) {
        waybillTrackList.value = res.data
        if (res.data.length > 0) {
          firstTrackType.value = res.data[0].nodeType
          if (res.data[0].nodeName) {
            statusLabel.value = res.data[0].nodeName
          } else {
            statusLabel.value = '已开单'
          }
          switch (res.data[0].nodeType) {
            case 'collect':
              statusTimeLabel.value = '揽收'
              break;
            case 'transit':
              statusTimeLabel.value = '运送'
              break;
            case 'delivery':
              statusTimeLabel.value = '派送'
              break;
            case 'sign':
              statusTimeLabel.value = '签收'
              break;
            default:
              statusTimeLabel.value = '下单'
              break;
          }
          if (res.data[0].nodeTime) {
            statusTimeStr.value = formatDate(res.data[0].nodeTime, 'yyyy/MM/dd HH:mm')
          } else {
            statusTimeStr.value = formatDate(formData.value.waybillCreateTime, 'yyyy/MM/dd HH:mm')
          }
        } else {
          statusLabel.value = '已开单'
          statusTimeLabel.value = '开单'
          statusTimeStr.value = formatDate(formData.value.waybillCreateTime, 'yyyy/MM/dd HH:mm')
        }
      }
    })
  }

  const printValue = ref('LODOP.PRINT_INIT("");\n' +
      'LODOP.SET_PRINT_MODE("PRINT_NOCOLLATE",1);\n' +
      'LODOP.ADD_PRINT_RECT(1,1,271,21,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(21,1,271,38,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(21,122,60,38,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(40,1,271,19,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(59,1,271,83,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(96,1,271,19,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(142,1,271,163,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(142,122,60,37,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(160,1,271,19,0,1);\n' +
      'LODOP.ADD_PRINT_TEXT(2,115,55,15,"客户联");\n' +
      'LODOP.ADD_PRINT_TEXT(-5,197,80,10,"*nowDate*");\n' +
      'LODOP.ADD_PRINT_TEXT(5,197,80,10,"*nowTime*");\n' +
      'LODOP.ADD_PRINT_TEXT(5,235,80,10,"*printCount*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",10);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(20,3,33,12,"单号");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(20,58,60,12,"*orderCode*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(20,138,35,12,"时间");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(20,187,80,12,"*dateTime02*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(41,3,33,12,"发站");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(41,58,63,14,"*billDeptName*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(41,138,29,14,"到站");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(41,184,84,14,"*receiveNetworkName*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(58,2,42,14,"目的地");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(58,58,209,14,"*destination*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(77,3,39,14,"发货人");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(77,58,55,14,"*sendName*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(77,138,28,14,"电话");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(95,138,25,14,"电话");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(77,185,84,14,"*sendPhone*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(95,185,84,14,"*collectPhone*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(95,3,44,14,"收货人");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(95,58,55,14,"*collectName*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(120,3,50,14,"收货地址");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(120,58,208,15,"*collectAddress*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(141,2,37,14,"货名");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(141,58,57,14,"*goodsType*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(141,124,57,14,"包装/件数");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(141,186,81,14,"*packMethodAndNum*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(160,2,50,14,"送货方式");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(160,58,57,14,"*deliveryMethod*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(160,124,57,14,"重量/体积");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(160,187,80,14,"*weightAndVolume*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_RECT(216,182,1,88,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalX",0.5);\n' +
      'LODOP.ADD_PRINT_RECT(22,55,1,283,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalX",0.2);\n' +
      'LODOP.ADD_PRINT_RECT(77,122,60,38,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(77,1,271,1,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalY",0.5);\n' +
      'LODOP.ADD_PRINT_RECT(197,1,271,19,0,1);\n' +
      'LODOP.ADD_PRINT_RECT(180,182,1,17,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalX",0.5);\n' +
      'LODOP.ADD_PRINT_RECT(180,222,1,36,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalX",0.5);\n' +
      'LODOP.ADD_PRINT_RECT(197,122,1,19,0,1);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"ScalX",0.5);\n' +
      'LODOP.ADD_PRINT_RECT(240,1,182,22,0,1);\n' +
      'LODOP.ADD_PRINT_TEXT(178,2,50,14,"控货方式");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(178,58,118,14,"*releaseMethod*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(178,188,30,14,"回单");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(178,231,34,14,"*isReceipt*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(197,2,50,14,"费用合计");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(197,58,57,14,"*totalAmount*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(197,149,50,14,"付款方式");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(197,227,36,14,"*payMethod*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(220,2,52,14,"代收货款");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(220,58,114,14,"*collectionDelivery*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(242,2,52,14,"到付合计");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(242,58,138,14,"*payTotal*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.SET_PRINT_STYLEA(0,"Bold",1);\n' +
      'LODOP.ADD_PRINT_TEXT(268,2,33,14,"备注");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(258,58,120,31,"*remark1*");\n' +
      'LODOP.ADD_PRINT_TEXT(270,58,120,31,"*remark2*");\n' +
      'LODOP.ADD_PRINT_TEXT(282,58,120,31,"*remark3*");\n' +
      'LODOP.ADD_PRINT_TEXT(294,58,180,31,"*remark4*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(223,186,80,74,"QRCode");\n' +
      'LODOP.ADD_PRINT_TEXT(305,6,172,14,"*logisticsNetworkAddress*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n' +
      'LODOP.ADD_PRINT_TEXT(305,182,82,14,"*logisticsNetworkPhone*");\n' +
      'LODOP.SET_PRINT_STYLEA(0,"FontSize",9);\n')

  // 绘制订单信息
  const handlePrintOrderFn1 = async () => {
    // modal.msgSuccess("暂未设计该打印模板！")
    const orderInfo = formData.value
    // 填充模板数据
    let printContent = fillTemplateData(printValue.value, orderInfo)

    // console.log(printContent)
    let printContent_split = printContent.split(";")
    // 绘制面单并打印
    createCanvasAndPrint(formData.value.orderCode, printContent_split)

  }

  /**
   * 填充模板数据
   * @param resPrintContent 模板数据
   * @param templateContent 业务数据
   */
  function fillTemplateData(resPrintContent, templateContent) {
    let printContent = resPrintContent
    // 替换数据
    if (printContent.match(/\*[0-9A-Za-z]+\*/g)) {
      for (let n = printContent.match(/\*[0-9A-Za-z]+\*/g).map(function (e) {
            return e.substring(1, e.length - 1)
          }),
               r = 0; r < n.length; r++) {
        let o = n[r]
        printContent = templateContent[o] || 0 === templateContent[o] ? printContent.replace(
                '*' + o + '*',
                templateContent[o]
                    .toString()
                    .replace(
                        /[\r|\n|\||\!|\@|\#|\$|\%|\^|\&|"|"|'|'|\,|\;|\~|\`|\，|\‘|\’|\“|\”|\。|\？|\！]/g,
                        ' '
                    )
            ) :
            printContent.replace('*' + o + '*', '')
      }
    }
    return printContent
  }

  /**
   * 绘制面单并打印
   * 该部分代码copy自 handlePrintWaybillFn1 -> wx.createSelectorQuery
   * @param billCode 订单号/运单号
   * @param apiChannel 渠道
   * @param printContent_split 打印模板数据
   */
  function createCanvasAndPrint(billCode, printContent_split) {
    //创建面单画布
    wx.createSelectorQuery()
        .select('#imgCanvas')
        .fields({
          node: true,
          size: true
        })
        .exec((res) => {
          // Canvas 对象
          const canvas = res[0].node
          // Canvas 画布的实际绘制宽高
          const canvasWidth = 272
          const canvasHeight = 370
          // 绘制上下文
          const ctx = canvas.getContext('2d')
          // 初始化画布大小
          const dpr = wx.getWindowInfo().pixelRatio
          canvas.width = canvasWidth * 2
          canvas.height = canvasHeight * 2
          ctx.scale(2, 2)
          // 清空画布
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.fillStyle = "#ffffff"
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          // 绘制
          let isRest = false
          let isFontSize = false
          let styleaFont = "12px Microsoft YaHei"
          let styleaTextAlign = "center"
          let styleaFillStyle = "#000000"
          for (let i = printContent_split.length; i >= 0; i--) {
            let regExpData = regExp(printContent_split[i])
            //console.log(regExpData)
            if (regExpData) {
              if (regExpData.key === 'shape') {
                ctx.styleaFillStyle = '#000000'
                ctx.fillRect(
                    parseInt(regExpData.data[2]),
                    parseInt(regExpData.data[1]),
                    parseInt(regExpData.data[3]),
                    parseInt(regExpData.data[4]))
              } else if (regExpData.key === 'rect') {
                ctx.strokeStyle = 'black'
                ctx.strokeRect(
                    parseInt(regExpData.data[1]),
                    parseInt(regExpData.data[0]),
                    parseInt(regExpData.data[2]),
                    parseInt(regExpData.data[3]))
                isRest = true
              } else if (regExpData.key === 'text') {
                let text = regExpData.data[4].split('"').join('')
                if (text === "QRCode") {
                  //console.log(obj.orderCode)
                  getQrcode(formData.value.qrcode, parseInt(regExpData.data[2]))
                  handleGenerateQr()
                  setTimeout(() => {
                    // console.log("二维码地址：", qrcodeTemplatePath.value)
                    if (qrcodeTemplatePath.value) {
                      const image = canvas.createImage()
                      image.onload = () => {
                        ctx.drawImage(
                            image,
                            parseInt(regExpData.data[1]) + 5,
                            parseInt(regExpData.data[0]) + 5,
                            parseInt(regExpData.data[2]) - 10,
                            parseInt(regExpData.data[2]) - 10,
                        )
                      }
                      image.src = qrcodeTemplatePath.value
                    }
                  }, 1000)
                } else {
                  if (!isFontSize) {
                    styleaFont += "12px Microsoft YaHei"
                  }
                  ctx.fillStyle = styleaFillStyle
                  ctx.font = styleaFont
                  ctx.fillText(text,
                      parseInt(regExpData.data[1]),
                      parseInt(regExpData.data[0]) + 15,
                      parseInt(regExpData.data[2]))
                  isRest = true
                }
              } else if (regExpData.key === 'stylea') {
                if (regExpData.data[1] === '"Alignment"') {
                  styleaTextAlign = 'center'
                } else if (regExpData.data[1] === '"Bold"') {
                  styleaFont = 'bold '
                } else if (regExpData.data[1] === '"FontSize"') {
                  styleaFont += (parseInt(regExpData.data[2]) + 3) + 'px Microsoft YaHei'
                  isFontSize = true
                } else if (regExpData.data[1] === '"FontColor"') {
                  styleaFillStyle = regExpData.data[2].split('"').join('')
                }
              }

              if (isRest) {
                isRest = false
                isFontSize = false
                styleaFont = "12px Microsoft YaHei"
                styleaTextAlign = "center"
                styleaFillStyle = "#000000"
              }
            }
          }
        })
  }
  // 制作二维码
  const getQrcode = (code, size) => {
    qrcodeOptions.value.code = code
    qrcodeOptions.value.size = size
  }
  const handleGenerateQr = async () => {
    await qrcodeRef.value.GetCodeImg().then(res => {
      qrcodeTemplatePath.value = res.tempFilePath
    })
  }


  // 联系方式后四位输入完成
  const verifyInputFinish = (value) => {
    console.log('verifyInputFinish', value)
    if (formData.value.sendPhone && value === formData.value.sendPhone.slice(-4)) {
      // 判断是否发件人手机号后四位
      showDetail.value = true
      modal.msgSuccess('验证成功')
      uni.hideKeyboard()
    } else if (formData.value.collectPhone && value === formData.value.collectPhone.slice(-4)) {
      // 判断是否收件人手机号后四位
      showDetail.value = true
      modal.msgSuccess('验证成功')
      uni.hideKeyboard()
    } else {
      modal.msgError('验证失败，请重新输入')
    }
  }

  // 点击手机号拨打电话
  const makeCall = (phoneContent) => {
    if (phoneContent.type === 'phone') {
      console.log('拨打电话', phoneContent.value)
      uni.makePhoneCall({
        phoneNumber: phoneContent.value
      })
    }
  }

  onLoad((option)=>{
    console.log('订单详情onLoad', option)
    // 页面跳转时传进来的订单号
    let orderCode = option.orderCode
    if (!orderCode) {
      // 扫描二维码进入时 option如下
      // {q: "https%3A%2F%2Fhwscm.com.cn%2Forderdetail%3ForderCode%3D241226006", scancode_time: "1735538022"}
      let uri = decodeURIComponent(option.q)
      orderCode = uri.split('orderCode=')[1]
      // 需要先输入联系方式后四位再展示订单详情
      showDetail.value = false
    }
    console.log('订单号', orderCode)
    qrcodeRef.value = null
    qrcodeTemplatePath.value = undefined
    initPageData(orderCode)
  })

  onMounted(() => {

  })


/*const getPhoneRef = ref()
  // 获取微信手机号
 const onGetPhoneNumber = async (e) => {
  console.log('onGetPhoneNumber', e.detail)
  // console.log(e.detail.code)  // 动态令牌
  // console.log(e.detail.errMsg) // 回调信息（成功失败都会返回）
  // console.log(e.detail.errno)  // 错误码（失败时返回）
  if (e.detail.code) {
    try {
      const accessToken = await getAccessToken();
      uni.request({
        method: 'POST',
        timeout: 10000,
        url: 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=' + accessToken,
        data: {
          code: e.detail.code
        },
        dataType: 'json'
      }).then(resp2 => {
        console.log(resp2)
        console.log(resp2.phone_info.phoneNumber)
        console.log(resp2.phone_info.purePhoneNumber)
      })
    } catch (e) {
      console.log('获取微信手机号' + e)
    }
  } else {
    modal.msgError('获取用户信息失败')
  }
}
// 获取AccessToken
const getAccessToken = async() => {
  const config = {
    appid: 'wxc8b13951b416ade9',
    secret: '8fe4ba7eb57ef04abc8164b3478280cf'
  }
  try {
    uni.request({
      method: 'GET',
      timeout: 10000,
      url: 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' + config.appid + '&secret=' + config.secret
    }).then(resp => {
      return resp.access_token
    })
  } catch (e) {
    console.log('获取AccessToken' + e)
    throw e
  }
}*/


</script>

<style scoped lang="scss">
	.layout-sub-orderDetails {
		background-color: #f1f1f1;
		// height: 100vh;
		height: 100vh;
		overflow: auto;
		padding: 0 20rpx;

		.custom-body {
			background-color: #fff;
			padding: 0 20rpx;
			padding-bottom: 20rpx;
			margin: 20rpx 0;
			border-radius: 5px;

			.custom-btn {
				margin-top: 20rpx;

				:deep(.u-button--square) {
					border-radius: 20px;
				}

				:deep(.u-button--primary) {
					background-color: #d81e06;
					border: #d81e06;
				}

				:deep(.u-button) {
					height: 45px;
				}
			}

			:deep(.u-form-item__body__left) {
				margin-right: 10rpx;
			}

			:deep(.u-input) {
				margin-right: 5px;
			}

			:deep(.u-form-item__body__left__content__label) {
				color: #606266;
			}
		}
	}
  .layout-task {
    background-color: #f1f1f1;
    // height: 100vh;
    height: calc(100vh - var(--window-bottom) - var(--window-top));
    //overflow: auto;

    .layout-task-header {
      background-color: #fff;
      height: 100rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .custom-tabs-menu {
        width: 500rpx;
      }

      .custom-tabs-btn {
        padding: 0 20rpx;

        :deep(.u-button--plain.u-button--info) {
          height: 60rpx;
          border-radius: 20px;
          padding: 0 40rpx;
          color: #606266;
        }
      }
    }


    .layout-task-body {
      padding: 0 10px 10px;
      height: calc(100% - 210rpx);
      overflow: auto;

      .custom-card {
        border-radius: 5px;
        font-size: 15px;
        background-color: #fff;
        margin-bottom: 20rpx;
        position: relative;
        margin: 20rpx 0;

        .custom-card-header {
          //display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #f1f1f1;
          height: 120rpx;
          line-height: 60rpx;
          //padding: 0 20rpx;
          border-top-left-radius: 5px;
          border-top-right-radius: 5px;

        }

        .custom-card-body {
          padding: 0 20rpx;
          border-bottom: 1px solid #f1f1f1;
        }

        :deep(.u-text__value) {
          font-size: 15px!important;
        }


      }

    }
  }
  .trackView {
    :deep(.u-steps-item__content) {
      margin-left: 25rpx!important;
    }
  }
  .color-gray {
    color: #8a8a8a;
  }
  .color-gray2 {
    color: #606266;
  }

  .step-icon-text {
    width: 21px;
    height: 21px;
    background-color: red;
    border-radius: 100px;
    font-size: 12px;
    color: #fff;
    line-height: 21px;
    text-align: center;
  }
  .step-icon-dot {
    //width: 21px;
    //height: 21px;
    border-radius: 100px;
    font-size: 35px;
    color: #6a6a6a;
    background-color: rgb(255 255 255 / 0%);
    //line-height: 21px;
    text-align: center;
  }
  .disapleBox {
    //display: flex;
    position: absolute;
    top: -1000px;
    left: -200;
  }
  .phone-verify {
    position: absolute;
    height: 100vh;
    width: 100vw;
    z-index: 5;
    //background-color: #ededed;
    background-image: linear-gradient(to bottom, #d81e06, #ededed, #ededed, #ededed);
    padding: 10vh 20rpx 0 20rpx;

    .phone-verify-box {
      height: 35vh;
      border-radius: 5px;
      background-color: #ffffff;
      padding: 20rpx;

      .phone-verify-box-header {

        .phone-verify-box-header-copy {
          display: inline-block;
          position: relative;
          top: 4rpx;
        }

      }

      .phone-verify-box-content {
        padding: 80rpx;

        .phone-verify-box-content-txt {
          text-align: center;
        }
        .phone-verify-box-content-input {
          padding-top: 40rpx;
          display: flex;
          justify-content: center;
        }

      }


    }
  }
  .step-content {
    font-size: 13px!important;
    margin-top: 10rpx;
  }
  .step-content-text {
    color: #606266!important;
  }
  .step-content-phone {
    color: #0081ff!important;
  }
</style>