import request from '@/utils/request'

// 分页获取通知列表
export const getNoticePage = (params : any) => {
	return request({
		url: '/system/notice/page',
		method: 'GET',
		params: params
	})
}

// 获得物流公司信息
export const getLogisticsCompaniesInfo = () => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"partnersTrue": [
				{
					"id": 14,
					"name": "百达物流",
					"address": null,
					"organizationCode": null,
					"organizationType": null,
					"organizationPhone": null,
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": null,
					"corporationIdentityType": null,
					"principalName": "张三",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "BD",
					"groupId": null,
					"comId": "117",
					"comName": "测试网点",
					"apiUsername": "admin",
					"apiPassword": "admin123",
					"needSendIdCard": "0",
					"qrcodePre": "",
					"allowBrokerage": "0",
					"receiveOne": null,
					"receiveTwo": null,
					"receiveThree": null,
					"receiveFive": null,
					"receiveCodeAudit": null,
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"rateFhkhwhf": null,
					"expressionFhkhwhf": null,
					"expressionFreight": null,
					"expressionDelivery": null,
					"collectionProcRate": 3.00,
					"collectionProcMin": null,
					"collectionProcMax": null,
					"receiptAmount": 10.00,
					"releaseAmount": null,
					"allowOtherAmount": "0",
					"allowDiscountAmount": "0"
				},
				{
					"id": 1,
					"name": "远通物流",
					"address": "河南省郑州市新郑市",
					"organizationCode": "**********",
					"organizationType": "CRED_ORG_USCC",
					"organizationPhone": "***********",
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": null,
					"corporationIdentityType": null,
					"principalName": "张三",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "SN",
					"groupId": null,
					"comId": "000846",
					"comName": null,
					"apiUsername": "蚌埠",
					"apiPassword": "123456",
					"needSendIdCard": "0",
					"qrcodePre": "",
					"allowBrokerage": "1",
					"receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
					"receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/61cd684eb2564fe618f6ae38a04e80da67b5b2274b54b9ebd4513aea40405962.jpg",
					"receiveThree": "https://hwscm.com.cn:9000/wljh-hongwei/8c28ee14fcbd68caef50448a096bc7cb645992be324a0ca69487624809324322.jpeg",
					"receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/141e480512f20abd06a374d21e6409ccf0267257ced12fdd4925bb818d826da0.png",
					"receiveCodeAudit": "2",
					"amountZdf": 1.00,
					"rateBzf": 1.00,
					"rateFhkhwhf": null,
					"expressionFhkhwhf": null,
					"expressionFreight": null,
					"expressionDelivery": null,
					"collectionProcRate": 3.00,
					"collectionProcMin": 1.00,
					"collectionProcMax": 50.00,
					"receiptAmount": 0.00,
					"releaseAmount": 10.00,
					"allowOtherAmount": "0",
					"allowDiscountAmount": "1"
				},
				{
					"id": 4,
					"name": "浩运物流",
					"address": "河南省郑州市",
					"organizationCode": "*********",
					"organizationType": "CRED_ORG_USCC",
					"organizationPhone": "***********",
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": "",
					"corporationIdentityType": null,
					"principalName": "测试",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "SN",
					"groupId": null,
					"comId": "000570",
					"comName": null,
					"apiUsername": "杨进才",
					"apiPassword": "yjc121718",
					"needSendIdCard": "1",
					"qrcodePre": "https://www.zbhy56.cn/applet/?orderNo=",
					"allowBrokerage": "1",
					"receiveOne": null,
					"receiveTwo": null,
					"receiveThree": null,
					"receiveFive": null,
					"receiveCodeAudit": null,
					"amountZdf": 0.00,
					"rateBzf": 3.00,
					"rateFhkhwhf": null,
					"expressionFhkhwhf": null,
					"expressionFreight": null,
					"expressionDelivery": null,
					"collectionProcRate": 3.00,
					"collectionProcMin": 5.00,
					"collectionProcMax": 100.00,
					"receiptAmount": null,
					"releaseAmount": null,
					"allowOtherAmount": "0",
					"allowDiscountAmount": "1"
				},
				{
					"id": 9,
					"name": "晋南物流",
					"address": null,
					"organizationCode": null,
					"organizationType": null,
					"organizationPhone": null,
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": null,
					"corporationIdentityType": null,
					"principalName": "测试",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "SN",
					"groupId": null,
					"comId": "000315",
					"comName": null,
					"apiUsername": "多多超市",
					"apiPassword": "yjc121718",
					"needSendIdCard": "0",
					"qrcodePre": "",
					"allowBrokerage": "1",
					"receiveOne": null,
					"receiveTwo": null,
					"receiveThree": null,
					"receiveFive": null,
					"receiveCodeAudit": null,
					"amountZdf": 0.00,
					"rateBzf": 2.00,
					"rateFhkhwhf": 0.00,
					"expressionFhkhwhf": "",
					"expressionFreight": null,
					"expressionDelivery": null,
					"collectionProcRate": null,
					"collectionProcMin": null,
					"collectionProcMax": null,
					"receiptAmount": null,
					"releaseAmount": null,
					"allowOtherAmount": "0",
					"allowDiscountAmount": "1"
				},
				{
					"id": 12,
					"name": "超达物流",
					"address": "河南省郑州市分拨中心",
					"organizationCode": null,
					"organizationType": null,
					"organizationPhone": null,
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": null,
					"corporationIdentityType": null,
					"principalName": "张三",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "CMM",
					"groupId": "3837",
					"comId": "222597",
					"comName": null,
					"apiUsername": "杨进才",
					"apiPassword": "z123456",
					"needSendIdCard": "0",
					"qrcodePre": "",
					"allowBrokerage": "1",
					"receiveOne": null,
					"receiveTwo": null,
					"receiveThree": null,
					"receiveFive": null,
					"receiveCodeAudit": null,
					"amountZdf": 0.00,
					"rateBzf": 10.00,
					"rateFhkhwhf": 9.00,
					"expressionFhkhwhf": "(#totalAmount-#brokerageAmount) * #rateFhkhwhf / 100",
					"expressionFreight": "#totalAmount",
					"expressionDelivery": "0",
					"collectionProcRate": 3.00,
					"collectionProcMin": 5.00,
					"collectionProcMax": 100.00,
					"receiptAmount": null,
					"releaseAmount": null,
					"allowOtherAmount": "1",
					"allowDiscountAmount": "0"
				},
				{
					"id": 15,
					"name": "创新物流",
					"address": null,
					"organizationCode": null,
					"organizationType": null,
					"organizationPhone": null,
					"organizationEmail": null,
					"organizationBankCardNum": null,
					"corporationName": null,
					"corporationPhone": null,
					"corporationEmail": null,
					"corporationIdentityNum": null,
					"corporationIdentityType": null,
					"principalName": "张三",
					"principalPhone": "***********",
					"principalEmail": null,
					"principalIdentityNum": "******************",
					"principalIdentityType": "CRED_PSN_CH_IDCARD",
					"networkRank": "1",
					"startStatus": "1",
					"partners": "1",
					"createTime": *************,
					"optionDisabled": null,
					"apiChannel": "SN",
					"groupId": null,
					"comId": "**********",
					"comName": "多多揽货",
					"apiUsername": "371557",
					"apiPassword": "yjc121718",
					"needSendIdCard": "0",
					"qrcodePre": "",
					"allowBrokerage": "0",
					"receiveOne": "https://hwscm.com.cn:9000/wljh-hongwei/6aed916ce91417282ff1438817e295013a86e1008ee73c9060be506dc92f220c.jpg",
					"receiveTwo": "https://hwscm.com.cn:9000/wljh-hongwei/e80d395f64398dda0d024d57b35344e6a360bc330a0f7f625e8d7acc7a5570f3.jpg",
					"receiveThree": "https://hwscm.com.cn:9000/wljh-hongwei/61cd684eb2564fe618f6ae38a04e80da67b5b2274b54b9ebd4513aea40405962.jpg",
					"receiveFive": "https://hwscm.com.cn:9000/wljh-hongwei/7a22f0866d6ebfd3dd9736021e8fa8ca3ebe91a2f3011a079c3bb1e9ffa8d74c.png",
					"receiveCodeAudit": null,
					"amountZdf": null,
					"rateBzf": 1.00,
					"rateFhkhwhf": null,
					"expressionFhkhwhf": null,
					"expressionFreight": null,
					"expressionDelivery": null,
					"collectionProcRate": null,
					"collectionProcMin": null,
					"collectionProcMax": null,
					"receiptAmount": null,
					"releaseAmount": null,
					"allowOtherAmount": "0",
					"allowDiscountAmount": "0"
				}
			],
			"partnersFalse": []
		},
		"msg": ""
	})})
}

// 获取客户列表
export const getCustomerListApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [
			{
				"pageNo": 1,
				"pageSize": 10,
				"id": 16,
				"name": "演示线索",
				"followUpStatus": true,
				"contactLastTime": [
					1708760561000
				],
				"contactLastContent": "111",
				"contactNextTime": [
					1708272000000
				],
				"ownerUserId": 1,
				"ownerTime": [
					1708694174000
				],
				"lockStatus": false,
				"dealStatus": false,
				"mobile": "13577788446",
				"telephone": null,
				"idCard": null,
				"qq": null,
				"wechat": null,
				"email": null,
				"areaId": null,
				"detailAddress": "幸福小区6栋501",
				"industryId": null,
				"level": null,
				"source": null,
				"remark": null,
				"createTime": [
					1708694174000
				],
				"addressOne": "河南省",
				"addressTwo": "郑州市",
				"addressThree": "金水区",
				"addressFour": "大石桥街道办事处",
				"addressFive": "金沙社区居民委员会"
			},
			{
				"pageNo": 1,
				"pageSize": 10,
				"id": 17,
				"name": "演示客户",
				"followUpStatus": false,
				"contactLastTime": null,
				"contactLastContent": null,
				"contactNextTime": null,
				"ownerUserId": null,
				"ownerTime": [
					1708441517000
				],
				"lockStatus": false,
				"dealStatus": false,
				"mobile": "18818260203",
				"telephone": "18818260223",
				"idCard": "******************",
				"qq": null,
				"wechat": null,
				"email": "<EMAIL>",
				"areaId": null,
				"detailAddress": "河南省郑州市",
				"industryId": null,
				"level": null,
				"source": 1,
				"remark": null,
				"createTime": [
					1708697093000
				],
				"addressOne": null,
				"addressTwo": null,
				"addressThree": null,
				"addressFour": null,
				"addressFive": null
			},
			{
				"pageNo": 1,
				"pageSize": 10,
				"id": 18,
				"name": "测试客户",
				"followUpStatus": false,
				"contactLastTime": null,
				"contactLastContent": null,
				"contactNextTime": null,
				"ownerUserId": null,
				"ownerTime": null,
				"lockStatus": false,
				"dealStatus": false,
				"mobile": "***********",
				"telephone": null,
				"idCard": null,
				"qq": null,
				"wechat": null,
				"email": null,
				"areaId": null,
				"detailAddress": "河南省郑州市二七区",
				"industryId": 2,
				"level": 2,
				"source": 9,
				"remark": null,
				"createTime": [
					1726709813000
				],
				"addressOne": null,
				"addressTwo": null,
				"addressThree": null,
				"addressFour": null,
				"addressFive": null
			}
		],
		"msg": ""
	})})
}

// 客户信息添加或编辑
export const saveOrEditCustomerInfoApp = (data : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 删除客户信息
export const deleteCustomerInfoApp = (id : number) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}

// 下订单
export const saveOrderInfoApp = (data : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"msg": "操作成功!",
			"code": "200",
			"success": true,
			"rows": "CA47E0C7C7EA4E7882797C92425CB363",
			"wljhOrderInfo": {
				"id": null,
				"orderCode": "D25032500003",
				"collectName": "金酷",
				"collectPhone": "18818260203",
				"collectAddress": "安",
				"sendName": "张爱因",
				"sendPhone": "***********",
				"sendIdCard": null,
				"sendAddress": "河南省郑州市二七区花园口",
				"sendAddressOne": null,
				"sendAddressTwo": null,
				"sendAddressThree": null,
				"sendAddressFour": null,
				"sendAddressFive": null,
				"collectAddressOne": null,
				"collectAddressTwo": null,
				"collectAddressThree": null,
				"collectAddressFour": null,
				"collectAddressFive": null,
				"goodsType": "GOODS_TYPE_OTHER",
				"totalWeight": 1.0,
				"totalVolume": 1.0,
				"totalNum": 2,
				"goodsLong": null,
				"goodsWidth": null,
				"goodsHeight": null,
				"payMethod": "PAY_METHOD_SEND_PAY_SETTLE",
				"shouldPayAmount": 320.0,
				"actualPayAmount": 320.0,
				"logisticsCompaniesId": 1,
				"logisticsNetworkId": 1,
				"orderStatus": "1",
				"logisticsStatus": "0",
				"orderStartTime": *************,
				"orderEndTime": null,
				"isReceipt": "0",
				"deliveryMethod": "1",
				"releaseMethod": "2",
				"packMethod": "2",
				"collectionDelivery": 0.0,
				"collectionName": null,
				"collectionBankName": null,
				"collectionCardNum": null,
				"amountFreight": 309.00,
				"totalAmount": 320.00,
				"remark": "测试app到站4444",
				"insuredAmount": 0.00,
				"waybillCode": "D25032500003",
				"networkName": null,
				"goodsTypeLabel": "食品饮料",
				"deptId": null,
				"amountTransfer": null,
				"amountBzf": "0.00",
				"standardShf": null,
				"deliveryAmount": 0.00,
				"amountJhf": null,
				"amountYj": null,
				"amountDfhk": null,
				"amountZdf": "1.00",
				"amountHdf": null,
				"amountKhf": null,
				"amountOther": null,
				"itemPkg": "纸",
				"rateBzf": 1.00,
				"rateFhkhwhf": null,
				"receiptType": "1",
				"receiptAmount": 0.00,
				"releaseAmount": 0.00,
				"insuredPayAmount": 0.00,
				"brokerageAmount": 10.00,
				"brokerageBackType": "1",
				"otherAmount": 0.00,
				"discountAmount": 0.00,
				"collectionFreightAmount": 0.00,
				"collectionPayAmount": 0.00,
				"taxAmount": 0.00,
				"comId": "000846",
				"comName": null,
				"discDeptId": "000036",
				"discDeptName": "安阳"
			}
		},
		"msg": ""
	})})
}


// 常发订单列表
export const getOrderConstantListApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [
			{
				"id": 149,
				"userId": null,
				"collectName": "张三",
				"collectPhone": "18530856666",
				"collectAddress": "河南省安阳市",
				"collectAddressOne": "河南省",
				"collectAddressTwo": "安阳市",
				"collectAddressThree": "文峰区",
				"collectAddressFour": "",
				"collectAddressFive": "乔家巷社区居委会",
				"sendName": "孙小孔",
				"sendPhone": "***********",
				"sendIdCard": null,
				"sendAddress": "河南省郑州市二七区",
				"sendAddressOne": "河南省",
				"sendAddressTwo": "郑州市",
				"sendAddressThree": "二七区",
				"sendAddressFour": "",
				"sendAddressFive": null,
				"goodsType": "GOODS_TYPE_OTHER",
				"goodsTypeLabel": "测试货品",
				"totalWeight": 3.0,
				"totalVolume": 3.0,
				"totalNum": 3,
				"goodsLong": 2,
				"goodsWidth": 100,
				"goodsHeight": 21,
				"packMethod": "3",
				"createTime": 1736580810000,
				"goodsShowLabel": "测试货品，3.0kg，3.0m³"
			},
			{
				"id": 159,
				"userId": null,
				"collectName": "张三",
				"collectPhone": "18530856666",
				"collectAddress": "河南省安阳市",
				"collectAddressOne": "河南省",
				"collectAddressTwo": "安阳市",
				"collectAddressThree": "",
				"collectAddressFour": null,
				"collectAddressFive": null,
				"sendName": "安阳",
				"sendPhone": "***********",
				"sendIdCard": null,
				"sendAddress": "河南省郑州市二七区",
				"sendAddressOne": "",
				"sendAddressTwo": null,
				"sendAddressThree": null,
				"sendAddressFour": null,
				"sendAddressFive": null,
				"goodsType": "GOODS_TYPE_OTHER",
				"goodsTypeLabel": "食品饮料",
				"totalWeight": 1.0,
				"totalVolume": 1.0,
				"totalNum": 1,
				"goodsLong": 2,
				"goodsWidth": 33,
				"goodsHeight": 100,
				"packMethod": "1",
				"createTime": 1736988762000,
				"goodsShowLabel": "食品饮料，1.0kg，1.0m³"
			}
		],
		"msg": ""
	})})
}
// 新增常发订单
export const addOrderConstantApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}
// 修改常发订单
export const updateOrderConstantApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}
// 删除常发订单
export const deleteOrderConstantApp = (id : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": true,
		"msg": ""
	})})
}


// 级联查询地址库
export const levelSearchAddress = (level: number, parentNo: string) => {
	return request({
		url: '/client/address/levelSearch?level=' + level + '&parentNo=' + parentNo,
		method: 'GET'
	})
}
// 模糊查询地址库
export const likeSearchAddress = (searchValue: string, limit: number) => {
	return request({
		url: '/client/address/likeSearch?searchValue=' + searchValue + '&limit=' + limit,
		method: 'GET'
	})
}
// 获取到达站-列表
export const getArriveDeptListApp = (companyId: number, networkId: number, address: string) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": [
			{
				"arriveDeptId": "000036",
				"arriveDeptName": "安阳",
				"arriveDeptMobile": "13303857614",
				"arriveDeptAddr": "安阳市文峰区安阳市人民政府",
				"destDeptId": null,
				"destDeptName": null
			},
			{
				"arriveDeptId": "000833",
				"arriveDeptName": "西安",
				"arriveDeptMobile": null,
				"arriveDeptAddr": "西安市灞桥区法斯特物流中心",
				"destDeptId": null,
				"destDeptName": null
			}
		],
		"msg": ""
	})})
}
// 获取标准运费
export const getFreightSchemaApp = (params : any) => {
	return new Promise((resolve, reject) => {resolve({
		"code": 0,
		"data": {
			"billDeptId1": "",
			"billDeptId2": "",
			"discountFreight": 0.0,
			"type2": "",
			"type1": "",
			"schemeType2": "",
			"schemeType1": "",
			"freightScheme": 0.0,
			"freightScheme2": 0.0,
			"schemeId2": "",
			"freightScheme1": 0.0,
			"profitModeType": "83801",
			"schemeId1": "",
			"discDeptId1": "",
			"discDeptId2": "",
			"freightSchemeLow": 0.0,
			"price": null,
			"standardFreight": null,
			"limitPrice": null
		},
		"msg": ""
	})})
}

// 获取系统配置
export const getSysConf = (key: string) => {
	return request({
		url: '/client/auth/getSysConf?key=' + key,
		headers: {
			isToken: false
		},
		method: 'GET'
	})
}


