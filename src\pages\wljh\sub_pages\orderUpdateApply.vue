<template>
	<scroll-view scroll-y="true" class="layout-home" style="" ref="scrollViewRef">
    <view style="height: 20rpx;"></view>
		<view  :style="customLayoutHomeStyle">
      <!-- 常发订单遮罩 -->
      <view class="constant-view">
        <view v-show="showOrderConstant" class="constant-pop">
          <view class="constant-pop-content" >
            <view class="constant-pop-content-title">
              <up-row>
                <up-col :span="12">
                  常发
                </up-col>
              </up-row>
            </view>
            <view v-if="!orderConstantList || orderConstantList.length===0" style="display: flex;align-items: center;justify-content: center;">
              <text>暂无数据</text>
            </view>
            <view class="constant-pop-content-data" v-for="(item, index) in orderConstantList" :key="index">
              <up-row >
                <up-col :span="3">
                  <view style="display: flex;align-items: center;">
                    <text style="min-width: 172rpx;">{{ item.sendName + '-' + item.goodsTypeLabel }}</text>
                  </view>
                </up-col>
                <up-col :span="1">
                  <up-icon name="edit-pen" class="constant-icon-edit" size="28" @click="handleOrderConstantEditClick(item)"></up-icon>
                </up-col>
                <up-col :span="2">{{ item.sendName }}</up-col>
                <up-col :span="5">{{ item.goodsShowLabel }}</up-col>
                <up-col :span="1"><up-icon name="trash" size="28" @click="handleOrderConstantDeleteClick(item)"></up-icon></up-col>
              </up-row>
            </view>
            <view class="constant-pop-content-title">
              <up-row>
                <up-col :span="12">
                  历史记录
                </up-col>
              </up-row>
            </view>
            <view v-if="!orderHistoryList || orderHistoryList.length===0" style="display: flex;align-items: center;justify-content: center;">
              <text>暂无数据</text>
            </view>
            <view class="constant-pop-content-data" v-for="(item, index) in orderHistoryList" :key="index">
              <up-row >
                <up-col :span="4">
                  <view @click="dbClickFillForm(item)">{{ formatDate(item.orderStartTime, 'yyyy/MM/dd') }}</view>
                </up-col>
                <up-col :span="2">
                  <view @click="dbClickFillForm(item)">{{ item.sendName }}</view>
                </up-col>
                <up-col :span="5">
                  <view @click="dbClickFillForm(item)">{{ item.goodsShowLabel }}</view>
                </up-col>
                <up-col :span="1">
                  <up-icon name="plus-circle" size="28" @click="handleOrderConstantAddClick(item)"></up-icon>
                </up-col>
              </up-row>
            </view>
          </view>
        </view>
      </view>
			<!-- 开单卡片 -->

			<up-card :border="false" :head-border-bottom="false" :foot-border-top="false" margin="0 20rpx 20rpx 20rpx"
				border-radius="5px" padding="0rpx 0rpx 10rpx 0rpx" >

				<template #body>

					<view class="custom-card-body">

            <!-- 常发订单 -->
            <view class="custom-card-body-item border-bottom-line custom_line order-constant-row" style="padding-bottom: 10rpx;">
              <view class="left-label" style="width:112rpx">
                <up-icon name="setting-fill" size="30" style="margin-left: 10rpx" @click="handleOrderConstantSettingClick"></up-icon>
              </view>
              <up-scroll-list :indicator="false">
                <view class="custom-card-body-item-tag" v-for="(item, index) in orderConstantList"
                      :key="index">
                  <up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
                          :text="item.sendName + '-' + item.goodsTypeLabel"
                          size="mini" :plain="!item.checked" :name="index"
                          @click="handlePopupCardFn(item, index, 'orderConstant')"></up-tag>
                </view>
              </up-scroll-list>
            </view>


            <!-- 发件人信息 -->
            <up-row customStyle="align-items: flex-start;margin-bottom: 20rpx; margin-top: 10rpx;">
<!--							<up-col :span="2" :customStyle="showIdCardStyle">-->
							<up-col :span="2">
								<up-image :src="sendIcon" bgColor="#ffffff" shape="circle" :fade="false" width="80rpx" height="80rpx"></up-image>
							</up-col>

							<up-col :span="10">
								<up-row :gutter="10" customStyle="align-items: flex-start;margin-bottom: 20rpx;">
                  <up-col :span="8">
                    <view class="custom-card-body-item">
                      <!--                      <text class="custom-card-body-item-lable">手机号</text>-->
                      <up-input v-model="orderData.sendPhone" fontSize="15px" color="#000" type="idcard" clearable placeholder="手机号"
                                border="none" :customStyle="customCardBodyItemInput"></up-input>
                    </view>
                  </up-col>
									<up-col :span="4">
										<view class="custom-card-body-item">
                      <!--<text class="custom-card-body-item-lable fw600">姓名</text>-->
                      <up-input v-model="orderData.sendName" fontSize="15px" color="#000" clearable placeholder="姓名"
                        border="none" :customStyle="customCardBodyItemInput"></up-input>
                    </view>
                  </up-col>
                </up-row>

                <view v-show="showIdCard">
                  <up-row :gutter="10" customStyle="align-items: flex-start;margin-top: 30rpx;margin-bottom: 30rpx;">
                    <up-col :span="12">
                      <view class="custom-card-body-item">
<!--                        <text class="custom-card-body-item-lable">身份证号</text>-->
                        <up-input v-model="orderData.sendIdCard" fontSize="15px" color="#000" type="idcard" clearable placeholder="身份证号"
                                  border="none" :customStyle="customCardBodyItemInput"></up-input>
                      </view>
                    </up-col>
                  </up-row>
                </view>


                <up-row :gutter="10"
                  customStyle="padding-bottom: 10px;border-bottom: 1px solid #f4f4f5;">
                  <up-col :span="9">
                    <view class="custom-card-body-item address-combox">
<!--                      <text class="custom-card-body-item-lable">地址</text>-->
                      <up-input v-model="orderData.sendAddress" fontSize="15px" color="#000" clearable placeholder="地址"
                                :customStyle="customCardBodyItemInput" border="none"
                                @focus="showSendAddressSelect=true" @blur="showSendAddressSelect=false"></up-input>
                      <view style="margin-left: 5px;">
                        <up-icon v-if="!showSendAddressSelect"
                                 name="arrow-down" color="#606266" size="20" @click="showSendAddressSelect=true"></up-icon>
                        <up-icon v-if="showSendAddressSelect"
                                 name="arrow-up" color="#606266" size="20" @click="showSendAddressSelect=false"></up-icon>
                      </view>
                    </view>
                  </up-col>
                  <up-col :span="3" customStyle="border-left: 1px solid #f4f4f5;">
                    <view class="custom-card-body-item" style="padding-left: 4rpx;"
                      @click="handleAddressBook('send')">
                      <text class="custom-card-body-item-lable">地址薄</text>
                    </view>
                  </up-col>
                </up-row>
                <view v-show="showSendAddressSelect" class="addressSelect">
                  <scroll-view :scroll-y="true" style="height: 100%" @scroll="showSendAddressSelect=true">
                    <up-row :gutter="10">
                      <up-col :span="12">
                        <view class="addressSelectItem" v-for="item in sendAddressSelectList" :key="item"
                              @click="orderData.sendAddress=item.replaceAll('-', '');showSendAddressSelect=false">
                          {{ item }}
                        </view>
                      </up-col>
                    </up-row>
                  </scroll-view>
                </view>
              </up-col>
            </up-row>

            <!-- 收件人信息 -->
            <up-row customStyle="align-items: flex-start;border-bottom: 1px solid #f4f4f5;">
              <up-col :span="2">
                <up-image :src="collIcon" bgColor="#ffffff" shape="circle" :fade="false" width="80rpx" height="80rpx"></up-image>
              </up-col>

              <up-col :span="10">
                <up-row :gutter="10" customStyle="align-items: flex-start;margin-bottom: 20rpx;">
                  <up-col :span="8">
                    <view class="custom-card-body-item">
                      <!--                      <text class="custom-card-body-item-lable">手机号</text>-->
                      <up-input v-model="orderData.collectPhone" fontSize="15px" color="#000" type="idcard" clearable placeholder="手机号"
                                border="none" :customStyle="customCardBodyItemInput"></up-input>
                    </view>
                  </up-col>
                  <up-col :span="4">
                    <view class="custom-card-body-item">
<!--                      <text class="custom-card-body-item-lable fw600">姓名</text>-->
                      <up-input v-model="orderData.collectName" fontSize="15px" color="#000" clearable placeholder="姓名"
                        border="none" :customStyle="customCardBodyItemInput"></up-input>
                    </view>
                  </up-col>
                </up-row>

                <up-row :gutter="10" customStyle="padding-bottom: 10px;">
                  <up-col :span="9">
                    <view class="custom-card-body-item">
<!--                      <text class="custom-card-body-item-lable">地址</text>-->
                      <up-input v-model="orderData.collectAddress" fontSize="15px" color="#000" clearable placeholder="地址"
                                :customStyle="customCardBodyItemInput" border="none"
                                @focus="showCollectAddressSelect=true" @blur="showCollectAddressSelect=false"></up-input>
                      <view style="margin-left: 5px;">
                        <up-icon v-if="!showCollectAddressSelect"
                                 name="arrow-down" color="#606266" size="20" @click="showCollectAddressSelect=true"></up-icon>
                        <up-icon v-if="showCollectAddressSelect"
                                 name="arrow-up" color="#606266" size="20" @click="showCollectAddressSelect=false"></up-icon>
                      </view>
                    </view>
                  </up-col>
                  <up-col :span="3" customStyle="border-left: 1px solid #f4f4f5;">
                    <view class="custom-card-body-item" style="padding-left: 4rpx;"
                      @click="handleAddressBook('collect')">
                      <text class="custom-card-body-item-lable">地址薄</text>
                    </view>
                  </up-col>
                </up-row>
                <view v-show="showCollectAddressSelect" class="addressSelect">
                  <scroll-view :scroll-y="true" style="height: 100%" @scroll="showCollectAddressSelect=true">
                    <up-row :gutter="10">
                      <up-col :span="12">
                        <view class="addressSelectItem" v-for="item in collectAddressSelectList" :key="item"
                              @click="orderData.collectAddress=item.replaceAll('-', '');showCollectAddressSelect=false">
                          {{ item }}
                        </view>
                      </up-col>
                    </up-row>
                  </scroll-view>
                </view>
              </up-col>
            </up-row>
            <!-- 物品信息 -->
            <view class="custom-card-body-item custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">物品信息</text>
              </view>
              <up-input v-model="orderData.goodsTypeLabel" fontSize="15px" color="#000" clearable placeholder="货品名称"
                        border="none" :customStyle="customCardBodyItemInput"></up-input>
              <view style="margin-left: 5px;display: flex;align-items: center;" @click="handlerBanGoods" >
                <text style="color: #606266">禁寄货品</text>
                <uni-icons type="info" size="20" color="#606266" style="margin-top: 2rpx"></uni-icons>
              </view>
            </view>
            <!-- 体积重量件数 -->
            <view class="custom-card-body-item custom_line" style="padding: 10rpx 0 !important">
              <up-row :gutter="1">
                <up-col :span="4">
                  <view class="goods-info-number">
                    <up-number-box v-model="orderData.totalVolume" integer :min="0">
                      <template v-slot:input>
                        <up-input v-model="orderData.totalVolume" @blur="onNumberChange('totalVolume', 2, $event)" inputAlign="center" type="digit" border="none">
                          <template #suffix>
                            <span>m³</span>
                          </template>
                        </up-input>
                      </template>
                    </up-number-box>
                  </view>
                </up-col>
                <up-col :span="4">
                  <view class="goods-info-number">
                    <up-number-box v-model="orderData.totalWeight" integer :min="0">
                      <template v-slot:input>
                        <up-input v-model="orderData.totalWeight" @blur="onNumberChange('totalWeight', 2, $event)" inputAlign="center" type="digit" border="none">
                          <template #suffix>
                            <span>kg</span>
                          </template>
                        </up-input>
                      </template>
                    </up-number-box>
                  </view>
                </up-col>
                <up-col :span="4">
                  <view class="goods-info-number">
                    <up-number-box v-model="orderData.totalNum" integer :min="1">
                      <template v-slot:input>
                        <up-input v-model="orderData.totalNum" @blur="onNumberChange('totalNum', 0, $event)" inputAlign="center" type="digit" border="none">
                          <template #suffix>
                            <span>件</span>
                          </template>
                        </up-input>
                      </template>
                    </up-number-box>
                  </view>
                </up-col>
              </up-row>
            </view>
            <!-- 包装 -->
            <up-row customStyle="display:flex;align-items: center;border-bottom: 1px solid #f4f4f5;padding: 20rpx 0;" :gutter="4">
              <up-col :span="2">
                <view style="font-size: 15px;">
                  <text>包装</text>
                </view>
              </up-col>
              <up-col :span="10">
                <view class="goods-select" >
                  <view class="goods-select-item" v-for="(item, index) in packMethodList"
                        :key="index">
                    <up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
                            :text="item.name"
                            size="mini" :plain="!item.checked" :name="index"
                            @click="handlePopupCardFn(item, index, 'orderConstant')"></up-tag>
                  </view>
                </view>
              </up-col>
            </up-row>

            <!-- 物流公司 -->
						<view class="custom-card-body-item  custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">物流公司</text>
							</view>

							<up-scroll-list :indicator="false">
								<view class="custom-card-body-item-tag" v-for="(item, index) in radiosPartnersTrue"
									:key="index">
									<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
										:text="item.name" size="mini" :plain="!item.checked" :name="item.id"
										@click="handlePopupCardFn(item, index, 'logisticsCompanies')"></up-tag>
								</view>
							</up-scroll-list>
						</view>
            <!-- 到站 -->
						<view class="custom-card-body-item custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">到达站</text>
							</view>

							<up-scroll-list :indicator="false">
								<view class="custom-card-body-item-tag" v-for="(item, index) in arriveInfoList"
									:key="index">
									<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
										:text="item.showText" size="mini" :plain="!item.checked" :name="item.arriveDeptId"
										@click="handlePopupCardFn(item, index, 'arriveDeptId')"></up-tag>
								</view>
							</up-scroll-list>
						</view>
						<view style="white-space: nowrap;overflow: auto;" class="custom-card-body-item border-bottom-line custom_line">
              {{ arriveInfo.arriveDeptMobile }} {{ arriveInfo.arriveDeptAddr }}
            </view>
						<!-- 付款方式 -->
						<view class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">付款方式</text>
							</view>
							<up-scroll-list :indicator="false">
								<view class="custom-card-body-item-tag" v-for="(item, index) in radiosPayList"
									:key="index">
									<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
										:text="item.name" size="mini" :plain="!item.checked" :name="index"
										@click="handlePopupCardFn(item, index)"></up-tag>
								</view>
							</up-scroll-list>
						</view>
						<!-- 送货方式 -->
						<view :class="orderData.deliveryMethod === '1' ? 'custom-card-body-item custom_line border-bottom-line' : 'custom-card-body-item custom_line'">
							<view class="left-label">
								<text class="custom-card-body-item-lable">送货方式</text>
							</view>
							<up-scroll-list :indicator="false">
								<view class="custom-card-body-item-tag" v-for="(item, index) in deliveryMethodList"
									:key="index">
									<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
										:text="item.name" size="mini" :plain="!item.checked" :name="index"
										@click="handlePopupCardFn(item, index)"></up-tag>
								</view>
							</up-scroll-list>
						</view>
						<!-- 送货费 -->
						<view v-if="orderData.deliveryMethod === '2'" class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">送货费：</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
							</view>
							<up-input v-model="orderData.deliveryAmount" mode="price" fontSize="15px" color="#000"
                        border="none" inputAlign="left" :adjustPosition="false" safe-area @blur="onAmountChange('deliveryAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
<!--							<view style="margin-left: 5px;"><text class="custom-card-body-item-lable">元</text></view>-->
						</view>
						<!-- 放货方式 -->
						<view :class="!(orderData.releaseMethod === '1' && getNumber(orderData.releaseAmount)>0) ? 'custom-card-body-item custom_line border-bottom-line' : 'custom-card-body-item custom_line'">
							<view class="left-label">
								<text class="custom-card-body-item-lable">放货方式</text>
							</view>
							<up-scroll-list :indicator="false">
								<view class="custom-card-body-item-tag" v-for="(item, index) in releaseMethodList"
									:key="index">
									<up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
										:text="item.name" size="mini" :plain="!item.checked" :name="index"
										@click="handlePopupCardFn(item, index)"></up-tag>
								</view>
							</up-scroll-list>
						</view>
            <!-- 控货费 -->
            <view v-if="orderData.releaseMethod === '1' && getNumber(orderData.releaseAmount)>0" class="custom-card-body-item border-bottom-line custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">控货费：</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
              </view>
              <up-input v-model="orderData.releaseAmount" mode="price" fontSize="15px" color="#000" disabled=""
                        border="none" inputAlign="left" :adjustPosition="false" safe-area @blur="onAmountChange('releaseAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
            </view>
            <!-- 回单类型 -->
            <view :class="!(orderData.receiptType && getNumber(orderData.receiptAmount)>0) ? 'custom-card-body-item custom_line border-bottom-line' : 'custom-card-body-item custom_line'">
              <view class="left-label">
                <text class="custom-card-body-item-lable">回单类型</text>
              </view>
              <up-scroll-list :indicator="false">
                <view class="custom-card-body-item-tag" v-for="(item, index) in receiptTypeList"
                      :key="index">
                  <up-tag borderColor="#f4f4f5" :color="item.checked ? '#fff' : '#000'"
                          :text="item.name" size="mini" :plain="!item.checked" :name="index"
                          @click="handlePopupCardFn(item, index)"></up-tag>
                </view>
              </up-scroll-list>
            </view>
            <!-- 回单费 -->
            <view v-if="orderData.receiptType && getNumber(orderData.receiptAmount)>0" class="custom-card-body-item border-bottom-line custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">回单费：</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
              </view>
              <up-input v-model="orderData.receiptAmount" mode="price" fontSize="15px" color="#000" disabled=""
                        border="none" inputAlign="left" :adjustPosition="false" safe-area @blur="onAmountChange('receiptAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
            </view>
            <!-- 代收货款 -->
            <up-row :customStyle="!showCollectionDeliveryInfo ? 'align-items: flex-start;border-bottom: 1px solid #f4f4f5;padding: 20rpx 0 20rpx 0;' : 'align-items: flex-start;padding: 20rpx 0 20rpx 0;'">
              <up-col :span="5.5">
                <view class="custom-card-body-item">
                  <view class="left-label">
                    <text class="custom-card-body-item-lable">代收货款</text>
                    <text class="custom-card-body-item-lable left-label-right">¥</text>
                  </view>
                  <up-input v-model="orderData.collectionDelivery" mode="price" fontSize="15px" color="#000" :customStyle="customCardBodyItemInput"
                            border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('collectionDelivery', $event)"
                            bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
                </view>
              </up-col>
              <up-col :span="4">
                <view class="custom-card-body-item">
                  <text class="custom-card-body-item-lable">手续费: ¥</text>
                  <up-input v-model="orderData.collectionPayAmount" mode="price" fontSize="15px" color="#000" :customStyle="customCardBodyItemInput"
                            border="none" inputAlign="left" :adjustPosition="true" safe-area disabled=""
                            cursor-spacing="0" type="digit"></up-input>
                </view>
              </up-col>
              <up-col :span="2.5">
                <view class="custom-card-body-item">
                  <text class="custom-card-body-item-lable">费率{{ orderData.collectionProcRate }}‰</text>
                </view>
              </up-col>
            </up-row>
            <!-- 收款人 -->
            <view v-if="showCollectionDeliveryInfo" class="custom-card-body-item custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">收款人</text>
              </view>
              <up-input v-model="orderData.collectionName" fontSize="15px" color="#000" border="none"
                        inputAlign="left" :adjustPosition="true" safe-area
                        bindfocus="onInputFocus"></up-input>
            </view>
            <!-- 收款行 -->
            <view v-if="showCollectionDeliveryInfo" class="custom-card-body-item custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">收款行</text>
              </view>
              <up-input v-model="orderData.collectionBankName" fontSize="15px" color="#000" border="none"
                        inputAlign="left" :adjustPosition="true" safe-area
                        bindfocus="onInputFocus"></up-input>
            </view>
            <!-- 卡号 -->
            <view v-if="showCollectionDeliveryInfo" class="custom-card-body-item border-bottom-line custom_line">
              <view class="left-label">
                <text class="custom-card-body-item-lable">卡号</text>
              </view>
              <up-input v-model="orderData.collectionCardNum" fontSize="15px" color="#000" border="none"
                        inputAlign="left" :adjustPosition="true" safe-area
                        bindfocus="onInputFocus"></up-input>
            </view>
            <!-- 保价 -->
            <up-row customStyle="align-items: flex-start;border-bottom: 1px solid #f4f4f5;padding: 20rpx 0 20rpx 0;">
              <up-col :span="5.5">
                <view class="custom-card-body-item">
                  <view class="left-label">
                    <text class="custom-card-body-item-lable">保价&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</text>
                    <text class="custom-card-body-item-lable left-label-right">¥</text>
                  </view>
                  <up-input v-model="orderData.insuredAmount" mode="price" fontSize="15px" color="#000" :customStyle="customCardBodyItemInput"
                            border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('insuredAmount', $event)"
                            bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
                </view>
              </up-col>
              <up-col :span="4">
                <view class="custom-card-body-item">
                  <text class="custom-card-body-item-lable">保价费: ¥</text>
                  <up-input v-model="orderData.insuredPayAmount" mode="price" fontSize="15px" color="#000" :customStyle="customCardBodyItemInput"
                            border="none" inputAlign="left" :adjustPosition="true" safe-area disabled=""
                            cursor-spacing="0" type="digit"></up-input>
                </view>
              </up-col>
              <up-col :span="2.5">
                <view class="custom-card-body-item">
                  <text class="custom-card-body-item-lable">费率{{ orderData.rateBzf }}‰</text>
                </view>
              </up-col>
            </up-row>
						<!-- 运费 -->
						<view class="custom-card-body-item border-bottom-line custom_line" >
							<view class="left-label">
								<text class="custom-card-body-item-lable">运费&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
							</view>
							<up-input v-model="orderData.amountFreight" mode="price" fontSize="15px" color="#000"
								border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('amountFreight', $event)"
								bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
<!--							<view style="margin-left: 5px;"><text class="custom-card-body-item-lable">元</text></view>-->
              <view style="margin-left: 5px;">
                <text>标准运费 ¥ {{ freightSchema.freightSchemeLow }}</text>
              </view>
            </view>
            <!-- 其他费用 -->
            <view v-if="showOtherAmount" class="custom-card-body-item border-bottom-line custom_line" >
              <view class="left-label">
                <text class="custom-card-body-item-lable">其他费用</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
              </view>
              <up-input v-model="orderData.otherAmount" mode="price" fontSize="15px" color="#000"
                        border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('otherAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
            </view>
            <!-- 佣金 -->
            <view v-if="showBrokerageAmount"  class="custom-card-body-item border-bottom-line custom_line" >
              <view class="left-label">
                <text class="custom-card-body-item-lable">佣金</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
              </view>
              <up-input v-model="orderData.brokerageAmount" mode="price" fontSize="15px" color="#000"
                        border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('brokerageAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
            </view>
						<!-- 费用合计 -->
						<view class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
                <text class="custom-card-body-item-lable">费用合计</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
							</view>
							<up-input v-model="orderData.totalAmount" fontSize="15px" color="#000" border="none"
								inputAlign="left" :adjustPosition="true" safe-area bindfocus="onInputFocus"
								bindblur="onInputBlur" type="digit" disabled="true">
							</up-input>
							<view style="margin-left: 5px;">
                <uni-icons type="help" size="20" color="#606266" @click="onHelpClick"></uni-icons>
<!--								<text class="custom-card-body-item-lable">元</text>-->
							</view>
						</view>
            <!-- 优惠金额 -->
            <view v-if="showDiscountAmount" class="custom-card-body-item border-bottom-line custom_line" >
              <view class="left-label">
                <text class="custom-card-body-item-lable">优惠金额</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
              </view>
              <up-input v-model="orderData.discountAmount" mode="price" fontSize="15px" color="#000"
                        border="none" inputAlign="left" :adjustPosition="true" safe-area @blur="onAmountChange('discountAmount', $event)"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
            </view>
						<!-- 实付金额 -->
						<view class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
                <text class="custom-card-body-item-lable">实付金额</text>
                <text class="custom-card-body-item-lable left-label-right">¥</text>
							</view>
              <up-input v-model="orderData.actualPayAmount" mode="price" fontSize="15px" color="#000"
                        border="none" inputAlign="left" :adjustPosition="true" safe-area disabled="true"
                        bindfocus="onInputFocus" cursor-spacing="0" type="digit"></up-input>
              <view style="margin-left: 5px;">
                <uni-icons type="help" size="20" color="#606266" @click="onHelpClick"></uni-icons>
              </view>
						</view>
						<!-- 备注 -->
						<view class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">备注</text>
							</view>
							<up-input v-model="orderData.remark" fontSize="15px" color="#000" border="none"
								inputAlign="left" :adjustPosition="true" safe-area
								bindfocus="onInputFocus"></up-input>
						</view>
						<!-- 申请原因 -->
						<view class="custom-card-body-item border-bottom-line custom_line">
							<view class="left-label">
								<text class="custom-card-body-item-lable">申请原因</text>
							</view>
							<up-input v-model="updateFormData.applyReason" fontSize="15px" color="#000" border="none"
								inputAlign="left" :adjustPosition="true" safe-area
								bindfocus="onInputFocus"></up-input>
						</view>
					</view>
				</template>

				<template #foot>
          <!-- 底部按钮 -->
					<view class="custom-card-foot">
						<up-row :gutter="60">
							<up-col :span="12">
								<up-button type="error" shape="circle" text="确定"
									@click="submitUpdateForm"></up-button>
							</up-col>
						</up-row>
					</view>
				</template>
			</up-card>
			<!-- 其他物流服务商 -->
			<view class="custom-home-bottom" v-if="radiosPartnersFalse && radiosPartnersFalse.length > 0">
				<view class="custom-home-bottom-tag">
					<up-tag v-for="(item, index) in radiosPartnersFalse" :key="index" borderColor="#f4f4f5"
						:color="item.checked ? '#fff' : '#000'" :text="item.name" size="large" :plain="!item.checked"
						:name="index" @click="handlePopupCardFn(item, index)"></up-tag>
				</view>
			</view>
		</view>
	</scroll-view>
  <!-- 费用明细弹窗 -->
  <up-modal :show="showAmountDetail" title="费用明细" @confirm="showAmountDetail=false">
    <up-cell-group>
      <up-cell title="运费" :value="getNumber(orderData.amountFreight) + '元'"></up-cell>
      <up-cell title="保价费" :value="getNumber(orderData.insuredPayAmount) + '元'"></up-cell>
      <up-cell title="送货费" v-if="orderData.deliveryMethod === '2'" :value="getNumber(orderData.deliveryAmount) + '元'"></up-cell>
      <up-cell title="控货费" v-if="orderData.releaseMethod === '1' && getNumber(orderData.releaseAmount)>0" :value="getNumber(orderData.releaseAmount) + '元'"></up-cell>
      <up-cell title="回单费" v-if="orderData.isReceipt === '1' && getNumber(orderData.receiptAmount)>0" :value="getNumber(orderData.receiptAmount) + '元'"></up-cell>
      <up-cell title="佣金" v-if="showBrokerageAmount" :value="getNumber(orderData.brokerageAmount) + '元'"></up-cell>
      <up-cell title="其他费用" v-if="showOtherAmount" :value="getNumber(orderData.otherAmount) + '元'"></up-cell>
      <up-cell title="制单费" v-if="getNumber(orderData.amountZdf) > 0" :value="getNumber(orderData.amountZdf) + '元'"></up-cell>
      <up-cell title="代收手续费" v-if="orderData.collectionDelivery" :titleStyle="unCalcCellTitleStyle" :value="getNumber(orderData.collectionPayAmount) + '元'"></up-cell>
      <up-cell title="费用合计" :titleStyle="detailTotalCellTitleStyle" :value="getNumber(orderData.totalAmount) + '元'"></up-cell>
      <up-cell title="优惠金额" v-if="showDiscountAmount" :titleStyle="discountCellTitleStyle" :value="getNumber(orderData.discountAmount) + '元'"></up-cell>
      <up-cell title="实付金额" :titleStyle="detailTotalCellTitleStyle"  :value="getNumber(orderData.totalAmount) + '元'"></up-cell>
    </up-cell-group>
  </up-modal>
  <!-- 禁寄货品弹窗 -->
  <up-popup :show="showBanGoods" :round="10" :closeable="true" mode="bottom" @close="handlerBanGoods" @touchmove.stop.prevent="">
    <view class="ban-goods-pop">
      <view class="ban-goods-pop-title">禁止寄递物品目录</view>
      <scroll-view scroll-y enable-flex style="width: 100vw;height: 100%;margin-bottom: 10px;padding-bottom: 20px">
        <view class="ban-goods-pop-h" style="margin-top: 80rpx">一、国家机关公文</view>
        <view class="ban-goods-pop-t">国家机关基于公务活动而制作的具有特定文体和格式，并加盖国家机关公章的书面材料。</view>
        <view class="ban-goods-pop-h">二、国家保护野生动物、濒危野生动物及其制品</view>
        <view class="ban-goods-pop-t">如象牙、虎骨、犀牛角及其制品等。</view>
        <view class="ban-goods-pop-h">三、侵犯知识产权和假冒伪劣、无证经营物品</view>
        <view class="ban-goods-pop-t">1.侵犯知识产权：如侵犯专利权、商标权、著作权的图书、音像制品等。</view>
        <view class="ban-goods-pop-t">2.假冒伪劣：如假冒伪劣的食品、药品、儿童用品、电子产品、化妆品、纺织品等。</view>
        <view class="ban-goods-pop-t">3.无证经营：未取得食品生产经营许可等资质证件而生产的物品等。</view>
        <view class="ban-goods-pop-h">四、非法伪造物品</view>
        <view class="ban-goods-pop-t">如伪造或者变造的货币、证件、公章、发票等。</view>
        <view class="ban-goods-pop-h">五、枪支(含仿制品、主要零部件)弹药</view>
        <view class="ban-goods-pop-t">1.枪支(含仿制品、主要零部件):如手枪、步枪、冲锋枪、防暴枪、气枪、猎枪、运动枪、麻醉注射枪、钢珠枪、催泪枪等。</view>
        <view class="ban-goods-pop-t">2.弹药(含仿制品):如子弹、炸弹、手榴弹、火箭弹、照明弹、燃烧弹、烟幕(雾)弹、信号弹、催泪弹、毒气弹、地雷、手雷、炮弹、火药等。</view>
        <view class="ban-goods-pop-h">六、管制器具</view>
        <view class="ban-goods-pop-t">1.管制刀具:如匕首、三棱刮刀、带有自锁装置的弹簧刀(跳刀)、其他相类似的单刃、双刃、三棱尖刀等。</view>
        <view class="ban-goods-pop-t">2.其他:如弩、催泪器、催泪枪、电击器等。</view>
        <view class="ban-goods-pop-h">七、爆炸物品</view>
        <view class="ban-goods-pop-t">1.爆破器材:如炸药、雷管、导火索、导爆索、爆破剂等。</view>
        <view class="ban-goods-pop-t">2.烟花爆竹:如烟花、鞭炮、摔炮、拉炮、砸炮、彩药弹等烟花爆竹及黑火药、烟火药、发令纸、引火线等。</view>
        <view class="ban-goods-pop-t">3.其他:如推进剂、发射药、硝化棉、电点火头等。</view>
        <view class="ban-goods-pop-h">八、压缩和液化气体及其容器</view>
        <view class="ban-goods-pop-t">1.易燃气体:如氢气、甲烷、乙烷、丁烷、天然气、液化石油气、乙烯、丙烯、乙炔、打火机等。</view>
        <view class="ban-goods-pop-t">2.有毒气体:如一氧化碳、一氧化氮、氯气等。</view>
        <view class="ban-goods-pop-t">3.易爆或者窒息、助燃气体:如压缩氧气、氮气、氦气、氖气、气雾剂等。</view>
        <view class="ban-goods-pop-h">九、易燃液体</view>
        <view class="ban-goods-pop-t">如汽油、柴油、煤油、桐油、丙酮、乙醚、油漆、生漆、苯酒精、松香油等。</view>
        <view class="ban-goods-pop-h">十、易燃固体、自燃物质、遇水易燃物质</view>
        <view class="ban-goods-pop-t">1.易燃固体:如红磷、硫磺、铝粉、闪光粉、固体酒精、火柴、活性炭等。</view>
        <view class="ban-goods-pop-t">2.自燃物质:如黄磷、白磷、硝化纤维(含胶片)、钛粉等。</view>
        <view class="ban-goods-pop-t">3.遇水易燃物质:如金属钠、钾、锂、锌粉、镁粉、碳化钙(电石)、氰化钠、氰化钾等。</view>
        <view class="ban-goods-pop-h">十一、氧化剂和过氧化物</view>
        <view class="ban-goods-pop-t">如高锰酸盐、高氯酸盐、氧化氢、过氧化钠、过氧化钾、过氧化铅、氯酸盐、溴酸盐、硝酸盐、双氧水等。</view>
        <view class="ban-goods-pop-h">十二、毒性物质</view>
        <view class="ban-goods-pop-t">如砷、砒霜、汞化物、铊化物、氰化物、硒粉、苯酚、汞、剧毒农药等。</view>
        <view class="ban-goods-pop-h">十三、生化制品、传染性、感染性物质</view>
        <view class="ban-goods-pop-t">如病菌、炭疽、寄生虫、排泄物、医疗废弃物、尸骨、动物器官、肢体、未经硝制的兽皮、未经药制的兽骨等。</view>
        <view class="ban-goods-pop-h">十四、放射性物质</view>
        <view class="ban-goods-pop-t">如铀、钴、镭、钚等。</view>
        <view class="ban-goods-pop-h">十五、腐蚀性物质</view>
        <view class="ban-goods-pop-t">如硫酸、硝酸、盐酸、蓄电池、氢氧化钠、氢氧化钾等。</view>
        <view class="ban-goods-pop-h">十六、毒品及吸毒工具、非正当用途麻醉药品和精神药品、非正当用途的易制毒化学品</view>
        <view class="ban-goods-pop-t">1.毒品、麻醉药品和精神药品:如鸦片(包括罂粟壳、花、苞、叶)、吗啡、海洛因、可卡因、大麻、甲基苯丙胺(冰毒)、氯胺酮、甲卡西酮、苯丙胺、安钠咖等。</view>
        <view class="ban-goods-pop-t">2.易制毒化学品:如胡椒醛、黄樟素、黄樟油、麻黄素、伪麻黄素、羟亚胺、邻酮、苯乙酸、溴代苯丙酮、醋酸酐、甲苯、丙酮等。</view>
        <view class="ban-goods-pop-t">3.吸毒工具:如冰壶等。</view>
        <view class="ban-goods-pop-h">十七、非法出版物、印刷品、音像制品等宣传品</view>
        <view class="ban-goods-pop-t">如含有反动、煽动民族仇恨、破坏国家统一、破坏社会稳定宣扬邪教、宗教极端思想、淫秽等内容的图书、刊物、图片照片、音像制品等。</view>
        <view class="ban-goods-pop-h">十八、间谍专用器材</view>
        <view class="ban-goods-pop-t">如暗藏式窃听器材、窃照器材、突发式收发报机、一次性密码本、密写工具、用于获取情报的电子监听和截收器材等。</view>
        <view class="ban-goods-pop-h">十九、非法伪造物品</view>
        <view class="ban-goods-pop-t">如伪造或者变造的货币、证件、公章等。</view>
        <view class="ban-goods-pop-h">二十、禁止进出境物品</view>
        <view class="ban-goods-pop-t">如有碍人畜健康的、来自疫区的以及其他能传播疾病的食品药品或者其他物品;内容涉及国家秘密的文件、资料及其他物品，以及收件地在国外、港澳台地区禁止入境的物品。</view>
        <view class="ban-goods-pop-h">二十一、其他物品</view>
        <view class="ban-goods-pop-t">《危险化学品目录》《民用爆炸物品品名表》《易制爆危险化学品名录》《易制毒化学品的分类和品种目录》《中华人民共和国禁止进出境物品表》载明的物品和《人间传染的病原微生物名录》载明的第一、二类病原微生物等，以及法律、行政法规、国务院和国务院有关部门规定禁止寄递的其他物品。</view>
        <view style="height: 60rpx"> </view>
      </scroll-view>
      <view class="ban-goods-pop-btn">
        <up-button :plain="true" text="知道了" shape="circle"
                   customStyle="color:#fff;height:45px;backgroundColor: #d81e06;" @click="handlerBanGoods"></up-button>
      </view>
    </view>
  </up-popup>

</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, watch} from 'vue'
import {onLoad, onUnload} from "@dcloudio/uni-app";
import {
  getLogisticsCompaniesInfo, saveOrderInfoApp,
  getOrderConstantListApp, addOrderConstantApp, deleteOrderConstantApp, likeSearchAddress,
  getArriveDeptListApp, getFreightSchemaApp
} from '@/api/wljh/home'
import {getDictDataInfoAll, getOrderInfoPageClient, getOrderShowInfo} from '@/api/wljh/task'
import modal from '@/plugins/modal'
import customStorage from "@/utils/customStorage"
import {formatDate, formatMoney} from "@/api/common"
import {createOrderUpdateApply} from "@/pages/wljh/sub_pages/api/sub_page";

const sendIcon = ref('https://dachisc.wang:9000/wljh/134cc43076fad1ccc6578ec1c80e188efeb55dd0665f7743ade3722e1ca59d54.png')
const collIcon = ref('https://dachisc.wang:9000/wljh/f455372bdd7e2cc155cf0dc4c316e1aea78f8c2803e2462a2a75e7dc7bec32b8.png')

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const scrollViewRef = ref(null)


const showOrderConstant = ref(false) // 常发订单弹窗展示
const showBanGoods = ref(false) // 禁寄货品弹窗展示

const showIdCard = ref(false) // 身份证号展示
const showIdCardStyle = ref('') // 身份证号展示样式

const showCollectionDeliveryInfo = ref(false) // 代收货款信息展示
const showBrokerageAmount = ref(false) // 佣金展示
const showOtherAmount = ref(false) // 其他费用展示
const showDiscountAmount = ref(false) // 优惠金额展示
const showAmountDetail = ref(false) // 费用明细展示

// 网点信息
const networkInfo = ref({
	id: undefined,
	name: '',
	address: '',
  mobile: '',
  allowBrokerage: ''
})
// 开单卡片标题
const orderConstantList = ref([]) // 常开订单数据
const orderHistoryList = ref([]) // 历史订单数据
const radiosPayList = ref([]) // 付款方式数据
const radiosPartnersTrue = ref([]) // 物流公司合作商
const radiosPartnersFalse = ref([]) // 物流公司非合作商
const isReceiptList = ref([ // 是否回单
    {
      name: '是',
      value: '1',
      dictType: 'Receipt',
      checked: false
    },
    {
      name: '否',
      value: '0',
      dictType: 'Receipt',
      checked: true
    }
])
const deliveryMethodList = ref([ // 送货方式
    {
      name: '自提',
      value: '1',
      dictType: 'Delivery',
      checked: true
    },
    {
      name: '送货',
      value: '2',
      dictType: 'Delivery',
      checked: false
    }
])
const releaseMethodList = ref([ // 放货方式
    {
      name: '等通知',
      value: '1',
      dictType: 'Release',
      checked: false
    }
])
const receiptTypeList = ref([ // 回单类型
    {
      name: '电子回单',
      value: '1',
      dictType: 'receiptType',
      checked: true
    },
      {
      name: '纸质回单',
      value: '2',
      dictType: 'receiptType',
      checked: false
    }
])
const packMethodList = ref([ // 包装方式
    {
      name: '箱',
      value: '1',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '纸',
      value: '2',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '袋',
      value: '3',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '桶',
      value: '4',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '木',
      value: '5',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '膜',
      value: '6',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '皮',
      value: '7',
      dictType: 'Pack',
      checked: false
    },
    {
      name: '布',
      value: '8',
      dictType: 'Pack',
      checked: false
    }
])
const orderData = ref({
	id: undefined,
	orderCode: undefined,
	collectName: undefined,
	collectPhone: undefined,
	collectAddress: 'null',
  sendAddressOne: undefined,
  sendAddressTwo: undefined,
  sendAddressThree: undefined,
  sendAddressFour: undefined,
  sendAddressFive: undefined,
  collectAddressOne: undefined,
  collectAddressTwo: undefined,
  collectAddressThree: undefined,
  collectAddressFour: undefined,
  collectAddressFive: undefined,
	sendName: undefined,
	sendPhone: undefined,
  sendIdCard: undefined,
	sendAddress: undefined,
	goodsType: undefined,
	totalWeight: 1,
	totalVolume: 1,
	totalNum: 1,
	goodsLong: undefined,
	goodsWidth: undefined,
	goodsHeight: undefined,
	payMethod: 'PAY_METHOD_SEND_PAY_SETTLE',
	shouldPayAmount: undefined,
	actualPayAmount: undefined,
  amountFreight: undefined,
  totalAmount: 0,
	logisticsCompaniesId: undefined,
	logisticsNetworkId: undefined,
	orderStatus: undefined,
	logisticsStatus: undefined,
	orderStartTime: undefined,
	orderEndTime: undefined,
	goodsInfo: undefined,
  collectionDeliveryInfo: undefined,
	isReceipt: '0',
	deliveryMethod: '1',
	releaseMethod: '2',
	packMethod: undefined,
	collectionDelivery: undefined,
  collectionName: undefined,
  collectionBankName: undefined,
  collectionCardNum: undefined,
	remark: undefined,
	deliveryAmount: undefined,// 送货费
	insuredAmount: undefined,// 保价
  amountZdf: undefined,// 制单费
  rateBzf: 1,
  receiptType: '1',// 回单类型（1-电子回单 2-纸质回单）
  receiptAmount: undefined,// 回单费
  releaseAmount: undefined,// 控货费
  insuredPayAmount: undefined,// 支付的保价费
  brokerageAmount: undefined,// 佣金
  brokerageBackType: '1',// 佣金返款方式（1-现返 2-欠返）
  brokerageBackTypeLabel: '现返',// 佣金返款方式（1-现返 2-欠返）
  otherAmount: undefined,// 其他费用
  discountAmount: undefined,// 优惠金额
  collectionPayAmount: undefined,// 代收款手续费
  collectionProcRate: 0,// 代收款手续费率(‰)
  collectionProcMin: undefined,// 代收款手续费起步价
  collectionProcMax: undefined,// 代收款手续费封顶价
})

// 跳转到地址薄
const handleAddressBook = (type) => {
	uni.navigateTo({
		url: '/pages/wljh/sub_pages/addressBook',
		events: {
			addressBookChild: function (data) {
				if (type === 'send') {
					if (data) {
						orderData.value.sendName = data.name
						orderData.value.sendPhone = data.mobile
						orderData.value.sendIdCard = data.idCard
						orderData.value.sendAddress = data.showAddress
					}
				} else if (type === 'collect') {
					if (data) {
						orderData.value.collectName = data.name
						orderData.value.collectPhone = data.mobile
						orderData.value.collectAddress = data.showAddress
					}
				}
			}
		}
	});
}
// 获取字典所有值
const getDictDataInfo = async (dictType) => {
	await getDictDataInfoAll({
		'type': dictType
	}).then(res => {
		if (res.code === 0) {
			switch (dictType) {
				case 'PAY_METHOD':
					radiosPayList.value = res.data.map(item => {
						let param = {
							'name': item.label,
							'value': item.value,
							'dictType': dictType,
							'checked': item.value === 'PAY_METHOD_SEND_PAY_SETTLE'
						}
						return param
					})
					break;
				case 'LOGISTICS_STATUS':
					radiosLogisticsList.value = res.data.map(item => {
						let param = {
							'name': item.label,
							'value': item.value,
							'dictType': dictType,
							'checked': false
						}
						return param
					})
					break;
			}
		}
	})
}
// 获取常发订单信息
const getOrderConstantListApi = async () => {
	getOrderConstantListApp({}).then(res => {
		if (res.code === 0) {
			orderConstantList.value = res.data
		}
	})
}
// 获取最近订单信息
const getOrderListApi = () => {
  getOrderInfoPageClient({
    pageNo: 1,
    pageSize: 20
  }).then(res => {
    if (res.code === 0) {
      orderHistoryList.value = res.data.list
    }
  }).catch(error => {
    console.log(error)
  })
}

// 选项卡选中
const handlePopupCardFn = (item, index, type) => {
	if (item.dictType === 'PAY_METHOD') { // 付款方式
		item.checked = !item.checked
		if (item.checked) {
			orderData.value.payMethod = item.value
		} else {
			orderData.value.payMethod = undefined
		}
		radiosPayList.value.map((obj, inx) => {
			if (index != inx) {
				obj.checked = false
			}
		})
    if (orderData.value.payMethod === 'PAY_METHOD_SEND_PAY_SETTLE') {
      // 现付-现返
      orderData.value.brokerageBackType = '1'
      orderData.value.brokerageBackTypeLabel = '现返'
    } else if (orderData.value.payMethod === 'PAY_METHOD_COLLECT_PAY_SETTLE') {
      // 到付-欠返
      orderData.value.brokerageBackType = '2'
      orderData.value.brokerageBackTypeLabel = '欠返'
    }
	} else if (item.dictType === 'Receipt') { // 是否回单
		//item.checked = !item.checked
		//orderData.value.isReceipt = item.checked ? item.value : undefined
		orderData.value.isReceipt = item.value
		isReceiptList.value.map((obj, inx) => {
			obj.checked = index != inx ? false : true
		})
	} else if (item.dictType === 'Delivery') { // 送货方式
		//item.checked = !item.checked
		//orderData.value.deliveryMethod = item.checked ? item.value : undefined
		orderData.value.deliveryMethod = item.value
		deliveryMethodList.value.map((obj, inx) => {
			obj.checked = index != inx ? false : true
		})
    onAmountChange('', 0)
	} else if (item.dictType === 'Release') { // 放货方式
		item.checked = !item.checked
		orderData.value.releaseMethod = item.checked ? item.value : undefined
		// orderData.value.releaseMethod = item.value
		releaseMethodList.value.map((obj, inx) => {
			// obj.checked = index != inx ? false : true
      if (index != inx) {
        obj.checked = false
      }
		})
    onAmountChange('', 0)
	} else if (item.dictType === 'receiptType') { // 回单类型
		item.checked = !item.checked
		orderData.value.receiptType = item.checked ? item.value : undefined
    receiptTypeList.value.map((obj, inx) => {
			// obj.checked = index != inx ? false : true
      if (index != inx) {
        obj.checked = false
      }
		})
    if (orderData.value.receiptType !== undefined) {
      orderData.value.isReceipt = '1'
    } else {
      orderData.value.isReceipt = '0'
    }
    console.log(orderData.value.receiptType)
    onAmountChange('', 0)
	} else if (item.dictType === 'Pack') { // 包装方式
		item.checked = !item.checked
		orderData.value.packMethod = item.checked ? item.value : undefined
		packMethodList.value.map((obj, inx) => {
			obj.checked = index != inx ? false : item.checked
		})
	} else if (type === 'logisticsCompanies'){
    // 物流公司
		item.checked = !item.checked
    console.log(item)
		if (item.checked) {
			orderData.value.logisticsCompaniesId = item.id
      // 设置制单费
      if (item.amountZdf === null || item.amountZdf === undefined || item.amountZdf === '') {
        // 默认0
        orderData.value.amountZdf = 0
      } else {
        orderData.value.amountZdf = item.amountZdf
      }
      // 设置保值费率
      if (item.rateBzf === null || item.rateBzf === undefined || item.rateBzf === '') {
        // 默认1
        orderData.value.rateBzf = 1
      } else {
        orderData.value.rateBzf = item.rateBzf
      }
      // 设置代收款手续费率
      if (item.collectionProcRate === null || item.collectionProcRate === undefined || item.collectionProcRate === '') {
        // 默认0
        orderData.value.collectionProcRate = 0
      } else {
        orderData.value.collectionProcRate = item.collectionProcRate
      }
      // 设置代收款手续费起步价
      if (item.collectionProcMin === null || item.collectionProcMin === undefined || item.collectionProcMin === '') {
        // 默认0
        orderData.value.collectionProcMin = 0
      } else {
        orderData.value.collectionProcMin = item.collectionProcMin
      }
      // 设置代收款手续费封顶价 默认不封顶
      orderData.value.collectionProcMin = item.collectionProcMin
      // 设置回单费
      if (item.receiptAmount === null || item.receiptAmount === undefined || item.receiptAmount === '') {
        // 默认0
        orderData.value.receiptAmount = 0
      } else {
        orderData.value.receiptAmount = item.receiptAmount
      }
      // 设置控货费
      if (item.releaseAmount === null || item.releaseAmount === undefined || item.releaseAmount === '') {
        // 默认0
        orderData.value.releaseAmount = 0
      } else {
        orderData.value.releaseAmount = item.releaseAmount
      }
      // 是否展示佣金
      showBrokerageAmount.value = item.allowBrokerage === '1' && networkInfo.value.allowBrokerage === '1'
      // 是否需填写寄件人身份证号
      if (item.needSendIdCard === '1') {
        showIdCard.value = true
        showIdCardStyle.value = 'padding-top: 3vh'
      } else {
        showIdCard.value = false
        showIdCardStyle.value = ''
      }
      // 是否展示其他费用
      showOtherAmount.value = item.allowOtherAmount === '1'
      // 是否展示优惠金额
      showDiscountAmount.value = item.allowDiscountAmount === '1'
		} else {
			orderData.value.logisticsCompaniesId = undefined
      showIdCard.value = false
      showIdCardStyle.value = ''
      showBrokerageAmount.value = false
    }
    // 物流公司费用配置发生变化，重新计算金额
    onAmountChange('', 0)
    // 获取到达站
    getArriveDept(false).then(() => {
      // 获取标准运费
      getFreightSchema()
    })

		if (item.partners === '0') {
			radiosPartnersTrue.value.map((obj, inx) => {
				obj.checked = false
			})
			radiosPartnersFalse.value.map((obj, inx) => {
				if (index != inx) {
					obj.checked = false
				}
			})
		} else if (item.partners === '1') {
			radiosPartnersFalse.value.map((obj, inx) => {
				obj.checked = false
			})
			radiosPartnersTrue.value.map((obj, inx) => {
				if (index != inx) {
					obj.checked = false
				}
			})
		}
	} else if (type === 'arriveDeptId') { // 到站
    //item.checked = !item.checked
    //orderData.value.arriveDeptId = item.checked ? item.value : undefined
    orderData.value.arriveDeptId = item.value
    arriveInfo.value = item
    arriveInfoList.value.map((obj, inx) => {
      obj.checked = index != inx ? false : true
    })
  } else if (type === 'orderConstant') {
    // 常开订单
    item.checked = !item.checked
    orderConstantList.value.map((obj, inx) => {
      obj.checked = index != inx ? false : item.checked
    })
    if (item.checked) {
      // 填充表单数据
      if (item.sendName) orderData.value.sendName = item.sendName
      if (item.sendPhone) orderData.value.sendPhone = item.sendPhone
      if (item.sendIdCard) orderData.value.sendIdCard = item.sendIdCard
      if (item.sendAddress) orderData.value.sendAddress = item.sendAddress
      if (item.collectName) orderData.value.collectName = item.collectName
      if (item.collectPhone) orderData.value.collectPhone = item.collectPhone
      if (item.collectAddress) orderData.value.collectAddress = item.collectAddress
      if (item.goodsType) orderData.value.goodsType = item.goodsType
      if (item.goodsTypeLabel) orderData.value.goodsTypeLabel = item.goodsTypeLabel
      if (item.totalNum) orderData.value.totalNum = item.totalNum
      if (item.totalVolume) orderData.value.totalVolume = item.totalVolume
      if (item.totalWeight) orderData.value.totalWeight = item.totalWeight
      if (item.goodsLong) orderData.value.goodsLong = item.goodsLong
      if (item.goodsWidth) orderData.value.goodsWidth = item.goodsWidth
      if (item.goodsHeight) orderData.value.goodsHeight = item.goodsHeight
      if (item.packMethod) {
        orderData.value.packMethod = item.packMethod
        // 选中包装方式
        packMethodList.value.map(item => {
          item.checked = orderData.value.packMethod === item.value
        })
      }
    } else {

    }
  }

}
// 处理提交的订单数据
const genOrderFormData = () => {
  if (orderData.value.releaseMethod === undefined || orderData.value.releaseMethod === null || orderData.value.releaseMethod === '') {
    orderData.value.releaseMethod = '2'
  }
  if (orderData.value.deliveryMethod !== '2') {
    // 不送货-送货费置0
    orderData.value.deliveryAmount = 0
  }
  // 不是等通知放货-控货费置0
  if (orderData.value.releaseMethod !== '1') {
    orderData.value.releaseAmount = 0
  }
  // 不回单-回单费置0
  if (orderData.value.isReceipt !== '1') {
    orderData.value.receiptAmount = 0
  }
  // 不展示佣金-佣金置0
  if (!showBrokerageAmount.value) {
    orderData.value.brokerageAmount = 0
  }
  // 不展示其他费用-置0
  if (!showOtherAmount.value) {
    orderData.value.otherAmount = 0
  }
  // 不展示优惠金额-置0
  if (!showDiscountAmount.value) {
    orderData.value.discountAmount = 0
  }
  // 到站信息
  orderData.value.discDeptId = arriveInfo.value.arriveDeptId
  orderData.value.discDeptName = arriveInfo.value.arriveDeptName
  orderData.value.discDeptMobile = arriveInfo.value.arriveDeptMobile
  orderData.value.discDeptAddr = arriveInfo.value.arriveDeptAddr
  orderData.value.destDeptId = arriveInfo.value.destDeptId
  orderData.value.destDeptName = arriveInfo.value.destDeptName
}

// 改单申请form
const updateFormData = ref({
  logisticsNetworkId: undefined,
  orderCode: undefined,
  waybillCode: undefined,
  applyReason: undefined,
})
const submitUpdateForm = async () => {
  if (!updateFormData.value.applyReason) {
    modal.msg('请输入申请原因')
    return false
  }
  if (paramCheck()) {
    await uni.showLoading({
      title: '正在提交',
      mask: true
    })
    genOrderFormData() // 处理修改的订单数据
    updateFormData.value.orderInfo = orderData.value
    try {
      await createOrderUpdateApply(updateFormData.value).then(res => {
        uni.hideLoading()
        modal.msgSuccess('操作成功')
        resultFn()
        uni.navigateBack()
      })
    } catch(e){
      console.log("提交失败", e)
    } finally {
      try {
        uni.hideLoading()
      } catch (e) {}
    }
  }
}

// 参数校验
function paramCheck() {
  if (!arriveInfo.value.arriveDeptName || !arriveInfo.value.arriveDeptId) {
    modal.msg('未匹配到到达站，请重新核对收货地址')
    return false
  }
  if (!orderData.value.insuredAmount) {
    orderData.value.insuredAmount = 0
  }
	if (!orderData.value.sendName) {
		setTimeout(() => {
			modal.msg("请输入寄件人姓名")
		}, 500)
		return false
	} else if (!orderData.value.sendPhone) {
		setTimeout(() => {
			modal.msg("请输入寄件人手机号")
		}, 500)
		return false
	} else if (!orderData.value.sendIdCard && showIdCard.value) {
		setTimeout(() => {
			modal.msg("请输入寄件人身份证号")
		}, 500)
		return false
	} else if (!orderData.value.sendAddress) {
		setTimeout(() => {
			modal.msg("请输入寄件人地址")
		}, 500)
		return false
	} else if (!orderData.value.collectName) {
		setTimeout(() => {
			modal.msg("请输入收件人姓名")
		}, 500)
		return false
	} else if (!orderData.value.collectPhone) {
		setTimeout(() => {
			modal.msg("请输入收件人手机号")
		}, 500)
		return false
	} else if (!orderData.value.collectAddress) {
		setTimeout(() => {
			modal.msg("请输入收件人地址")
		}, 500)
		return false
	} else if (!orderData.value.goodsTypeLabel) {
		setTimeout(() => {
			modal.msg("请选择物品信息")
		}, 500)
		return false
	} else if (!orderData.value.logisticsCompaniesId) {
		setTimeout(() => {
			modal.msg("请选择物流公司")
		}, 500)
		return false
	} else if (!orderData.value.payMethod) {
		setTimeout(() => {
			modal.msg("请选择付款方式")
		}, 500)
		return false
	} else if (!orderData.value.packMethod) {
		setTimeout(() => {
			modal.msg("请选择包装方式")
		}, 500)
		return false
	} else if (!orderData.value.totalVolume || getNumber(orderData.value.totalVolume) === 0) {
    setTimeout(() => {
      modal.msg("请输入货品体积")
    }, 500)
    return false
  } else if (!orderData.value.totalWeight || getNumber(orderData.value.totalWeight) === 0) {
		setTimeout(() => {
			modal.msg("请输入货品重量")
		}, 500)
		return false
	} else if (!orderData.value.totalNum || getNumber(orderData.value.totalNum) === 0) {
		setTimeout(() => {
			modal.msg("请输入货品件数")
		}, 500)
		return false
	} else if (orderData.value.deliveryMethod === '2' && orderData.value.deliveryAmount < 0) {
    setTimeout(() => {
      modal.msg("请输入送货费")
    }, 500)
    return false
	} else if (orderData.value.releaseMethod === '1' && orderData.value.releaseAmount < 0) {
    setTimeout(() => {
      modal.msg("请输入控货费")
    }, 500)
    return false
	} else if (orderData.value.receiptType && orderData.value.receiptAmount < 0) {
    setTimeout(() => {
      modal.msg("请输入回单费")
    }, 500)
    return false
	} else if (showCollectionDeliveryInfo.value && !orderData.value.collectionName) {
    setTimeout(() => {
      modal.msg("请输入收款人")
    }, 500)
    return false
  } else if (showCollectionDeliveryInfo.value && !orderData.value.collectionBankName) {
    setTimeout(() => {
      modal.msg("请输入收款行")
    }, 500)
    return false
  } else if (showCollectionDeliveryInfo.value && !orderData.value.collectionCardNum) {
    setTimeout(() => {
      modal.msg("请输入收款卡号")
    }, 500)
    return false
  } else if (!orderData.value.amountFreight) {
    setTimeout(() => {
      modal.msg("请输入运费")
    }, 500)
    return false
  } else if (showBrokerageAmount.value && orderData.value.brokerageAmount < 0) {
    setTimeout(() => {
      modal.msg("请输入佣金")
    }, 500)
    return false
  } else if (!updateFormData.value.applyReason) {
    setTimeout(() => {
      modal.msg("请输入申请原因")
    }, 500)
    return false
  } else {
		return true
	}
}
const customCardBodyItemInput = reactive({
	'padding': '0'
})
// 重置表单
const resultFn = () => {
  const lastSendName = orderData.value.sendName
  const lastSendPhone = orderData.value.sendPhone
  const lastSendIdCard = orderData.value.sendIdCard
  const lastSendAddress = orderData.value.sendAddress
	orderData.value = {
		id: undefined,
		orderCode: undefined,
		collectName: undefined,
		collectPhone: undefined,
		collectAddress: undefined,
    sendAddressOne: undefined,
    sendAddressTwo: undefined,
    sendAddressThree: undefined,
    sendAddressFour: undefined,
    sendAddressFive: undefined,
    collectAddressOne: undefined,
    collectAddressTwo: undefined,
    collectAddressThree: undefined,
    collectAddressFour: undefined,
    collectAddressFive: undefined,
		sendName: lastSendName,
		sendPhone: lastSendPhone,
    sendIdCard: lastSendIdCard,
		sendAddress: networkInfo.value.address,
		goodsType: undefined,
		totalWeight: 1,
		totalVolume: 1,
		totalNum: 1,
		goodsLong: undefined,
		goodsWidth: undefined,
		goodsHeight: undefined,
		payMethod: 'PAY_METHOD_SEND_PAY_SETTLE',
		shouldPayAmount: undefined,
		actualPayAmount: undefined,
    amountFreight: undefined,
    totalAmount: 0,
		logisticsCompaniesId: undefined,
		logisticsNetworkId: undefined,
		orderStatus: undefined,
		logisticsStatus: undefined,
		orderStartTime: undefined,
		orderEndTime: undefined,
		goodsInfo: undefined,
		isReceipt: '0',
		deliveryMethod: '1',
		releaseMethod: '2',
		packMethod: undefined,
		collectionDelivery: undefined,
    collectionName: undefined,
    collectionBankName: undefined,
    collectionCardNum: undefined,
		remark: undefined,
		deliveryAmount: undefined,// 送货费
		insuredAmount: undefined,// 保价
    amountZdf: undefined,// 制单费
    rateBzf: 1,
    receiptType: '1',// 回单类型（1-电子回单 2-纸质回单）
    receiptAmount: undefined,// 回单费
    releaseAmount: undefined,// 控货费
    insuredPayAmount: undefined,// 支付的保价费
    brokerageAmount: undefined,// 佣金
    brokerageBackType: '1',// 佣金返款方式（1-现返 2-欠返）
    brokerageBackTypeLabel: '现返',// 佣金返款方式（1-现返 2-欠返）
    otherAmount: undefined,// 其他费用
    discountAmount: undefined,// 优惠金额
    collectionPayAmount: undefined,// 代收手续费
    collectionProcRate: 0,// 代收款手续费率(‰)
    collectionProcMin: undefined,// 代收款手续费起步价
    collectionProcMax: undefined,// 代收款手续费封顶价
	}
	isReceiptList.value = [{ // 是否回单
		name: '是',
		value: '1',
		dictType: 'Receipt',
		checked: false
	},
	{
		name: '否',
		value: '0',
		dictType: 'Receipt',
		checked: true
	}
	]
	deliveryMethodList.value = [{ // 送货方式
		name: '自提',
		value: '1',
		dictType: 'Delivery',
		checked: true
	},
	{
		name: '送货',
		value: '2',
		dictType: 'Delivery',
		checked: false
	}
	]
	releaseMethodList.value = [{ // 放货方式
		name: '等通知',
		value: '1',
		dictType: 'Release',
		checked: false
	}
	]
  receiptTypeList.value = [{ // 回单类型
    name: '电子回单',
    value: '1',
    dictType: 'receiptType',
    checked: true
  },
    {
      name: '纸质回单',
      value: '2',
      dictType: 'receiptType',
      checked: false
    }
  ]
	packMethodList.value.map(item => {
		item.checked = false
	})
	if (radiosPayList.value) {
		radiosPayList.value.map(item => {
			item.checked = item.value === 'PAY_METHOD_SEND_PAY_SETTLE'
		})
	}
	if (radiosPartnersTrue.value) {
		radiosPartnersTrue.value.map(item => {
			item.checked = false
		})
	}
  // 隐藏身份证号
  showIdCard.value = false
  showIdCardStyle.value = ''
  // 隐藏代收信息
  showCollectionDeliveryInfo.value = false
  // 隐藏佣金
  showBrokerageAmount.value = false
  getOrderListApi() // 最近订单
}
const onHelpClick = () => {
  showAmountDetail.value = true;
}
// 滚动表单到键盘高度，否则输入框会被键盘遮挡
const onInputFocus = () => {
	// 获取系统信息
	const systemInfo = uni.getSystemInfoSync();
	const keyboardHeight = systemInfo.keyboardHeight || 0; // 获取键盘高度

	// 向上滚动 scroll-view
	if (scrollViewRef.value) {
		scrollViewRef.value.scrollTo({
			top: keyboardHeight, // 滚动高度为键盘高度
			duration: 300 // 滚动动画持续时间
		});
	}
}
/**
 * 金额变化时自动计算总金额
 * @param type
 * @param value
 */
const onAmountChange = (type, value) => {
  value = getNumber(value)
  if (value < 0) {
    value = 0
  }
  if (type === 'deliveryAmount') {
    // 送货费
    orderData.value.deliveryAmount = value
  } else if (type === 'releaseAmount') {
    // 控货费
    orderData.value.releaseAmount = value
  } else if (type === 'receiptAmount') {
    // 回单费
    orderData.value.receiptAmount = value
  } else if (type === 'insuredAmount') {
    // 保价
    orderData.value.insuredAmount = value
  } else if (type === 'collectionDelivery') {
    // 代收货款
    orderData.value.collectionDelivery = value
  } else if (type === 'amountFreight') {
    // 运费
    orderData.value.amountFreight = value
  } else if (type === 'brokerageAmount') {
    // 佣金
    orderData.value.brokerageAmount = value
  } else if (type === 'otherAmount') {
    // 其他费用
    orderData.value.otherAmount = value
  } else if (type === 'discountAmount') {
    // 优惠金额
    orderData.value.discountAmount = value
  }

  // 送货费
  let deliveryAmount = getNumber(orderData.value.deliveryAmount)
  // 控货费
  let releaseAmount = getNumber(orderData.value.releaseAmount)
  // 回单费
  let receiptAmount = getNumber(orderData.value.receiptAmount)
  // 保价
  let insuredAmount = getNumber(orderData.value.insuredAmount)
  // 代收货款
  let collectionDelivery = getNumber(orderData.value.collectionDelivery)
  // 运费
  let amountFreight = getNumber(orderData.value.amountFreight)
  // 佣金
  let brokerageAmount = getNumber(orderData.value.brokerageAmount)
  // 其他费用
  let otherAmount = getNumber(orderData.value.otherAmount)
  // 优惠金额
  let discountAmount = getNumber(orderData.value.discountAmount)
  // 制单费
  let zdf = getNumber(orderData.value.amountZdf)

  // 不送货-送货费置0
  if (orderData.value.deliveryMethod !== '2') {
    deliveryAmount = 0
  }
  // 不是等通知放货-控货费置0
  if (orderData.value.releaseMethod !== '1') {
    releaseAmount = 0
  }
  // 不回单-回单费置0
  if (orderData.value.isReceipt !== '1') {
    receiptAmount = 0
  }
  // 不展示佣金-置0
  if (!showBrokerageAmount.value) {
    brokerageAmount = 0
  }
  // 不展示其他费用-置0
  if (!showOtherAmount.value) {
    otherAmount = 0
  }
  // 不展示优惠金额-置0
  if (!showDiscountAmount.value) {
    discountAmount = 0
  }

  // 保价费向上取整  保价 x 保价费率
  let insuredPayAmount = Math.ceil(insuredAmount * orderData.value.rateBzf / 1000)
  orderData.value.insuredPayAmount = insuredPayAmount

  // 代收手续费
  let collectionPayAmount = Math.ceil(collectionDelivery * orderData.value.collectionProcRate / 1000)
  // 代收货款
  if (!collectionDelivery || collectionDelivery === 0) {
    showCollectionDeliveryInfo.value = false
  } else {
    showCollectionDeliveryInfo.value = true
    if (orderData.value.collectionProcMin && collectionPayAmount < orderData.value.collectionProcMin) {
      collectionPayAmount = orderData.value.collectionProcMin
    } else if (orderData.value.collectionProcMax && collectionPayAmount > orderData.value.collectionProcMax) {
      collectionPayAmount = orderData.value.collectionProcMax
    }
  }
  orderData.value.collectionPayAmount = collectionPayAmount

  // 佣金、代收手续费不参与计算
  // 费用合计 = 送货费 + 控货费 + 回单费 + 保价费 + 运费 + 其他费用 + 制单费 + 佣金
  let addAmount = deliveryAmount + releaseAmount + receiptAmount + insuredPayAmount
       + amountFreight + otherAmount + zdf + brokerageAmount
  if (addAmount < discountAmount) {
    modal.msg('优惠金额不能大于总金额')
    discountAmount = 0
    orderData.value.discountAmount = 0
  }
  // 费用合计
  orderData.value.totalAmount = addAmount
  // 实付金额 = 费用合计 - 优惠金额
  orderData.value.actualPayAmount = addAmount - discountAmount
  orderData.value.shouldPayAmount = orderData.value.actualPayAmount
}

function getNumber(number) {
  let result
  if (number === '' || number === undefined || number === null) {
    result = 0
  } else {
    try {
      result = Number(number)
    } catch (error) {
      result = 0
    }
  }
  if (isNaN(number)) {
    result = 0
  }
  return result
}
// 数值输入格式化
const onNumberChange = (type, pointNum, value) => {
  let result = formatMoney(value, pointNum)
  if (result < 0) {
    result = 0
  }
  console.log(value, '->', result)
  if (type === 'totalVolume') {
    orderData.value.totalVolume = result
  } else if (type === 'totalWeight') {
    orderData.value.totalWeight = result
  } else if (type === 'totalNum') {
    // 件数最小为1
    if (result < 1) {
      result = 1
    }
    orderData.value.totalNum = result
  }
}

// 初始化
onMounted(() => {
  console.log('onMounted')
	getDictDataInfo('PAY_METHOD')
  getOrderConstantListApi() // 常发订单
  getOrderListApi() // 最近订单
  // 接收数据
  eventChannel.on('parentPageData', async function (data) {
    if (data) {
      updateFormData.value = {
        logisticsNetworkId: data.logisticsNetworkId,
        orderCode: data.orderCode,
        waybillCode: data.waybillCode,
        applyReason: undefined,
      }
      await getOrderShowInfo(data.orderCode).then(async (res) => {
        if (res && res.data) {
          if (res.data.logisticsNetworkInfo) {
            // 网点信息
            networkInfo.value.id = res.data.logisticsNetworkInfo.id
            networkInfo.value.name = res.data.logisticsNetworkInfo.name
            networkInfo.value.mobile = res.data.logisticsNetworkInfo.principalPhone
            networkInfo.value.address = res.data.logisticsNetworkInfo.address
            networkInfo.value.allowBrokerage = res.data.logisticsNetworkInfo.allowBrokerage
          }
          if (res.data.logisticsCompaniesInfo) {
            // 物流公司信息
            radiosPartnersTrue.value.push(res.data.logisticsCompaniesInfo)
          }
          // console.log('getOrderShowInfo', res.data)
          await fillFormData(res.data, true) // 回显填充表单
        }
      })
    }
  })

})

onLoad(() => {
  console.log('onLoad')
})
onUnload(() => {

});

// 常开订单弹窗的显隐
const handleOrderConstantSettingClick = () => {
  showOrderConstant.value = !showOrderConstant .value
  if (showOrderConstant.value) {
    customLayoutHomeStyle.value = {
      'overflow': 'hidden'
    }
  } else {
    customLayoutHomeStyle.value = {
      'overflow': 'auto'
    }
  }
}

// 常开订单编辑
const handleOrderConstantEditClick = (data) => {
  uni.navigateTo({
    url: '/pages/wljh/sub_pages/orderConstantSave',
    events: {
      // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
      orderConstantChild: function (data) {
        modal.msgSuccess('保存成功')
        // 重新获取常发订单数据
        getOrderConstantListApi()
      }
    },
    success: function (res) {
      res.eventChannel.emit('orderConstantParent', data)
    }
  })
}
// 常开订单删除
const handleOrderConstantDeleteClick = (data) => {
  modal.confirm('确定删除选中数据？').then(res => {
    deleteOrderConstantApp(data.id).then(res => {
      if (res.code === 0) {
        modal.msgSuccess('删除成功')
        getOrderConstantListApi()
      }
    })
  })
}
// 常开订单新增
const handleOrderConstantAddClick = (data) => {
 addOrderConstantApp(data).then(res => {
   if (res.code === 0) {
     modal.msgSuccess('添加成功')
     getOrderConstantListApi()
   }
 })
}

const lastTapDiffTime = ref(0)
const lastTapTimeoutFunc = ref(undefined)
// 双击填充表单
const dbClickFillForm = (data) => {
  let curTime = new Date().getTime();
  let lastTime = lastTapDiffTime.value;
  lastTapDiffTime.value = curTime;
  //两次点击间隔小于300ms, 认为是双击
  let diff = curTime - lastTime;
  console.log("diff:" + diff)
  if (diff < 300) {
    console.log("双击")
    clearTimeout(lastTapTimeoutFunc.value); // 成功触发双击事件时，取消单击事件的执行
    let dbClickFillData = {...data}
    dbClickFillData.id = undefined
    dbClickFillData.orderCode = undefined
    dbClickFillData.waybillCode = undefined
    fillFormData(dbClickFillData, false) // 双击后填充表单
    handleOrderConstantSettingClick() // 关闭常开订单弹窗
  } else {
    // 单击事件延时300毫秒执行
    lastTapTimeoutFunc.value = setTimeout(function() {
      console.log("单击")
      //_this.handleVideo('playOrStop',index)自定义事件
    }, 300);
  }
}

// 填充表单
const fillFormData = async (data, isFirst) => {
  data.taxAmount = 0
  // 填充表单
  orderData.value = data
  // 选中包装方式
  packMethodList.value.map(item => {
    item.checked = orderData.value.packMethod === item.value
  })
  // 选中物流公司
  radiosPartnersTrue.value.map(item => {
    if (item.id === orderData.value.logisticsCompaniesId) {
      item.checked = true
      // 是否展示佣金
      showBrokerageAmount.value = item.allowBrokerage === '1' && networkInfo.value.allowBrokerage === '1'
      // 是否需填写寄件人身份证号
      if (item.needSendIdCard === '1') {
        showIdCard.value = true
        showIdCardStyle.value = 'padding-top: 3vh'
      } else {
        showIdCard.value = false
        showIdCardStyle.value = ''
      }
    } else {
      item.checked = false
    }
  })
  // 选中付款方式
  radiosPayList.value.map(item => {
    item.checked = orderData.value.payMethod === item.value
  })
  // 选中送货方式
  deliveryMethodList.value.map(item => {
    item.checked = orderData.value.deliveryMethod === item.value
  })
  // 选中放货方式
  releaseMethodList.value.map(item => {
    item.checked = orderData.value.releaseMethod === item.value
  })
  // 选中回单类型
  receiptTypeList.value.map(item => {
    item.checked = orderData.value.receiptType === item.value
  })
  onAmountChange('', 0)
  // 获取到达站
  await getArriveDept(isFirst).then(() => {
    // 获取标准运费
    getFreightSchema()
  })
}

//  禁寄货品点击
const handlerBanGoods = () => {
  showBanGoods.value = !showBanGoods.value
}

// 地址
const showSendAddressSelect = ref(false)
const sendAddressSelectList = ref([])
const showCollectAddressSelect = ref(false)
const collectAddressSelectList = ref([])
// 发货地址变化
watch(() => orderData.value.sendAddress, (newVal, oldVal) => {
  sendAddressSelectList.value = []
  if (!orderData.value.sendAddress || orderData.value.sendAddress === '') {
    return
  }
  likeSearchAddress(orderData.value.sendAddress, 10).then(res => {
    if (res.data) {
      sendAddressSelectList.value = res.data
    }
  })
})
// 收货地址变化
watch(() => orderData.value.collectAddress, (newVal, oldVal) => {
  collectAddressSelectList.value = []
  if (!orderData.value.collectAddress || orderData.value.collectAddress === '') {
    return
  }
  likeSearchAddress(orderData.value.collectAddress, 10).then(res => {
    if (res.data) {
      collectAddressSelectList.value = res.data
    }
  })
  if (oldVal !== 'null') {
    // 获取到达站
    getArriveDept(false).then(() => {
      // 获取标准运费
      getFreightSchema()
    })
  }
})
// 总重量、总件数、总体积变化
watch(() => {
  return {
    totalWeight: orderData.value.totalWeight,
    totalNum: orderData.value.totalNum,
    totalVolume: orderData.value.totalVolume
  }
}, (newVal, oldVal) => {
  // 获取标准运费
  getFreightSchema()
})

/** 查找到站和目的地 */
const arriveInfoList = ref([])
// 到站和目的地
const arriveInfo = ref({
  arriveDeptName: undefined,
  arriveDeptId: undefined,
  arriveDeptMobile: undefined,
  arriveDeptAddr: undefined,
  destDeptName: undefined,
  destDeptId: undefined,
})
const getArriveDept = async (isFirst) => {
  arriveInfoList.value = []
  arriveInfo.value = {
    arriveDeptName: undefined,
    arriveDeptId: undefined,
    arriveDeptMobile: undefined,
    arriveDeptAddr: undefined,
    destDeptName: undefined,
    destDeptId: undefined,
  }
  const companyId = orderData.value.logisticsCompaniesId
  const networkId = networkInfo.value.id
  const address = orderData.value.collectAddress
  if (companyId && networkId && address) {
    await getArriveDeptListApp(companyId, networkId, address).then(res => {
      if (res.data) {
        arriveInfoList.value = res.data
        arriveInfoList.value.map(item => {
          if (item.destDeptName) {
            item.showText = item.arriveDeptName + '->' + item.destDeptName
          } else {
            item.showText = item.arriveDeptName
          }
        })
        if (isFirst) {
          // 首次进入回显选中到站
          arriveInfoList.value.map(item => {
            if (item.arriveDeptId === orderData.value.discDeptId) {
              arriveInfo.value = item
              item.checked = true
            } else {
              item.checked = false
            }
          })
        } else {
          // 默认选中第一个
          arriveInfo.value = arriveInfoList.value[0]
          arriveInfoList.value[0].checked = true
        }
      } else {
        modal.msg("未匹配到到达站，请重新核对收货地址")
      }
      // console.log("arriveInfo", arriveInfo.value)
    })
  }
}
/** 获取标准运费 */
// 标准运费
const freightSchema = ref({
  freightSchemeLow: 0
})
const getFreightSchema = async () => {
  freightSchema.value = {
    freightSchemeLow: 0
  }
  const companyId = orderData.value.logisticsCompaniesId
  const networkId = networkInfo.value.id
  const arriveDeptId = arriveInfo.value.arriveDeptId
  if (companyId && networkId && arriveDeptId) {
    await getFreightSchemaApp({
      "companyId": companyId,
      "networkId": networkId,
      "billDeptId": null,
      "arriveDeptId": arriveDeptId,
      "destDeptId": arriveInfo.value.destDeptId,
      "totalWeight": getNumber(orderData.value.totalWeight),
      "totalVolume": getNumber(orderData.value.totalVolume),
      "totalNum": getNumber(orderData.value.totalNum)
    }).then(res => {
      if (res.data) {
        freightSchema.value = res.data
        if (!freightSchema.value.freightSchemeLow) {
          freightSchema.value.freightSchemeLow = 0
        }
      }
    })
  }

}


// 自定义页面样式
const customLayoutHomeStyle = ref({
  'overflow': 'auto'
})
// 不参与计算的金额
const unCalcCellTitleStyle = reactive({
  'color': '#036df7'
})
// 合计金额
const detailTotalCellTitleStyle = reactive({
  'font-weight': '600'
})
// 优惠金额
const discountCellTitleStyle = reactive({
  'color': '#08b926'
})

</script>

<style scoped lang="scss">
.layout-home {
	background-color: #f1f1f1;
	// height: 100vh;
	height: calc(100vh - var(--window-bottom) - var(--window-top) - 20rpx);
	overflow: auto;

	.custom-card-body {
    background-color: #ffffff;
    border-radius: 5px;
    padding: 0 20rpx 0 20rpx;
		.custom-card-body-item {
			display: flex;
			align-items: center;

      .left-label {
        width: 180rpx;

        .left-label-right {
          float: right;
          margin-top: 2rpx;
        }
      }

			.custom-card-body-item-lable {
				font-size: 15px;
				margin-right: 6rpx;
			}

			.custom-card-body-item-tag {
				margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 15px;
				}
        :deep(.u-tag--mini) {
          height: 34px!important;
          padding: 0 10rpx!important;
        }
			}

			:deep(.u-scroll-list) {
				width: calc(100% - 180rpx);
				padding-bottom: 0;
			}
		}
    .address-combox {

      :deep(.uni-combox) {
        border: none;
        padding: 0;
        width: 100%;
      }

      :deep(.uni-combox__input>.uni-input-wrapper>.uni-input-input) {
        color: #000000!important;
        font-size: 15px !important;
      }
      :deep(.uni-combox__input-box) {
        :deep(.uni-icons) {
          display: none;
        }
      }
      :deep(.uni-combox__selector-scroll) {
        padding: 0 10px;
      }
      :deep(.uni-combox__selector-item) {
        text-align: left;
        line-height: 16px;
        padding: 5px 0;
        border-bottom: 1px solid #f4f4f5;
      }
    }

    .order-constant-row {
      :deep(.u-scroll-list) {
        width: calc(100% - 110rpx) !important;
      }
    }
	}

	.custom-card-foot {
		padding: 0 20rpx 26rpx 20rpx;

		:deep(.u-button) {
			height: 45px;
		}

		:deep(.u-button--circle) {
			background-color: #d81e06;
			border-color: #d81e06;
		}

		:deep(.u-button--circle) {
			border-radius: 30px;
		}
	}

	.custom-home-bottom {
		// height: 120rpx;
		margin: 0 20rpx;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		background-color: #fff;
		border-radius: 5px;

		:deep(.u-scroll-list) {
			background-color: #fff;
			border-radius: 5px;
			padding-bottom: 0;
		}

		.custom-home-bottom-tag {
			// height: 100rpx;
			margin: 0 20rpx;
			// line-height: 100rpx;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			column-gap: 20rpx;
			row-gap: 20rpx;
			padding: 20rpx 0;

			:deep(.u-tag--large) {
				height: 43px;
				line-height: 43px;
				padding: 0 15px;
			}
		}
	}

	.fw600 {
		font-weight: 600;
	}

	.border-bottom-line {
		border-bottom: 1px solid #f4f4f5;
	}

	.custom_line {
		// margin-bottom: 10px;
		// padding-bottom: 5px;
		// padding: 40rpx 20rpx;
		padding: 20rpx 0;
	}

	.custom_required_css {
		font-size: 8px !important;
		color: red;
		font-weight: 400;
	}

	:deep(.u-tag__text--primary) {
		white-space: nowrap;
	}

	:deep(.u-button__text) {
		font-size: 15px !important;
	}

	:deep(.u-tag--mini) {
		height: 43px;
		line-height: 43px;
		padding: 0 15px;
		min-width: 92px;
		display: flex;
		justify-content: center;
	}

	:deep(.u-tag--primary) {
		background-color: #d81e06;
	}
}
.constant-view {
  height: 0;
  //position: absolute;
  width: 100vw;
  color: #606266;

  .constant-pop {
    transition-duration: 350ms;
    transition-timing-function: ease-out;
    position: relative;
    height: calc(100vh - var(--window-bottom) - var(--window-top));
    inset: 0px;
    z-index: 5;
    background-color: #00000080;
    top: calc(100rpx);
    padding: 0 20rpx 20rpx 20rpx;

    .constant-pop-content {
      z-index: 6;
      background-color: #ffffff;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      padding: 0 20rpx 20rpx 20rpx;
      height: calc(100vh - var(--window-bottom) - var(--window-top) - 650rpx);
      overflow: auto;

      .constant-pop-content-title {
        padding-top: 20rpx;
        font-size: 16px ;
      }

      .constant-pop-content-data {
        padding: 20rpx 0;
        border-bottom: 1px solid #f4f4f5;
      }
    }

    .constant-icon-edit {
      float: right;
      margin-top: 2rpx;
    }

  }

  .order-constant-scroll {
    padding-left: 40rpx!important;
  }
}

.ban-goods-pop {
  height: calc(100vh - var(--window-bottom) - var(--window-top) - 200rpx);
  overflow: auto;

  .ban-goods-pop-title {
    width: 100vw;
    font-weight: 600;
    font-size: 18px;
    position: absolute;
    background-color: #ffffff;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    padding: 20rpx;
    z-index: 100;
  }

  .ban-goods-pop-h {
    font-weight: 600;
    padding: 10rpx 20rpx;
  }
  .ban-goods-pop-t {
    padding: 10rpx 20rpx;
  }

  .ban-goods-pop-btn {
    width: 100vw;
    position: absolute;
    background-color: #ffffff;
    bottom: 0;
    padding: 0 20rpx;
  }

  :deep(.u-popup__content__close) {
    z-index: 101;
  }
}

.goods-select{
  display: flex;
  overflow: auto;

  .goods-select-item {
    display: flex;
    height: 34px !important;
    padding: 0 10rpx !important;
  }

  :deep(.u-tag--mini) {
    height: 34px !important;
    padding: 0 10rpx !important;
    min-width: 34px!important;
  }

  :deep(.u-tag__text--mini) {
    font-size: 15px;
}
}

.goods-info-number {

  :deep(.u-number-box__input) {
    background-color: #ffffff!important;
  }

  :deep(.u-number-box__minus) {
    height: 25px!important;
    max-width: 25px!important;
  }
  :deep(.u-number-box__plus) {
    height: 25px!important;
    max-width: 25px!important;
  }

  :deep(.u-input) {
    min-width: 55px !important;
  }
  :deep(.uni-input-input) {
    font-size: 12px!important;
  }
  :deep(.u-input__content__subfix-icon) {
    font-size: 12px;
    margin-left: 0 !important;
  }

}
.goods-vol-detail {
  display: flex;
  align-items: center;

  :deep(.u-input) {
    padding: 3px 0!important
  }
}
.addressSelect {
  position: absolute;
  margin-top: -10px;
  width: 60%;
  z-index: 3;
  height: auto;
  max-height: 400rpx;
  background-color: #ffffff;
  border: 1px solid #EBEEF5;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  //border-top: none;
  overflow: auto;
  padding: 10rpx;

  .addressSelectItem {
    padding: 10rpx 0;
    border-bottom: 1px solid #f4f4f5;
    font-size: 15px;
  }
}
</style>