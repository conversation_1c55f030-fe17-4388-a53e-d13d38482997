<template>
	<view class="layout-mine">
		<view class="layout-mine-essential">
			<up-cell-group>
				<up-cell title="网点编号">
					<template #value>
						<up-text :text="dataParam.logisticsNetworkInfo.networkCode" align="right" color="#606266"></up-text>
					</template>
				</up-cell>
				<up-cell title="网点名称">
					<template #value>
						<up-text :text="dataParam.logisticsNetworkInfo.name" align="right" color="#606266"></up-text>
					</template>
				</up-cell>
        <!--<up-cell title="网点地址">
          <template #value>
            <up-text :text="dataParam.logisticsNetworkInfo.address" align="right" color="#606266"></up-text>
          </template>
        </up-cell>-->
        <up-cell title="网点认证" isLink @click="handleCertification(dataParam)"
                 :value="authApprovalStatus"></up-cell>

			</up-cell-group>
		</view>
		<view class="layout-mine-essential">
      <up-cell title="物流服务商配置" isLink @click="handleOuter" :name="dataParam.logisticsNetworkInfo.id"
               :value="dataParam.openOuterNum > 0 ? '已设置' : '未设置'" ></up-cell>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from "vue"
	import customStorage from "@/utils/customStorage"
	import {
		getUserInfo
	} from '@/api/wljh/mine'
  import {onLoad, onUnload} from "@dcloudio/uni-app";


	const dataParam = ref({
		id: undefined,
		headPortrait: undefined,
		nickname: undefined,
		account: undefined,
		logisticsNetworkInfo: {
			id: undefined,
			certification: '0',
			name: undefined,
			address: undefined,
      networkCode: undefined
		}
	})
  const printConfigStatus = ref()
	const defaultNickname = ref() //默认昵称
  // 认证审核状态
  const authApprovalStatus = ref('')

	// 实名认证事件
	const handleCertification = (obj) => {
		uni.navigateTo({
			url: '/page_mine/certification',
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        updateAuthApprovalStatus: function (data) {
          console.log(data)
          authApprovalStatus.value = '认证待审核'
        }
      },
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('params', {
          data: {
            networkId: dataParam.value.logisticsNetworkId,
          },
          type: 'edit'
        })
			}
		})
	}
	// 账号绑定事件
	const handleOuter = (obj) => {
		uni.navigateTo({
			url: '/pages/wljh/sub_mine/outer',
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('params', obj)
			}
		})
	}
	// 获取个人详细信息
	const getUserInfoApi = async (param) => {
		await getUserInfo(param).then(res => {
			if (res.code === 0) {
				dataParam.value = res.data
				// 设置头像
				/*if (!res.data.headPortrait) {
					dataParam.value.headPortrait = defaultHeader
				}*/
				defaultNickname.value = JSON.parse(JSON.stringify(res.data)).nickname
        // 实名认证：0-未认证 1-已认证 2-认证待审核 3-认证审核不通过
        if (res.data.logisticsNetworkInfo.certification === '1') {
          authApprovalStatus.value = '已认证'
        } else if (res.data.logisticsNetworkInfo.certification === '2') {
          authApprovalStatus.value = '认证待审核'
        } else if (res.data.logisticsNetworkInfo.certification === '3') {
          authApprovalStatus.value = '认证审核不通过'
        } else {
          authApprovalStatus.value = '未认证'
        }
			}
		})
	}
  // 打印机配置
  const getPrintConfigStatus = async () => {
    const blueInfo = customStorage.get("BlueInfo")
    const wifiInfo = customStorage.get("WifiInfo")
    if (!blueInfo && !wifiInfo) {
      printConfigStatus.value = '未设置'
    } else {
      printConfigStatus.value = '已设置'
    }
  }
	// 初始化页面
	const initPage = () => {
		const userInfo = customStorage.get("AccountInfo")
		if (userInfo) {
		}
			getUserInfoApi(userInfo)
    // 刷新打印机配置
    getPrintConfigStatus()
	}
	onMounted(() => {
		initPage()
	});
  onLoad(() => {
    // 监听事件
    uni.$on('printerChange',d=>{
      console.log(d.msg)
      // 刷新打印机配置
      getPrintConfigStatus()
    })
  });
  onUnload(() => {
    // 移除监听事件
    uni.$off('printerChange');
  });

</script>

<style scope lang="scss">
	.layout-mine {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-bottom) - var(--window-top));
		overflow: auto;
		padding: 20rpx;

		.layout-mine-essential {
			background-color: #fff;
			border-radius: 5px;
			margin-bottom: 10rpx;

			:deep(.u-cell__body) {
				height: 120rpx;
			}

			.custom-cell-title {
				display: flex;

				.custom-cell-title-item {
					margin-left: 26rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
				}
			}
		}
	}
</style>