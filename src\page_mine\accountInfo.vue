<template>
	<view class="layout-mine">
		<view class="layout-mine-essential" style="padding: 20rpx;">
      <up-text text="账户余额" size="13" align="center" color="#606266"
               customStyle="padding: 20rpx 0;"></up-text>
      <up-text :text="dataParam.accountTotal.totalAmount" size="15" mode="price" align="center" color="#333333"
               customStyle="padding: 20rpx 0;font-weight: 600;"></up-text>
      <up-row customStyle="padding: 20rpx 0 0 0;">
        <up-col :span="1">
        </up-col>
        <up-col :span="5" customStyle="display: flex;justify-content: center;">
          <up-text text="可用余额：" size="13" align="right" color="#606266"></up-text>
          <up-text :text="dataParam.accountTotal.availableAmount" mode="price" size="12" align="left" color="#606266"></up-text>
        </up-col>
        <up-col :span="5" customStyle="display: flex;justify-content: center;">
          <up-text text="冻结余额：" size="13" align="right" color="#606266"></up-text>
          <up-text :text="dataParam.accountTotal.freezeAmount" mode="price" size="12" align="left" color="#606266"></up-text>
        </up-col>
        <up-col :span="1">
        </up-col>
      </up-row>
      <up-row customStyle="padding:0 0 20rpx 0;">
        <up-col :span="2"></up-col>
        <up-col :span="8" >
            <up-text :text="'到付授信额度：' + formatDecimal(dataParam.accountTotal.overAllowAmount, 2)" size="13" align="center" color="#606266"></up-text>
        </up-col>

        <up-col :span="2"></up-col>
      </up-row>
      <up-row customStyle="padding: 20rpx 0;">
        <up-col :span="4"></up-col>
        <up-col :span="4">
          <up-text text="资金明细" size="13" align="center" color="#02A7F0" @click="handleAccountChangeLog"></up-text>
        </up-col>
        <up-col :span="4"></up-col>
      </up-row>
      <up-row customStyle="padding: 20rpx 0 0 0;">
        <up-col :span="4" customStyle="padding: 20rpx 30rpx;">
          <up-button :plain="true" text="提现申请" shape="circle"
                     customStyle="color:#000;height:35px;" @click="handleTakeCashApply"></up-button>
        </up-col>
        <up-col :span="4" customStyle="padding: 20rpx 30rpx;">
          <up-button :plain="true" text="充值申请" shape="circle"
                     customStyle="color:#fff;height:35px;backgroundColor: #d81e06;" @click="handleRechargeApply"></up-button>
        </up-col>
        <up-col :span="4" customStyle="padding: 20rpx 30rpx;">
          <up-button :plain="true" text="资金划转" shape="circle"
                     customStyle="color:#000;height:35px;" @click="handleAccountTransfer"></up-button>
        </up-col>
      </up-row>
    </view>
		<view class="layout-mine-essential">
			<up-cell-group>
        <up-cell title="交易密码" isLink @click="handleEditAccountPwd" :value="accountPwdStatusLabel"></up-cell>
        <up-cell title="提现码" isLink @click="handleReceiveCode" :value="receiveCodeStatusLabel"></up-cell>
      </up-cell-group>
		</view>
	</view>
</template>

<script setup>
import {
  ref,
  onMounted, getCurrentInstance
} from "vue"
	import customStorage from "@/utils/customStorage"
	import {
		getUserInfo
	} from '@/api/wljh/mine'
import {formatDecimal, formatMoney} from "@/api/common";

  const defaultHeader = ref('https://dachisc.wang:9000/wljh/4e29406c6510fb74ce5ce3538304a07b1dd7fa6333937e7da6b5e9384af75fb7.svg')

	const dataParam = ref({
		id: undefined,
		headPortrait: defaultHeader,
		nickname: undefined,
		account: undefined,
    accountList: [],
		accountTotal: {},
    levelInfo: {

    },
    logisticsNetworkId: undefined,
		logisticsNetworkInfo: {
			id: undefined,
			certification: '0',
			name: undefined,
			address: undefined,
      networkCode: undefined
		}
	})
	const defaultNickname = ref() //默认昵称
  const receiveCodeStatusLabel = ref('未设置')
  const accountPwdStatusLabel = ref('未设置')

	// 获取个人详细信息
	const getUserInfoApi = async (param) => {
		await getUserInfo(param).then(res => {
			if (res.code === 0) {
				dataParam.value = res.data
				// 设置头像
				if (!res.data.headPortrait) {
					dataParam.value.headPortrait = defaultHeader
				}
				defaultNickname.value = JSON.parse(JSON.stringify(res.data)).nickname
        if (dataParam.value.accountList && dataParam.value.accountList.length > 0) {
          dataParam.value.accountTotal = dataParam.value.accountList.find(item => item.accountType === 'TOTAL')
        }
        if (dataParam.value.logisticsNetworkInfo) {
          // 0-未提交审核 1-已审核 2-待审核 3-审核不通过
          if (dataParam.value.logisticsNetworkInfo.receiveCodeAudit === '0') {
            receiveCodeStatusLabel.value = '未提交审核'
          } else if (dataParam.value.logisticsNetworkInfo.receiveCodeAudit === '1') {
            receiveCodeStatusLabel.value = '已设置'
          } else if (dataParam.value.logisticsNetworkInfo.receiveCodeAudit === '2') {
            receiveCodeStatusLabel.value = '待审核'
          } else if (dataParam.value.logisticsNetworkInfo.receiveCodeAudit === '3') {
            receiveCodeStatusLabel.value = '审核不通过'
          }

          if (dataParam.value.logisticsNetworkInfo.accountPwdStatus === '1') {
            accountPwdStatusLabel.value = '已设置'
          }
        }
			}
		})
	}
  // 账户明细
  const handleAccountChangeLog = () => {
    uni.navigateTo({
      url: '/page_mine/accountChangeLog',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('accountChangeLogParams', {
          id: dataParam.value.logisticsNetworkId
        })
      }
    })
  }
  // 提现申请
  const handleTakeCashApply = () => {

    uni.navigateTo({
      url: '/page_mine/takeCashList',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('takeCashListParams', {
          id: dataParam.value.logisticsNetworkId
        })
      }
    })
  }
  // 充值申请
  const handleRechargeApply = () => {
    uni.navigateTo({
      url: '/page_mine/rechargeList',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('rechargeListParams', {
          id: dataParam.value.logisticsNetworkId
        })
      }
    })
  }
  // 资金划转
  const handleAccountTransfer = () => {
    uni.navigateTo({
      url: '/page_mine/accountTransferList',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('accountTransferListParams', {
          id: dataParam.value.logisticsNetworkId
        })
      }
    })
  }
  // 修改密码事件
  const handleEditAccountPwd = () => {
    uni.navigateTo({
      url: '/page_mine/editAccountPwd',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('params', {
          id: dataParam.value.logisticsNetworkId
        })
      }
    })
  }
  // 修改提现码
  const handleReceiveCode = () => {
    uni.navigateTo({
      url: '/page_mine/networkReceiveConfigForm',
      events: {
        childFn: function(data) {
          if (data) {
            // 刷新账户状态
            const userInfo = customStorage.get("AccountInfo")
            if (userInfo) {
            }
              getUserInfoApi(userInfo)
          }
        }
      },
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('params', {
          id: dataParam.value.logisticsNetworkId,
          receiveOne: dataParam.value.logisticsNetworkInfo.receiveOne,
          receiveTwo: dataParam.value.logisticsNetworkInfo.receiveTwo,
          receiveThree: dataParam.value.logisticsNetworkInfo.receiveThree,
          receiveFive: dataParam.value.logisticsNetworkInfo.receiveFive,
        })
      }
    })
  }
	// 初始化页面
	const initPage = () => {
		const userInfo = customStorage.get("AccountInfo")
		if (userInfo) {
		}
			getUserInfoApi(userInfo)
	}
	onMounted(() => {
		initPage()
	});

</script>

<style scope lang="scss">
	.layout-mine {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-bottom));
		overflow: auto;
		padding: 20rpx;

		.layout-mine-essential {
			background-color: #fff;
			border-radius: 5px;
			margin-bottom: 10rpx;

			:deep(.u-cell__body) {
				height: 120rpx;
			}

			.custom-cell-title {
				display: flex;

				.custom-cell-title-item {
					margin-left: 26rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
				}
			}
		}
	}
</style>