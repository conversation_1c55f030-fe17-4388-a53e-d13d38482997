<template>
	<view class="layout-page">
		<view class="text-content">
      <view class="text-p">暂无内容</view>
      <view class="text-line"></view>
    </view>
	</view>
</template>

<script setup>
</script>

<style scoped lang="scss">
	.layout-page {
		height: calc(100vh - var(--window-top));
		overflow: auto;
		background-color: #fff;

		.text-title {
			text-align: center;
			font-size: 20px;
			font-weight: 600;
			padding-bottom: 20rpx;
		}

		.text-content {
			padding: 20rpx;

			.text-p {
				font-size: 15px;
				line-height: 40rpx;
				text-indent: 40rpx;
			}
			
			.text-line {
				padding: 10rpx 0;
			}
			
			.align {
				text-align: right;
			}
		}
	}
</style>