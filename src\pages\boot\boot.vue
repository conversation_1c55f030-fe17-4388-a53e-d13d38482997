<template>
	<view class="boot-page">
		<view class="skip-button" @click="toIndex">
			跳过{{ countDown }}秒
		</view>
		<view class="logo">
			<image :src="imgUrl" />
		</view>
	</view>
</template>
<script setup>
	import { ref } from 'vue';
	import {getVersion,downloadProgress,formatBytes,formatTime} from "@/api/version/version.js"
	const countDown = ref(5)
	const imgUrl = ref("/static/wait/load1.png")
	const waitpngs = ["/static/wait/load1.png","/static/wait/load2.png","/static/wait/load3.png","/static/wait/load4.png"]
	//设置倒计时
	const trimer = setInterval(()=>{
		if(countDown.value <= 0){
			
			toIndex()
		}else{
			imgUrl.value = waitpngs[(countDown.value)%waitpngs.length]
			console.log(imgUrl)
			countDown.value = countDown.value - 1
		}
	},1000)
	const toIndex = ()=>{
		//跳转到首页的时候更新
		clearInterval(trimer)
		getVersion(true)
		uni.reLaunch({
			url:"/pages/wljh/home"
		})
	} 
</script>

<style scoped lang="scss">
	.boot-page{
		width: 100%;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		background-color: antiquewhite;
		//#ifdef MP-WEIXIN
		.skip-button{
			position: absolute;
			right: 10px;
			top: 10%;	
		}
		//#endif
		//#ifndef MP-WEIXIN
		.skip-button{
			position: absolute;
			right: 10px;
			top: 40px;	
		}
		//#endif
		
		.logo{
			width: 100px;
			height: 100px;
			image{
				width: 100%;
				height: 100%;	
				object-fit: cover;
			}
		}
	} 
</style>
