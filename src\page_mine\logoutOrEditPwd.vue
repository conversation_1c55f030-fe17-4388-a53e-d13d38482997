<template>
  <view class="layout-mine">
    <view class="layout-mine-essential">
      <up-cell-group>
        <up-cell title="修改登录密码" isLink @click="handleEditPassword(dataParam)"></up-cell>
      </up-cell-group>
    </view>
    <view style="padding-top: 10px;">
      <up-button plain text="注销账号" shape="circle" color="#b9bcc0" hairline
                 customStyle="color:#000;height:40px;" @click="handleLogout"></up-button>
    </view>
    <view class="text-center" style="padding-top: 10px;">
      <text style="color: rgb(119 120 122);font-size: 14px" >注销后无法恢复，请谨慎操作</text>
    </view>
  </view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref
	} from 'vue'
	import {
		updateUserInfo
	} from '@/api/wljh/mine'
	import modal from '@/plugins/modal'

  const defaultHeader = ref('https://dachisc.wang:9000/wljh/4e29406c6510fb74ce5ce3538304a07b1dd7fa6333937e7da6b5e9384af75fb7.svg')
	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

  const dataParam = ref({
    id: undefined,
    headPortrait: defaultHeader,
    nickname: undefined,
    account: undefined,
    logisticsNetworkInfo: {
      id: undefined,
      certification: '0',
      name: undefined,
      address: undefined,
      networkCode: undefined
    }
  })

	// 返回事件
	const handleBlack = () => {
		uni.navigateBack()
	}

  // 修改密码事件
  const handleEditPassword = (obj) => {
    uni.navigateTo({
      url: '/page_mine/editPassword',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('params', obj)
      }
    })
  }
  // 注销账号事件
  const handleLogout = () => {
    modal.confirm('确定注销账号？\n注销账号后，账号将无法使用，且无法恢复，请谨慎操作！').then(res => {
      updateUserInfo({
        id: dataParam.value.id,
        cancalStatus: 1
      }).then(res => {
        if (res.code === 0) {
          modal.msgSuccess('注销账号成功！')
          uni.reLaunch({
            url: '/page_mine/login'
          })
        }
      })
    })
  }

	onMounted(() => {
		eventChannel.on('params', function(data) {
			if (data) {
        dataParam.value = data
			}
		})
	})
</script>

<style scope lang="scss">
.layout-mine {
  background-color: #f1f1f1;
  height: calc(100vh - var(--window-bottom));
  overflow: auto;
  padding: 20rpx;

  .layout-mine-essential {
    background-color: #fff;
    border-radius: 5px;
    margin-bottom: 10rpx;

    :deep(.u-cell__body) {
      height: 120rpx;
    }

    .custom-cell-title {
      display: flex;

      .custom-cell-title-item {
        margin-left: 26rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}
</style>