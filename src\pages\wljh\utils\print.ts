import modal from "@/plugins/modal";
import customStorage from "@/utils/customStorage";
import {getCpclDataBuffer} from "@/utils/print/printCode";
import {ref} from "vue";


// 打印所用到的参数
const maxChunk = ref(20)
const deviceId = ref() // 蓝牙设备 id
const serviceId = ref() // 蓝牙特征值对应服务的 uuid
const characteristic = ref({ // 蓝牙特征值的 uuid
    notifyId: undefined,
    writeId: undefined
})

/**
 * CPCL指令触发打印
 * @param dataArr CPCL指令列表
 */
export function printCpcl(dataArr: any) {
    // console.log(dataArr)
    uni.showLoading({
        title: '打印中...',
        mask: true
    })
    // 调用打印机
    const blueInfo = customStorage.get("BlueInfo")
    const wifiInfo = customStorage.get("WifiInfo")
    if (!blueInfo && !wifiInfo) {
        uni.hideLoading()
        modal.msgError("请先设置打印机配置")
    } else {
        if (blueInfo) {
            maxChunk.value = 20
            uni.openBluetoothAdapter({
                success(res) {
                    let printCode = getCpclDataBuffer(dataArr)

                    deviceId.value = blueInfo.deviceId
                    conneBlue(printCode)
                },
                fail(err) {
                    uni.hideLoading()
                    modal.msgError("请先打开蓝牙")
                }
            })
        } else if (wifiInfo) {
            let printCode = getCpclDataBuffer(dataArr)
            sendWifiData(wifiInfo, printCode)
        }
    }
    setTimeout(() => {
        try {
            uni.hideLoading()
            console.log('强制关闭弹窗')
        } catch (e) {
            console.log('弹窗已关闭')
        }
    }, 10000)
}

// 连接低功耗蓝牙设备
const conneBlue = (bufferQueue) => {
    uni.createBLEConnection({
        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
        deviceId: deviceId.value,
        success: (res) => {
            getBlueService(bufferQueue)
        },
        fail: (err) => {
            if (err.errCode === -1) {
                getBlueService(bufferQueue)
            }
            console.log("连接蓝牙设备失败", err)
        },

    })
}
// 获取蓝牙设备所有服务
const getBlueService = (bufferQueue) => {
    uni.getBLEDeviceServices({
        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
        deviceId: deviceId.value,
        success(res) {
            console.log("获取服务", JSON.stringify(res.services))
            // serviceId.value = res.services[0].uuid
            // getBlueCharacteristic(bufferQueue, deviceId.value, serviceId.value)

            let serviceList = res.services
            for (let j = 0; j < serviceList.length; j++) {
                let currentServiceId = serviceList[j].uuid
                // 逐一获取特征值
                uni.getBLEDeviceCharacteristics({
                    // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
                    deviceId: deviceId.value,
                    // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
                    serviceId: currentServiceId,
                    success(res1) {
                        try {
                            console.log("获取特征值", currentServiceId)
                            console.log(JSON.stringify(res1.characteristics))
                            for (let i = 0; i < res1.characteristics.length; i++) {
                                if (!(serviceId.value && characteristic.value.writeId)) {
                                    let model = res1.characteristics[i]
                                    if (model.properties.write) {
                                        serviceId.value = currentServiceId
                                        characteristic.value.writeId = model.uuid;
                                    }
                                    if (model.properties.notify) {
                                        characteristic.value.notifyId = model.uuid
                                    }
                                }
                            }
                        } catch (e) {
                            console.log(e)
                        }
                    },
                    fail(err) {
                        console.log("获取特征值失败", err)
                        uni.hideLoading()
                        modal.msgError("请检查蓝牙打印机是否配对成功")
                    }
                })
                if (serviceId.value && characteristic.value.writeId) {
                    break;
                }
            }

            // 等获取到write特征值了再发送数据
            setTimeout(() => {
                if (serviceId.value && characteristic.value.writeId) {
                    // 写数据
                    sendBlueData(bufferQueue, deviceId.value, serviceId.value, characteristic.value.writeId)
                } else {
                    try {
                        uni.hideLoading()
                        modal.msgError("打印机连接异常，打印失败")
                    } catch (e) {
                        console.log("弹窗已关闭")
                    }
                }
            }, 1000)

            // uni.setBLEMTU({
            // 	deviceId: deviceId.value,
            // 	mtu: customStorage.get("BlueInfo").chunkSize,
            // 	success(res){
            // 		console.log('设置MTU完成', res)
            // 	},
            // 	fail(err){
            // 		console.log("设置失败", err)
            // 	}
            // })

        },
        fail(err) {
            console.log("获取蓝牙设备所有服务失败", err)
            uni.hideLoading()
            modal.msgError("请检查蓝牙打印机是否配对成功")
        }
    })
}
// 获取蓝牙某个服务中所有特征值
const getBlueCharacteristic = (bufferQueue, deviceId, serviceId) => {
    uni.getBLEDeviceCharacteristics({
        // 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
        deviceId: deviceId,
        // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
        serviceId: serviceId,
        success(res) {
            console.log("获取特征值", JSON.stringify(res.characteristics))
            for (let i = 0; i < res.characteristics.length; i++) {
                let model = res.characteristics[i]
                if (model.properties.write) {
                    characteristic.value.writeId = model.uuid;
                }
                if (model.properties.notify) {
                    characteristic.value.notifyId = model.uuid
                }
            }
            setTimeout(() => {
                sendBlueData(bufferQueue, deviceId, serviceId, characteristic.value.writeId) //写数据
            }, 1000)
        },
        fail(err) {
            console.log("获取特征值失败", err)
            uni.hideLoading()
            modal.msgError("请检查蓝牙打印机是否配对成功")
        }
    })
}
// 关闭连接
const closeBlue = () => {
    uni.closeBLEConnection({
        deviceId: deviceId.value,
        success(res) {
            console.log(' 断开连接成功', res)
        },
        fail(err) {
            console.log(' 断开连接失败', err)
            //uni.hideLoading()
        }
    })
}
// 蓝牙发送数据模板
const sendBlueData = (bufferQueue, deviceId, serviceId, writeId) => {
    sendBlueWrite(bufferQueue, deviceId, serviceId, writeId)
}
// 蓝牙发送
function sendBlueWrite(bufferQueue, deviceId, serviceId, writeId) {
    console.log("蓝牙发送数据", deviceId, serviceId, writeId)
    // 1.并行调用多次会存在写失败的可能性
    // 2.建议每次写入不超过20字节
    // const maxChunk = 20
    console.log('蓝牙分片大小', maxChunk)
    //const length = bufferQueue.length
    const length = bufferQueue.byteLength
    for (let i = 0, j = 0; i < length; i += maxChunk.value, j++) {
        const subPackage = bufferQueue.slice(i, i + maxChunk.value <= length ? i + maxChunk.value : length)
        //let uint8Array = new Uint8Array(subPackage).buffer
        //console.log("打印数据", subPackage)
        writeBLECharacteristicValue(subPackage, deviceId, serviceId, writeId)
    }
    setTimeout(() => {
        try {
            uni.hideLoading()
            modal.msgSuccess("打印结束")
        } catch (e) {
            console.log("弹窗已关闭")
        } finally {
            /*uni.closeBLEConnection({
              deviceId: deviceId.value,
              success(res) {
                console.log(' 断开连接成功', res)
              },
              fail(err) {
                console.log(' 断开连接失败', err)
                //uni.hideLoading()
              }
            })*/
        }
    }, 3000)
}
// 调用蓝牙接口
const writeBLECharacteristicValue = (buffer, deviceId, serviceId, characteristicId) => {
    uni.writeBLECharacteristicValue({
        // 这里的 deviceId 需要在 getBluetoothDevices 或 onBluetoothDeviceFound 接口中获取
        deviceId: deviceId,
        // 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
        serviceId: serviceId,
        // 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
        characteristicId: characteristicId,
        // 这里的value是ArrayBuffer类型
        value: buffer,
        success(res) {
            //console.log('writeBLECharacteristicValue success', res)
        },
        fail(err) {
            console.log("打印异常", err)
            closeBlue(deviceId.value)
        },
        complete(res) {
            //console.log("打印完成", res)
        }
    })
}

// wifi发送数据模板
const sendWifiData = (wifiInfo, bufferQueue) => {
    sendWifiSocket(wifiInfo, bufferQueue)
}
// 连接TCP打印机
const sendWifiSocket = (wifiInfo, bufferQueue) => {
    var buffer = new Uint8Array(bufferQueue)
    const socket = wx.createTCPSocket()
    // 创建连接
    socket.connect({
        address: wifiInfo.IP,
        port: parseInt(wifiInfo.PRNPORT)
    })
    socket.onConnect((res) => {
        socket.write(buffer)
        socket.write(hexStringToArrayBuffer('1B69')) // 切纸命令
        //uni.hideLoading()
        socket.close()

    })

    socket.onMessage((res) => {
        console.log("监听当接收到数据的时触发该事件", res)
    })

    socket.onClose(() => {
        console.log("关闭连接")
        uni.hideLoading()
        modal.msgSuccess("打印结束")
    })

    socket.onError((err) => {
        console.log('连接异常' + err.errMsg)
        uni.hideLoading()
    })
}
// 切纸指令数据转换
function hexStringToArrayBuffer(hex) {
    const typedArray = new Uint8Array(hex.match(/[\da-f]{2}/gi).map((h) => parseInt(h, 16)));
    return typedArray.buffer;
}


