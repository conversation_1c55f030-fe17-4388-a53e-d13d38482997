<template>
	<view class="normal-login-container">
		<view class="logo-content align-center justify-center flex">
			<image style="width: 300rpx;height: 300rpx;" :src="globalConfig.appInfo.logo" mode="scaleToFill"></image>
		</view>
		<view class="logo-content align-center justify-center flex" style="padding-top: 25rpx;">
			<text class="title" style="color: #3281c6;">发货更省心！</text>
		</view>
    <view style="padding: 10rpx 80rpx">
      <up-line color="#3281c6" :hairline="false"></up-line>
    </view>
		<view class="login-form-content">
			<view class="input-item align-center">
        <view class="input-with-button">
          <uni-easyinput type="idcard"  v-model="loginForm.account" placeholder="请输入手机号"
                         :inputBorder="false" placeholderStyle="font-size: 15px"
          ></uni-easyinput>
          <up-button class="get-phone-btn" @click="handleGetPhone"
                     :customStyle="{backgroundColor: '#ffffff', color: '#3281c6', border: '1px solid #3281c6', fontSize: '12px', height: '30px', padding: '0 8px', minWidth: '80px'}">
            获取手机号
          </up-button>
        </view>
        <up-line color="#e7e7e7" :hairline="false"></up-line>
      </view>
			<view class="input-item align-center">
        <uni-easyinput type="password" v-model="loginForm.password" placeholder="请输入密码"
                       :inputBorder="false" placeholderStyle="font-size: 15px" :passwordIcon="false"
        ></uni-easyinput>
        <up-line color="#e7e7e7" :hairline="false"></up-line>
			</view>
			<view style="padding-top: 20rpx;">
        <up-row>
          <up-col :span="9">
            <up-checkbox label="记住手机号" name="remember" usedAlone v-model:checked="loginForm.isRemember"
                         size="15" :customStyle="{fontWeight: 'bold'}"
            ></up-checkbox>
          </up-col>
          <up-col :span="3">
<!--            <view style="text-align: right;color: #606266;font-size: 15px;" @click="handleForgetPassword">忘记密码</view>-->
          </up-col>
        </up-row>

      </view>
			<view class="action-btn">
				<up-button type="primary" :loading="loginLoading" loadingText="登录中" @click="handleLogin" shape="circle"
					:customStyle="loginBtn" :disabled="!aloneChecked">登录</up-button>
			</view>
		</view>

		<view class="xieyi">
			<up-checkbox label="我已阅读并同意" name="agree" usedAlone v-model:checked="aloneChecked"
                   shape="circle" size="15">
			</up-checkbox>
			<text @click="handleUserAgrement" style="color: #02a7f0;">《用户协议》、</text>
			<text @click="handlePrivacy" style="color: #02a7f0;">《隐私政策》</text>
		</view>
		<view class="text-center" style="padding: 0 80rpx;margin-top: 4px;font-size: 15px;">
      <up-row>
        <up-col :span="9">
          <text style="color: #606266;">版本号：{{ appVersion }}</text>
        </up-col>
        <up-col :span="3">
          <view style="text-align: right;color: #606266;font-size: 15px;" @click="handleRegister">注册</view>
        </up-col>
      </up-row>
		</view>

    <view class="custom-footer">
      <view style="text-align: center;padding: 0 0 10rpx 0;">
        多多物流超市加盟热线:&nbsp;
        <span style="color: #02a7f0;text-decoration: underline;"
              @click="makeCall({type:'phone',value:'17786356789'})">
          17786356789
        </span>
      </view>
      <up-line color="#000000" :hairline="false"></up-line>
      <view style="text-align: center;padding: 10rpx 0 20rpx 0;">
        行业变革势不可挡，携手大驰抢占先机
      </view>
    </view>

    <Verify @success="pwdLogin" :mode="'pop'" :captchaType="'blockPuzzle'" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>

  </view>
<!--  <view class="login-bottom"><view class="login-bottom-version">版本号：{{ appVersion }}</view></view>-->
</template>

<script setup>
	import modal from '@/plugins/modal'
	import {
    onMounted,
		reactive,
		ref
	} from "vue";
	import config from '@/config.js'
	import useUserStore from '@/store/modules/user'
  import Verify from "./components/verifition/Verify.vue"
  import { getSysConf } from "@/api/wljh/home"

  const verify = ref(null)

	const aloneChecked = ref(false)
	const globalConfig = ref(config);
	const loginForm = ref({ //登录参数
    account: uni.getStorageSync("loginFormData").account,
    password: uni.getStorageSync("loginFormData").password,
    isRemember: uni.getStorageSync("loginFormData").isRemember,
		socialCode: undefined
	});
	const loginLoading = ref(false) // 登录加载状态
	const loginBtn = reactive({ // 登录按钮样式
		marginTop: '10px',
		height: '35px'
	})

  // 小程序版本号
  const appVersion = ref('')

  // 记录登录失败次数
  const failNum = ref(0)

  // 系统是否开启小程序登录验证码
  const verifyConf = ref('0')

	// 登录事件
	async function handleLogin() {
		if (!loginForm.value.account) {
			modal.msgError("请输入您的账号")
		} else if (!loginForm.value.password) {
			modal.msgError("请输入您的密码")
		} else {
      if (verifyConf.value === '1') {
        // verify.value.refresh()
        verify.value.show()
      } else {
        loginLoading.value = true
        await pwdLogin()
      }
      /*console.log('登录失败次数: ', failNum.value)
      if (verifyConf.value === '1' && failNum.value >=3 && verify.value) {
        // 开启了验证码，且失败次数达到3次，唤起验证码
        // verify.value.refresh()
        verify.value.show()
      } else {
        loginLoading.value = true
        pwdLogin()
      }*/
		}
	}
	// 密码登录
	async function pwdLogin() {
    // loginSuccess()

    const userStore = useUserStore()
		userStore.login(loginForm.value).then((res) => {
      failNum.value = 0
			loginSuccess()
		}).catch(() => {
			loginLoading.value = false
      failNum.value ++
      if (verify.value) {
        verify.value.hide()
      }

		})
	};
	// 登录成功
	function loginSuccess(result) {
    failNum.value = 0
		loginLoading.value = false
    if (loginForm.value.isRemember) {
      // 记住密码
      uni.setStorageSync("loginFormData", loginForm.value)
    } else {
      // 只记住账号
      let loginFormData = loginForm.value
      loginFormData.password = ''
      uni.setStorageSync("loginFormData", loginFormData)
    }
		// 跳转到首页
		uni.switchTab({
			url: '/pages/wljh/home'
		})
	}
	// 隐私协议
	function handlePrivacy() {
		/* let site = globalConfig.value.appInfo.agreements[0];
		uni.navigateTo({
			url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
		}); */
		uni.navigateTo({
			url: `/page_word/userPrivacy`
		});
	};
	// 用户协议
	function handleUserAgrement() {
		/* let site = globalConfig.value.appInfo.agreements[1]
		uni.navigateTo({
			url: `/pages/common/webview/index?title=${site.title}&url=${site.url}`
		}); */
		uni.navigateTo({
			url: `/page_word/userService`
		});
	};

  // 忘记密码
	function handleForgetPassword() {
		uni.navigateTo({
			url: '/page_mine/findPassword',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('parentParam', {
          account: loginForm.value.account
        })
      }
		});
	}

  function handleRegister() {
    uni.navigateTo({
      url: '/page_mine/register',
      success: function(res) {
        // 通过eventChannel向被打开页面传送数据
        res.eventChannel.emit('parentParam', {
          account: loginForm.value.account,
          appVersion: appVersion.value
        })
      }
    });
  }

  // 点击手机号拨打电话
  const makeCall = (phoneContent) => {
    if (phoneContent.type === 'phone') {
      console.log('拨打电话', phoneContent.value)
      uni.makePhoneCall({
        phoneNumber: phoneContent.value
      })
    }
  }

  // 获取手机号
  const handleGetPhone = () => {
    uni.getPhoneNumber({
      success: (res) => {
        console.log('获取手机号成功', res)
        // 这里可以处理获取到的手机号
        if (res.phoneNumber) {
          loginForm.value.account = res.phoneNumber
          modal.msgSuccess('手机号获取成功')
        }
      },
      fail: (err) => {
        console.log('获取手机号失败', err)
        modal.msgError('获取手机号失败，请手动输入')
      }
    })
  }

  onMounted(() => {
    try {
      getSysConf('app.login.verify').then(res => {
        // console.log(res)
        verifyConf.value = res.data
      })
    } catch (e) {
      verifyConf.value = '0'
    }
    try {
      getSysConf('app.version').then(res => {
        console.log('版本号: ', res.data)
        appVersion.value = res.data
      })
    } catch (e) {
      verifyConf.value = '0'
    }
  })

</script>

<style lang="scss">
	page {
		background-color: #ffffff;
	}

	.normal-login-container {
		width: 100%;

		.logo-content {
			width: 100%;
			font-size: 18px;
			text-align: center;
			//padding-top: 20rpx;

			image {
				border-radius: 4px;
			}

			.title {
				margin-left: 10px;
			}
		}

		.login-form-content {
			text-align: center;
			margin: 10px auto;
			//margin-top: 0rpx;
			width: 80%;

			.input-item {
				margin: 20px 0 0 0;
				//background-color: #f5f6f7;
				height: 45px;
				border-radius: 20px;

				.icon {
					font-size: 38rpx;
					margin-left: 10px;
					color: #999;
				}

				.input {
					width: 100%;
					font-size: 14px;
					line-height: 20px;
					text-align: left;
					padding-left: 15px;
				}

				.input-with-button {
					display: flex;
					align-items: center;
					gap: 8px;

					:deep(.uni-easyinput) {
						flex: 1;
					}

					.get-phone-btn {
						flex-shrink: 0;
						width: auto;
					}
				}

			}

			.login-btn {
				margin-top: 10px;
				height: 45px;
			}

			.login-code {
				height: 38px;
				float: right;

				.login-code-img {
					height: 38px;
					position: absolute;
					margin-left: 10px;
					width: 200rpx;
				}
			}

			.action-btn {
        margin-top: 40rpx;

				:deep(.u-button--circle) {
					border-radius: 5px;
				}

				:deep(.u-button--primary) {
					background-color: #3281c6;
					border-color: #3281c6;
				}
			}

			:deep(.u-input--radius, .u-input--square) {
				border-radius: 20px;
				height: 45px;
				border: 0;
			}
		}
	}
  .login-bottom {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: right;

    .login-bottom-version {
      padding: 0rpx 20rpx;
      color: #606266;
    }
  }
  .uni-easyinput {
    :deep(.uni-easyinput__content-input) {
      padding-left: 0!important;
    }
  }

  .xieyi {
    color: #333;
    margin-top: 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 记住手机号粗体样式
  :deep(.u-checkbox__label) {
    font-weight: bold;
  }
  .custom-footer {
    height: 150rpx;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 80rpx;
    //display: flex;
    gap: 60rpx;
    font-size: 17px;
    //justify-content: center;
    //align-items: center;

    :deep(.u-button) {
      height: 70rpx;
    }

    :deep(.u-button--error){
      background-color: #d81e06;
    }

    :deep(.u-button--plain.u-button--info){
      color:#000;
    }
  }
</style>