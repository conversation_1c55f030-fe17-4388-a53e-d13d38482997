<template>
	<view class="layout-sub-addressBookForm">
		<view class="custom-body">
			<view class="custom-body-form">
				<up-form labelPosition="left" ref="formRef" :model="formData" labelWidth="auto">
					<up-form-item label="姓名" prop="name" borderBottom="true">
						<up-input v-model="formData.name" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item label="手机号" prop="mobile" borderBottom="true">
						<up-input v-model="formData.mobile" type="idcard" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item label="身份证号" prop="idCard" borderBottom="true">
						<up-input v-model="formData.idCard" type="idcard" border="none" inputAlign="right"></up-input>
					</up-form-item>
          <!-- 级联地址 -->
					<up-form-item label="地址" prop="addressOne" borderBottom="true">
            <view class="addressRow">
              <up-row>
                <up-col :span="4">
                  <uni-data-select v-model="formData.addressOne" placeholder="省" :localdata="address1Arr" @change="addressChange(1, $event)"></uni-data-select>
                </up-col>
                <up-col :span="4">
                  <uni-data-select v-model="formData.addressTwo" placeholder="市" :localdata="address2Arr" @change="addressChange(2, $event)"></uni-data-select>
                </up-col>
                <up-col :span="4">
                  <uni-data-select v-model="formData.addressThree" placeholder="区/县" :localdata="address3Arr" @change="addressChange(3, $event)"></uni-data-select>
                </up-col>
              </up-row>
            </view>
            <view class="addressRow">
              <up-row>
                <up-col :span="6">
                  <uni-data-select v-model="formData.addressFour" placeholder="乡/镇" :localdata="address4Arr" @change="addressChange(4, $event)"></uni-data-select>
                </up-col>
                <up-col :span="6">
                  <uni-data-select v-model="formData.addressFive" placeholder="村/社区" :localdata="address5Arr" @change="addressChange(5, $event)"></uni-data-select>
                </up-col>
              </up-row>
            </view>
          </up-form-item>
					<up-form-item label="详细地址" prop="detailAddress" borderBottom="true">
						<up-input v-model="formData.detailAddress" border="none" inputAlign="right"></up-input>
					</up-form-item>
					<up-form-item label="公司名称" prop="companyName">
						<up-input v-model="formData.companyName" border="none" inputAlign="right"></up-input>
					</up-form-item>
				</up-form>
			</view>
		</view>
		<view class="custom-footer">
			<view style="width: 100%;padding: 20rpx;">
				<up-button type="primary" shape="circle" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		getCurrentInstance,
		ref,reactive
	} from 'vue'
	import {
    levelSearchAddress
	} from '@/api/wljh/home'
	import modal from '@/plugins/modal'

	const instance = getCurrentInstance().proxy
	const eventChannel = instance.getOpenerEventChannel()

	const formType = ref() // 表单类型
	const formData = ref({ // 表单数据
		id: undefined,
		name: undefined,
		mobile: undefined,
    idCard: undefined,
		detailAddress: undefined,
    addressOne: undefined,
    addressTwo: undefined,
    addressThree: undefined,
    addressFour: undefined,
    addressFive: undefined,
		companyName: undefined,
	})
  const address1Arr = ref([])
  const address2Arr = ref([])
  const address3Arr = ref([])
  const address4Arr = ref([])
  const address5Arr = ref([])

	// 提交信息
	const handleSubmit = async () => {
    let data = {...formData.value}
    if (data.addressOne && data.addressOne.name) data.addressOne = data.addressOne.name
    if (data.addressTwo && data.addressTwo.name) data.addressTwo = data.addressTwo.name
    if (data.addressThree && data.addressThree.name) data.addressThree = data.addressThree.name
    if (data.addressFour && data.addressFour.name) data.addressFour = data.addressFour.name
    if (data.addressFive && data.addressFive.name) data.addressFive = data.addressFive.name
    modal.msgSuccess(formType.value === 'create' ? '添加成功' : '修改成功')
    console.log(formType.value, data)
    // 父页面发送数据
    if (formType.value === 'create') {
      data.id = Date.now()
      eventChannel.emit('childFn', data);
    } else {
      eventChannel.emit('childFn', undefined);
    }
    await uni.navigateBack()
	}
	// 初始化页面
	const initPage = (obj) => {
		// 动态改变顶部标题
		uni.setNavigationBarTitle({
			title: obj.type === 'create' ? '新增地址' : '编辑地址'
		});
    formType.value = obj.type
		if (obj.data) {
			formData.value = {...obj.data}
      // 地址回显
      if (formData.value.addressOne) {
        const addressValue = {no: '', name: formData.value.addressOne}
        address1Arr.value = [{value: addressValue, text: formData.value.addressOne}]
        formData.value.addressOne = addressValue
      }
      if (formData.value.addressTwo) {
        const addressValue = {no: '', name: formData.value.addressTwo}
        address2Arr.value = [{value: addressValue, text: formData.value.addressTwo}]
        formData.value.addressTwo = addressValue
      }
      if (formData.value.addressThree) {
        const addressValue = {no: '', name: formData.value.addressThree}
        address3Arr.value = [{value: addressValue, text: formData.value.addressThree}]
        formData.value.addressThree = addressValue
      }
      if (formData.value.addressFour) {
        const addressValue = {no: '', name: formData.value.addressFour}
        address4Arr.value = [{value: addressValue, text: formData.value.addressFour}]
        formData.value.addressFour = addressValue
      }
      if (formData.value.addressFive) {
        const addressValue = {no: '', name: formData.value.addressFive}
        address5Arr.value = [{value: addressValue, text: formData.value.addressFive}]
        formData.value.addressFive = addressValue
      }
		}
	}


  /** 地址变化 */
  const addressChange = (level, value) => {
    if (level <= 4) {
      address5Arr.value = []
      formData.value.addressFive = undefined
    }
    if (level <= 3) {
      address4Arr.value = []
      formData.value.addressFour = undefined
    }
    if (level <= 2) {
      address3Arr.value = []
      formData.value.addressThree = undefined
    }
    if (level === 1) {
      address2Arr.value = []
      formData.value.addressTwo = undefined
    }
    // 加载下级地址
    if (level >= 1 && level < 5 && value) {
      if (value.no) {
        loadLevelAddress(level + 1, value.no)
      }
    }

  }

  /** 加载指定层级地址数据 */
  const loadLevelAddress = async (level, parentNo) => {
    await levelSearchAddress(level, parentNo).then(res => {
      const newAddress = res.data.map(item => {
        return {
          text: item.name,
          value: item
        }
      })
      if (level === 1) {
        address1Arr.value = newAddress
        // 回显一级地址
        if (formData.value.addressOne) {
          address1Arr.value.forEach(item => {
            if (item.value.name === formData.value.addressOne.name || item.value.name === formData.value.addressOne) {
              formData.value.addressOne = item.value
              loadLevelAddress(2, formData.value.addressOne.no)
            }
          })
        }
      } else if (level === 2) {
        address2Arr.value = newAddress
        // 回显二级地址
        if (formData.value.addressTwo) {
          address2Arr.value.forEach(item => {
            if (item.value.name === formData.value.addressTwo.name || item.value.name === formData.value.addressTwo) {
              formData.value.addressTwo = item.value
              loadLevelAddress(3, formData.value.addressTwo.no)
            }
          })
        }
      } else if (level === 3) {
        address3Arr.value = newAddress
        // 回显三级地址
        if (formData.value.addressThree) {
          address3Arr.value.forEach(item => {
            if (item.value.name === formData.value.addressThree.name || item.value.name === formData.value.addressThree) {
              formData.value.addressThree = item.value
              loadLevelAddress(4, formData.value.addressThree.no)
            }
          })
        }
      } else if (level === 4) {
        address4Arr.value = newAddress
        // 回显四级地址
        if (formData.value.addressFour) {
          address4Arr.value.forEach(item => {
            if (item.value.name === formData.value.addressFour.name || item.value.name === formData.value.addressFour) {
              formData.value.addressFour = item.value
              loadLevelAddress(5, formData.value.addressFour.no)
            }
          })
        }
      } else if (level === 5) {
        address5Arr.value = newAddress
        // 回显五级地址
        if (formData.value.addressFive) {
          address5Arr.value.forEach(item => {
            if (item.value.name === formData.value.addressFive.name || item.value.name === formData.value.addressFive) {
              formData.value.addressFive = item.value
            }
          })
        }
      }

    })
  }

  onMounted(() => {
		eventChannel.on('parentParam', function(data) {
			initPage(data)
		})

    try {
      // 加载一级地址数据
      loadLevelAddress(1, '')
    } catch (e) {
      address1Arr.value = []
    }
	})
</script>

<style scoped lang="scss">
	.layout-sub-addressBookForm {
		background-color: #f1f1f1;
		height: calc(100vh - var(--window-top));
		overflow: hidden;
		padding: 0 20rpx;

		.custom-body {
			height: calc(100% - 100rpx);
			overflow: auto;

			.custom-body-form {
				background-color: #fff;
				margin: 20rpx 0;
				padding: 0 20rpx;
				border-radius: 10rpx;
				//padding-bottom: 30rpx;
				
				:deep(.u-form-item__body__left__content__label){
					color: #606266;
				}
        :deep(.u-form-item__body__right__content__slot) {
          display: block;
        }
			}
		}

		.custom-footer {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100rpx;
			background-color: #fff;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			
			:deep(.u-button--circle){
				border-radius: 20px;
			}
			
			:deep(.u-button--primary){
				background-color: #d81e06;
				border-color: #d81e06;
			}
		}
	}

  .addressRow {
    display: block;

    :deep(.uni-select__selector-scroll) {
      max-height: 50vh;
    }
    :deep(.uni-select__selector-item) {
      text-align: left;
      border-bottom: 1px solid #f4f4f5;
      line-height: 15px;
      padding: 10px 10px;
    }
    :deep(.uni-scroll-view) {
      //width: max-content;
    }
    :deep(.uniui-clear) {
      //position: absolute;
      //right: 0;
    }
    :deep(.uni-select__input-box) {
      width: 100%;
    }
    :deep(.uni-select__input-text) {
      white-space: unset;
    }
  }
</style>