<template>
	<view class="layout-sub-goodsSave">
		<view class="custom-body">

      <view class="upload-box">
        <view class="upload-box-title">
          <view class="upload-box-title-icon"></view>
          <view>申请原因</view>
        </view>
        <view class="upload-box-btn">
          <textarea v-model="deleteFormData.applyReason" placeholder="请输入申请原因"/>
        </view>
        <up-line color="#d6d7d9"></up-line>
      </view>
			<view class="custom-btn">
				<up-button type="primary" text="确定" @click="handleSubmit"></up-button>
			</view>
		</view>
	</view>
</template>

<script setup>
import {
	reactive,
	getCurrentInstance,
	ref,
	onMounted
} from 'vue'
import modal from '@/plugins/modal'
import {createOrderDeleteApply} from '@/pages/wljh/sub_pages/api/sub_page'

const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const deleteFormData = ref({
  logisticsNetworkId: undefined,
  orderCode: undefined,
  waybillCode: undefined,
  applyReason: undefined,
})

// 确定事件
const handleSubmit = async () => {
	if (!deleteFormData.value.applyReason) {
		setTimeout(() => {
			modal.msgError("请输入申请原因")
		}, 500)
		return false
	} else {
    await uni.showLoading({
      title: '正在提交',
      mask: true
    })
    console.log(deleteFormData.value)
    try {
      await createOrderDeleteApply(deleteFormData.value).then(res => {
        uni.hideLoading()
        modal.msgSuccess('操作成功')
        uni.navigateBack()
      })
    } catch(e){
      console.log("提交失败", e)
    } finally {
      try {
      uni.hideLoading()
      } catch (e) {}
    }
	}
}

onMounted(() => {
	// 接收数据
	eventChannel.on('parentPageData', function (data) {
    deleteFormData.value = {
      logisticsNetworkId: data.logisticsNetworkId,
      orderCode: data.orderCode,
      waybillCode: data.waybillCode,
      applyReason: undefined,
    }
	})
})
</script>

<style scoped lang="scss">
.layout-sub-goodsSave {
	background-color: #f1f1f1;
	height: calc(100vh - var(--window-top));
	overflow: auto;
	padding: 0 20rpx;

	.custom-body {
		background-color: #fff;
		padding: 0 20rpx;
		padding-bottom: 20rpx;
		margin: 20rpx 0;
		border-radius: 5px;

		:deep(.u-form-item__body__right__content__slot) {
			padding-right: 50rpx;
		}

		:deep(.u-number-box) {
			padding: 0 60rpx;
		}

		:deep(.u-number-box__input) {
			width: auto !important;
		}

		:deep(.u-form-item__body__left__content__label) {
			color: #606266;
		}

		.custom-body-form-row {
			.custom-body-form-item {
				display: flex;
				width: 100%;
				flex-direction: column;
				gap: 20rpx;

				.total-volume-item-input {
					width: 89%;
					height: 60rpx;
					margin-left: auto;
				}

				.custom-body-form-item-line {
					display: flex;

				}

				.custom-body-form-item-input {
					display: flex;
					flex-wrap: nowrap;
					gap: 5px;
					// margin-left: 30px;
					height: 30px;
				}
			}
		}

		.custom-btn {
			margin-top: 20rpx;

			:deep(.u-button--square) {
				border-radius: 20px;
			}

			:deep(.u-button--primary) {
				background-color: #d81e06;
				border: #d81e06;
			}

			:deep(.u-button) {
				height: 45px;
			}
		}

		// :deep(.u-scroll-list) {
		// 	width: 100%;
		// }

		.custom_line {
			display: flex;
			height: 60px;
			align-items: center;
			.custom-card-body-item-lable {
				font-size: 16px;
				color: #606266;
				width: 150rpx;
				// margin-right: 6rpx;
			}

			.custom-card-body-item-tag {
				// padding-left: 120rpx;
				width: 50px;
				height: 100%;
				display: flex;
				align-items: center;
        margin-right: 20rpx;

				:deep(.u-tag__text--mini) {
					font-size: 18px;
				}
			}
		}


	}


	:deep(.u-scroll-list) {
		width: calc(100% - 220rpx);
		padding-bottom: 0;
	}


  :deep(.u-tag--mini) {
    height: 43px;
    line-height: 43px;
    padding: 0 15px;
    min-width: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.u-tag--primary) {
    background-color: #d81e06;
  }
}
.upload-box {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .upload-box-title {
    display: flex;
    align-items: center;
    font-size: 15px;
  }

  .upload-box-title-icon {
    height: 15px;
    width: 6rpx;
    background-color: #d81e06;
    margin-right: 20rpx;
  }

  .upload-box-btn {
    margin: 10px 0;

    :deep(.u-upload) {
      align-items: center;
    }
  }
}
</style>